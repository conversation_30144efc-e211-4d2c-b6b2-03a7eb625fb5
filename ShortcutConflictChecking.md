# Shortcut Conflict Checking in Rectangle

This document explains how <PERSON><PERSON><PERSON><PERSON> implements shortcut conflict checking, both globally (with system shortcuts) and locally (within the application).

## Overview

Rectangle uses the MASShortcut library to handle keyboard shortcuts and conflict detection. The application provides users with the ability to either enforce shortcut conflict checking or bypass it, giving flexibility for different user preferences.

## Global Shortcut Conflict Checking

### MASShortcutValidator

The core of Rectangle's global shortcut conflict checking is the `MASShortcutValidator` class from the MASShortcut library. This class is responsible for checking if shortcuts conflict with system shortcuts or other applications.

### PassthroughShortcutValidator

Rectangle implements a custom subclass of `MASShortcutValidator` called `PassthroughShortcutValidator` that bypasses all conflict checks:

```swift
class PassthroughShortcutValidator: MASShortcutValidator {
    
    override func isShortcutValid(_ shortcut: MASShortcut!) -> Bool {
        return true
    }
    
    override func isShortcutAlreadyTaken(bySystem shortcut: MASShortcut!, explanation: AutoreleasingUnsafeMutablePointer<NSString?>!) -> Bool {
        return false
    }
    
    override func isShortcut(_ shortcut: MASShortcut!, alreadyTakenIn menu: NSMenu!, explanation: AutoreleasingUnsafeMutablePointer<NSString?>!) -> Bool {
        return false
    }
}
```

This class overrides three key methods:
- `isShortcutValid`: Always returns true, allowing any shortcut
- `isShortcutAlreadyTaken(bySystem:)`: Always returns false, ignoring system shortcut conflicts
- `isShortcut(_:alreadyTakenIn:)`: Always returns false, ignoring menu shortcut conflicts

### Defaults.allowAnyShortcut Setting

Rectangle provides a user preference called `allowAnyShortcut` that controls whether shortcut conflict checking is enabled or disabled:

```swift
// In Defaults.swift
static let allowAnyShortcut = BoolDefault(key: "allowAnyShortcut")
```

When this setting is enabled (true), Rectangle uses the `PassthroughShortcutValidator` which bypasses all conflict checks. When disabled (false), Rectangle uses the standard `MASShortcutValidator` which performs conflict checks.

### Toggling Conflict Checking

The `PrefsViewController` subscribes to changes in the `allowAnyShortcut` setting and updates the validator accordingly:

```swift
private func subscribeToAllowAnyShortcutToggle() {
    Notification.Name.allowAnyShortcut.onPost { notification in
        guard let enabled = notification.object as? Bool else { return }
        let validator = enabled ? PassthroughShortcutValidator() : MASShortcutValidator()
        self.actionsToViews.values.forEach { $0.shortcutValidator = validator }
    }
}
```

The user can toggle this setting in the preferences window via a checkbox labeled "Remove keyboard shortcut restrictions":

```swift
@IBAction func toggleAllowAnyShortcut(_ sender: NSButton) {
    let newSetting: Bool = sender.state == .on
    Defaults.allowAnyShortcut.enabled = newSetting
    Notification.Name.allowAnyShortcut.post(object: newSetting)
}
```

### Default Behavior for New Users

For new users, Rectangle sets `allowAnyShortcut` to `true` by default, which means shortcut conflict checking is disabled initially:

```swift
// In AppDelegate.swift - applicationDidFinishLaunching
if let lastVersion = Defaults.lastVersion.value,
   let intLastVersion = Int(lastVersion) {
    // Migration code for existing users
} else {
    Defaults.allowAnyShortcut.enabled = true
}
```

This allows new users to use Rectangle's default shortcuts without worrying about conflicts with other applications.

## Conflicting Applications Detection

Rectangle also checks for other window management applications that might conflict with its functionality:

```swift
func checkForConflictingApps() {
    let conflictingAppsIds: [String: String] = [
        "com.divisiblebyzero.Spectacle": "Spectacle",
        "com.crowdcafe.windowmagnet": "Magnet",
        "com.hegenberg.BetterSnapTool": "BetterSnapTool",
        "com.manytricks.Moom": "Moom"
    ]
    
    let runningApps = NSWorkspace.shared.runningApplications
    for app in runningApps {
        guard let bundleId = app.bundleIdentifier else { continue }
        if let conflictingAppName = conflictingAppsIds[bundleId] {
            AlertUtil.oneButtonAlert(question: "Potential window manager conflict: \(conflictingAppName)", text: "Since \(conflictingAppName) might have some overlapping behavior with Rectangle, it's recommended that you either disable or quit \(conflictingAppName).")
            break
        }
    }
}
```

This method checks if any known window management applications (Spectacle, Magnet, BetterSnapTool, or Moom) are running and alerts the user about potential conflicts.

## macOS Built-in Tiling Conflicts

Rectangle also checks for conflicts with macOS's built-in window tiling features (added in macOS 15 Sequoia):

```swift
static func checkForBuiltInTiling(skipIfAlreadyNotified: Bool) {
    guard #available(macOS 15, *), !Defaults.windowSnapping.userDisabled
    else { return }

    let isStandardTilingConflicting = (tilingByEdgeDrag.enabled || tilingOptionAccelerator.enabled)
    
    let shouldSkipStandardCheck = skipIfAlreadyNotified && Defaults.internalTilingNotified.enabled
    
    if isStandardTilingConflicting && !shouldSkipStandardCheck {
        resolveStandardTilingConflict()
    } else if isTopTilingConflicting {
        resolveTopTilingConflict()
    }
    Defaults.internalTilingNotified.enabled = true
}
```

If both Rectangle and macOS have edge-dragging tiling enabled, Rectangle offers to disable one of them:

```swift
private static func resolveStandardTilingConflict() {
    let result = AlertUtil.threeButtonAlert(
        question: "Conflict with macOS tiling".localized,
        text: "Drag to screen edge tiling is enabled in both Rectangle and macOS.".localized,
        buttonOneText: "Disable in macOS".localized,
        buttonTwoText: "Disable in Rectangle".localized,
        buttonThreeText: "Dismiss".localized)
    switch result {
    case .alertFirstButtonReturn:
        disableMacTiling()
        // Show confirmation and offer to open System Settings
    case .alertSecondButtonReturn:
        Defaults.windowSnapping.enabled = false
        Notification.Name.windowSnapping.post(object: false)
        // Show confirmation and offer to open System Settings
    default:
        break
    }
}
```

## Problematic Applications Detection

Rectangle also identifies applications known to have issues with its drag-to-snap feature:

```swift
func checkForProblematicApps() {
    guard !Defaults.windowSnapping.userDisabled, !Defaults.notifiedOfProblemApps.enabled else { return }
    
    let problemBundleIds: [String] = [
        "com.mathworks.matlab",
        "com.live2d.cubism.CECubismEditorApp",
        "com.aquafold.datastudio.DataStudio",
        "com.adobe.illustrator",
        "com.adobe.AfterEffects"
    ]
    
    // these apps are java based with dynamic bundleIds
    let problemJavaAppNames: [String] = [
        "thinkorswim",
        "Trader Workstation"
    ]
    
    // Implementation continues...
}
```

## Conclusion

Rectangle provides a comprehensive approach to shortcut conflict management:

1. **User Control**: Users can choose to enable or disable shortcut conflict checking based on their preferences.
2. **Default Permissiveness**: For new users, conflict checking is disabled by default to provide a smoother initial experience.
3. **Conflict Detection**: Rectangle checks for conflicts with other window management applications and macOS built-in features.
4. **Problem Application Awareness**: Rectangle identifies applications known to have issues with its features.

This approach gives users flexibility while also providing safeguards against potential conflicts that could affect the user experience.