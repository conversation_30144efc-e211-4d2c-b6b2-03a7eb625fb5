/* Cancel action button in recording state */
"Cancel" = "Cancelar";

/* Tooltip for non-empty shortcut button */
"Click to record new shortcut" = "Haga clic para grabar nuevo atajo";

/* Tooltip for hint button near the non-empty shortcut */
"Delete shortcut" = "Borrar atajo";

/* VoiceOver title */
"keyboard shortcut" = "función rápida de teclado";

/* Alert button when shortcut is already used */
"OK" = "OK";

/* Empty shortcut button in normal state */
"Record Shortcut" = "Grabar Función rápida";

/* VoiceOver: Shortcut cleared */
"Shortcut cleared" = "Función rápida eliminada";

/* VoiceOver: Shortcut set */
"Shortcut set" = "Función rápida creada";

/* Shortcut glyph name for SPACE key */
"Space" = "Espacio";

/* Title for alert when shortcut is already used */
"The key combination %@ cannot be used" = "La combinación de teclas %@ no puede ser utilizada";

/* Message for alert when shortcut is already used by the system */
"This combination cannot be used because it is already used by a system-wide keyboard shortcut.\nIf you really want to use this key combination, most shortcuts can be changed in the Keyboard & Mouse panel in System Preferences." = "Esta combinación no puede ser utilizar debido a que es una función rápida del sistema.\nSi realmente desea utilizar esta combinación de teclas, la mayoría de las funciones rápidas se puede cambiar en el Panel de Teclado en las Preferencias del sistema.";

/* Message for alert when shortcut is already used */
"This shortcut cannot be used because it is already used by the menu item ‘%@’." = "Esta función rápida no se puede utilizar debido a que ya está siendo utilizada por el elemento de menú '%@'.";

/* VoiceOver shortcut help */
"To record a new shortcut, click this button, and then type the new shortcut, or press delete to clear an existing shortcut." = "Para grabar una nueva función rápida, haga clic en este botón, y luego teclee la nueva función rápida, o pulse borrar para quitar una función rápida existente.";

/* Non-empty shortcut button in recording state */
"Type New Shortcut" = "Teclee la nueva función rápida";

/* Empty shortcut button in recording state */
"Type Shortcut" = "Teclee la función rápida";

/* Cancel action button for non-empty shortcut in recording state */
"Use Old Shortcut" = "Usar una función rápida previa";
