﻿/* Cancel action button in recording state */
"Cancel" = "Cancel";

/* Tooltip for non-empty shortcut button */
"Click to record new shortcut" = "Click to record new shortcut";

/* Tooltip for hint button near the non-empty shortcut */
"Delete shortcut" = "Delete shortcut";

/* VoiceOver title */
"keyboard shortcut" = "keyboard shortcut";

/* Alert button when shortcut is already used */
"OK" = "OK";

/* Empty shortcut button in normal state */
"Record Shortcut" = "Record Shortcut";

/* VoiceOver: Shortcut cleared */
"Shortcut cleared" = "Shortcut cleared";

/* VoiceOver: Shortcut set */
"Shortcut set" = "Shortcut set";

/* Shortcut glyph name for SPACE key */
"Space" = "Space";

/* Title for alert when shortcut is already used */
"The key combination %@ cannot be used" = "The key combination %@ cannot be used";

/* Message for alert when shortcut is already used by the system */
"This combination cannot be used because it is already used by a system-wide keyboard shortcut.\nIf you really want to use this key combination, most shortcuts can be changed in the Keyboard & Mouse panel in System Preferences." = "This combination cannot be used because it is already used by a system-wide keyboard shortcut.\nIf you really want to use this key combination, most shortcuts can be changed in the Keyboard & Mouse panel in System Preferences.";

/* Message for alert when shortcut is already used */
"This shortcut cannot be used because it is already used by the menu item ‘%@’." = "This shortcut cannot be used because it is already used by the menu item ‘%@’.";

/* VoiceOver shortcut help */
"To record a new shortcut, click this button, and then type the new shortcut, or press delete to clear an existing shortcut." = "To record a new shortcut, click this button, and then type the new shortcut, or press delete to clear an existing shortcut.";

/* Non-empty shortcut button in recording state */
"Type New Shortcut" = "Type New Shortcut";

/* Empty shortcut button in recording state */
"Type Shortcut" = "Type Shortcut";

/* Cancel action button for non-empty shortcut in recording state */
"Use Old Shortcut" = "Use Old Shortcut";
