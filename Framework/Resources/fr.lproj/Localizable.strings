/* Cancel action button in recording state */
"Cancel" = "Annuler";

/* Tooltip for non-empty shortcut button */
"Click to record new shortcut" = "Cliquez pour enregistrer le raccourci";

/* Tooltip for hint button near the non-empty shortcut */
"Delete shortcut" = "Supprimer le raccourci";

/* VoiceOver title */
"keyboard shortcut" = "raccourci clavier";

/* Alert button when shortcut is already used */
"OK" = "OK";

/* Empty shortcut button in normal state */
"Record Shortcut" = "Enregistrer le raccourci";

/* VoiceOver: Shortcut cleared */
"Shortcut cleared" = "Raccourci supprimé";

/* VoiceOver: Shortcut set */
"Shortcut set" = "Raccourci configuré";

/* Shortcut glyph name for SPACE key */
"Space" = "Espace";

/* Title for alert when shortcut is already used */
"The key combination %@ cannot be used" = "La combinaison %@ ne peut être utilisée";

/* Message for alert when shortcut is already used by the system */
"This combination cannot be used because it is already used by a system-wide keyboard shortcut.\nIf you really want to use this key combination, most shortcuts can be changed in the Keyboard & Mouse panel in System Preferences." = "Cette combinaison de touches ne peut être utilisée parce qu’elle est réservée pour un raccourci du système.\nSi vous désirez l’utiliser, la plupart des raccourcis peuvent être modifiés dans l’onglet Clavier, dans Préférences Système.";

/* Message for alert when shortcut is already used */
"This shortcut cannot be used because it is already used by the menu item ‘%@’." = "Ce raccourci ne peut être utilisé parce qu’il est déjà utilisé par le point de menu «%@».";

/* VoiceOver shortcut help */
"To record a new shortcut, click this button, and then type the new shortcut, or press delete to clear an existing shortcut." = "Pour enregistrer un nouveau raccourci, cliquez sur ce bouton et tapez le nouveau raccourci, ou bien, tapez sur «Supprimer» pour supprimer le raccourci configuré.";

/* Non-empty shortcut button in recording state */
"Type New Shortcut" = "Saisir un raccourci";

/* Empty shortcut button in recording state */
"Type Shortcut" = "Saisir un raccourci";

/* Cancel action button for non-empty shortcut in recording state */
"Use Old Shortcut" = "Revenir au raccourci précédent";
