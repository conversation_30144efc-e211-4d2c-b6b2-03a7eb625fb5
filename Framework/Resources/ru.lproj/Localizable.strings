﻿/* Cancel action button in recording state */
"Cancel" = "Отмена";

/* Tooltip for non-empty shortcut button */
"Click to record new shortcut" = "Нажмите для записи сочетания клавиш";

/* Tooltip for hint button near the non-empty shortcut */
"Delete shortcut" = "Удалить горячую клавишу";

/* VoiceOver title */
"keyboard shortcut" = "сочетание клавиш";

/* Alert button when shortcut is already used */
"OK" = "ОК";

/* Empty shortcut button in normal state */
"Record Shortcut" = "Ввести сочетание";

/* VoiceOver: Shortcut cleared */
"Shortcut cleared" = "Сочетание клавиш удалено";

/* VoiceOver: Shortcut set */
"Shortcut set" = "Сочетание клавиш назначено";

/* Shortcut glyph name for SPACE key */
"Space" = "Пробел";

/* Title for alert when shortcut is already used */
"The key combination %@ cannot be used" = "Нельзя использовать сочетание клавиш %@";

/* Message for alert when shortcut is already used by the system */
"This combination cannot be used because it is already used by a system-wide keyboard shortcut.\nIf you really want to use this key combination, most shortcuts can be changed in the Keyboard & Mouse panel in System Preferences." = "Нельзя использовать это сочетание клавиш, потому что оно уже используется в системе.\n Если вы хотите использовать это сочетание, измените существующее системное сочетание клавиш через панель Клавиатура в Cистемных настройках.";

/* Message for alert when shortcut is already used */
"This shortcut cannot be used because it is already used by the menu item ‘%@’." = "Нельзя использовать это сочетание, потому что оно уже связано с элементом ‘%@’.";

/* VoiceOver shortcut help */
"To record a new shortcut, click this button, and then type the new shortcut, or press delete to clear an existing shortcut." = "Чтобы назначить новое сочетание клавиш, нажмите эту кнопку и введите новое сочетание, или нажмите \"Удалить\", чтобы удалить действующее сочетание клавиш.";

/* Non-empty shortcut button in recording state */
"Type New Shortcut" = "Введите сочетание";

/* Empty shortcut button in recording state */
"Type Shortcut" = "Введите сочетание";

/* Cancel action button for non-empty shortcut in recording state */
"Use Old Shortcut" = "Вернуть старое";