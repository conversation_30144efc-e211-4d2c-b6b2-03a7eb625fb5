﻿/* Cancel action button in recording state */
"Cancel" = "Cancelar";

/* Tooltip for non-empty shortcut button */
"Click to record new shortcut" ="Clique para gravar o atalho";

/* Tooltip for hint button near the non-empty shortcut */
"Delete shortcut" = "Apagar atalho";

/* VoiceOver title */
"keyboard shortcut" = "atalho de teclado";

/* Alert button when shortcut is already used */
"OK" = "OK";

/* Empty shortcut button in normal state */
"Record Shortcut" = "Gravar Atalho";

/* VoiceOver: Shortcut cleared */
"Shortcut cleared" = "Atalho limpo";

/* VoiceOver: Shortcut set */
"Shortcut set" = "Atalho definido";

/* Shortcut glyph name for SPACE key */
"Space" = "Espaço";

/* Title for alert when shortcut is already used */
"The key combination %@ cannot be used" = "A combinação de teclas “%@” não pode ser usada";

/* Message for alert when shortcut is already used by the system */
"This combination cannot be used because it is already used by a system-wide keyboard shortcut.\nIf you really want to use this key combination, most shortcuts can be changed in the Keyboard & Mouse panel in System Preferences." = "Esta combinação não pode ser usada porque ela já <PERSON> usada por um atalho global do sistema.\nA maioria dos atalhos pode ser alterada no painel Teclado das Preferências do Sistema, caso realmente deseje usar esta combinação.";

/* Message for alert when shortcut is already used */
"This shortcut cannot be used because it is already used by the menu item ‘%@’." = "Este atalho não pode ser usado porque ele já é usado pelo item de menu “%@”.";

/* VoiceOver shortcut help */
"To record a new shortcut, click this button, and then type the new shortcut, or press delete to clear an existing shortcut." = "Para gravar um atalho novo, clique neste botão e digite o novo atalho ou pressione apagar para limpar um atalho existente.";

/* Non-empty shortcut button in recording state */
"Type New Shortcut" = "Digite o atalho";

/* Empty shortcut button in recording state */
"Type Shortcut" = "Digite o atalho";

/* Cancel action button for non-empty shortcut in recording state */
"Use Old Shortcut" = "Usar atalho antigo";
