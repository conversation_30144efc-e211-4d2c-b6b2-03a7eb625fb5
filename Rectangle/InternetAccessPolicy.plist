<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ApplicationDescription</key>
	<string>Rectangle is the gold standard for window management on macOS. Free and open source.</string>
	<key>Connections</key>
	<array>
		<dict>
			<key>IsIncoming</key>
			<false/>
			<key>Host</key>
			<string>rectangleapp.com</string>
			<key>NetworkProtocol</key>
			<string>TCP</string>
			<key>Port</key>
			<string>443</string>
			<key>Purpose</key>
			<string>Rectangle checks for new versions</string>
			<key>DenyConsequences</key>
			<string>If you deny this connection, you will not be notified about new versions.</string>
		</dict>
	</array>
</dict>
</plist>
