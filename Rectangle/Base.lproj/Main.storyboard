<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="23504"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Application-->
        <scene sceneID="JPo-4y-FX3">
            <objects>
                <application id="hnw-xV-0zn" sceneMemberID="viewController">
                    <menu key="mainMenu" title="Main Menu" systemMenu="main" id="AYu-sK-qS6">
                        <items>
                            <menuItem title="Rectangle" id="1Xt-HY-uBw">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Rectangle" systemMenu="apple" id="uQy-DD-JDr">
                                    <items>
                                        <menuItem title="About Rectangle" id="5kV-Vb-QxS">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="orderFrontStandardAboutPanel:" target="Ady-hI-5gd" id="Exp-CZ-Vem"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="VOq-y0-SEH"/>
                                        <menuItem title="Preferences…" keyEquivalent="," id="BOF-NM-1cW"/>
                                        <menuItem isSeparatorItem="YES" id="wFC-TO-SCJ"/>
                                        <menuItem title="Services" id="NMo-om-nkz">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <menu key="submenu" title="Services" systemMenu="services" id="hz9-B4-Xy5"/>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="4je-JR-u6R"/>
                                        <menuItem title="Hide Rectangle" keyEquivalent="h" id="Olw-nP-bQN">
                                            <connections>
                                                <action selector="hide:" target="Ady-hI-5gd" id="PnN-Uc-m68"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Hide Others" keyEquivalent="h" id="Vdr-fp-XzO">
                                            <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                            <connections>
                                                <action selector="hideOtherApplications:" target="Ady-hI-5gd" id="VT4-aY-XCT"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Show All" id="Kd2-mp-pUS">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="unhideAllApplications:" target="Ady-hI-5gd" id="Dhg-Le-xox"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="kCx-OE-vgT"/>
                                        <menuItem title="Quit Rectangle" keyEquivalent="q" id="4sb-4s-VLi">
                                            <connections>
                                                <action selector="terminate:" target="Ady-hI-5gd" id="Te7-pn-YzF"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="File" id="dMs-cI-mzQ">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="File" id="bib-Uj-vzu">
                                    <items>
                                        <menuItem title="New" keyEquivalent="n" id="Was-JA-tGl">
                                            <connections>
                                                <action selector="newDocument:" target="Ady-hI-5gd" id="4Si-XN-c54"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Open…" keyEquivalent="o" id="IAo-SY-fd9">
                                            <connections>
                                                <action selector="openDocument:" target="Ady-hI-5gd" id="bVn-NM-KNZ"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Close" keyEquivalent="w" id="DVo-aG-piG">
                                            <connections>
                                                <action selector="performClose:" target="Ady-hI-5gd" id="HmO-Ls-i7Q"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Save…" keyEquivalent="s" id="pxx-59-PXV">
                                            <connections>
                                                <action selector="saveDocument:" target="Ady-hI-5gd" id="teZ-XB-qJY"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="Edit" id="5QF-Oa-p0T">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Edit" id="W48-6f-4Dl">
                                    <items>
                                        <menuItem title="Undo" keyEquivalent="z" id="dRJ-4n-Yzg">
                                            <connections>
                                                <action selector="undo:" target="Ady-hI-5gd" id="M6e-cu-g7V"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Redo" keyEquivalent="Z" id="6dh-zS-Vam">
                                            <connections>
                                                <action selector="redo:" target="Ady-hI-5gd" id="oIA-Rs-6OD"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="WRV-NI-Exz"/>
                                        <menuItem title="Cut" keyEquivalent="x" id="uRl-iY-unG">
                                            <connections>
                                                <action selector="cut:" target="Ady-hI-5gd" id="YJe-68-I9s"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Copy" keyEquivalent="c" id="x3v-GG-iWU">
                                            <connections>
                                                <action selector="copy:" target="Ady-hI-5gd" id="G1f-GL-Joy"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Paste" keyEquivalent="v" id="gVA-U4-sdL">
                                            <connections>
                                                <action selector="paste:" target="Ady-hI-5gd" id="UvS-8e-Qdg"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Paste and Match Style" keyEquivalent="V" id="WeT-3V-zwk">
                                            <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                            <connections>
                                                <action selector="pasteAsPlainText:" target="Ady-hI-5gd" id="cEh-KX-wJQ"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Delete" id="pa3-QI-u2k">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="delete:" target="Ady-hI-5gd" id="0Mk-Ml-PaM"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Select All" keyEquivalent="a" id="Ruw-6m-B2m">
                                            <connections>
                                                <action selector="selectAll:" target="Ady-hI-5gd" id="VNm-Mi-diN"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="uyl-h8-XO2"/>
                                        <menuItem title="Find" id="4EN-yA-p0u">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <menu key="submenu" title="Find" id="1b7-l0-nxx">
                                                <items>
                                                    <menuItem title="Find…" tag="1" keyEquivalent="f" id="Xz5-n4-O0W">
                                                        <connections>
                                                            <action selector="performFindPanelAction:" target="Ady-hI-5gd" id="cD7-Qs-BN4"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Find and Replace…" tag="12" keyEquivalent="f" id="YEy-JH-Tfz">
                                                        <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                                        <connections>
                                                            <action selector="performFindPanelAction:" target="Ady-hI-5gd" id="WD3-Gg-5AJ"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Find Next" tag="2" keyEquivalent="g" id="q09-fT-Sye">
                                                        <connections>
                                                            <action selector="performFindPanelAction:" target="Ady-hI-5gd" id="NDo-RZ-v9R"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Find Previous" tag="3" keyEquivalent="G" id="OwM-mh-QMV">
                                                        <connections>
                                                            <action selector="performFindPanelAction:" target="Ady-hI-5gd" id="HOh-sY-3ay"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Use Selection for Find" tag="7" keyEquivalent="e" id="buJ-ug-pKt">
                                                        <connections>
                                                            <action selector="performFindPanelAction:" target="Ady-hI-5gd" id="U76-nv-p5D"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Jump to Selection" keyEquivalent="j" id="S0p-oC-mLd">
                                                        <connections>
                                                            <action selector="centerSelectionInVisibleArea:" target="Ady-hI-5gd" id="IOG-6D-g5B"/>
                                                        </connections>
                                                    </menuItem>
                                                </items>
                                            </menu>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="View" id="H8h-7b-M4v">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="View" id="HyV-fh-RgO">
                                    <items>
                                        <menuItem title="Enter Full Screen" keyEquivalent="f" id="4J7-dP-txa">
                                            <modifierMask key="keyEquivalentModifierMask" control="YES" command="YES"/>
                                            <connections>
                                                <action selector="toggleFullScreen:" target="Ady-hI-5gd" id="dU3-MA-1Rq"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="Window" id="aUF-d1-5bR">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Window" systemMenu="window" id="Td7-aD-5lo">
                                    <items>
                                        <menuItem title="Minimize" keyEquivalent="m" id="OY7-WF-poV">
                                            <connections>
                                                <action selector="performMiniaturize:" target="Ady-hI-5gd" id="VwT-WD-YPe"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="Help" id="wpr-3q-Mcd">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Help" systemMenu="help" id="F2S-fz-NVQ">
                                    <items>
                                        <menuItem title="Rectangle Help" keyEquivalent="?" id="FKE-Sm-Kum">
                                            <connections>
                                                <action selector="showHelp:" target="Ady-hI-5gd" id="y7X-2Q-9no"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                        </items>
                    </menu>
                    <connections>
                        <outlet property="delegate" destination="Voe-Tx-rLC" id="PrD-fu-P6m"/>
                    </connections>
                </application>
                <customObject id="Voe-Tx-rLC" customClass="AppDelegate" customModule="Rectangle" customModuleProvider="target">
                    <connections>
                        <outlet property="ignoreMenuItem" destination="D99-0O-MB6" id="iBN-Bc-ds7"/>
                        <outlet property="mainStatusMenu" destination="6wc-Rn-2EG" id="UWt-be-ZH1"/>
                        <outlet property="quitMenuItem" destination="A66-A4-cGD" id="Vul-Dd-RcH"/>
                        <outlet property="unauthorizedMenu" destination="HZt-qn-3MK" id="c4f-hw-GWr"/>
                        <outlet property="viewLoggingMenuItem" destination="O8K-y6-bva" id="31H-se-kyh"/>
                    </connections>
                </customObject>
                <customObject id="YLy-65-1bz" customClass="NSFontManager"/>
                <customObject id="Ady-hI-5gd" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
                <menu id="6wc-Rn-2EG">
                    <items>
                        <menuItem title="Ignore frontmost.app" id="D99-0O-MB6">
                            <modifierMask key="keyEquivalentModifierMask"/>
                            <connections>
                                <action selector="ignoreFrontMostApp:" target="Voe-Tx-rLC" id="CTQ-bh-luD"/>
                            </connections>
                        </menuItem>
                        <menuItem isSeparatorItem="YES" id="hf9-TP-K3t"/>
                        <menuItem title="Settings…" id="YRC-4a-xGg">
                            <modifierMask key="keyEquivalentModifierMask"/>
                            <connections>
                                <action selector="openPreferences:" target="Voe-Tx-rLC" id="EgB-O8-H7c"/>
                            </connections>
                        </menuItem>
                        <menuItem title="About" id="gFy-Zj-RGl">
                            <modifierMask key="keyEquivalentModifierMask"/>
                            <connections>
                                <action selector="showAbout:" target="Voe-Tx-rLC" id="dGA-Fy-dtB"/>
                            </connections>
                        </menuItem>
                        <menuItem title="View Logging…" alternate="YES" id="O8K-y6-bva">
                            <modifierMask key="keyEquivalentModifierMask"/>
                            <connections>
                                <action selector="viewLogging:" target="Voe-Tx-rLC" id="8cH-2P-yKC"/>
                            </connections>
                        </menuItem>
                        <menuItem title="Check for Updates…" id="HIK-3r-i7E">
                            <modifierMask key="keyEquivalentModifierMask"/>
                            <connections>
                                <action selector="checkForUpdates:" target="Voe-Tx-rLC" id="kWZ-ie-6y9"/>
                            </connections>
                        </menuItem>
                        <menuItem title="Quit Rectangle" keyEquivalent="q" id="A66-A4-cGD">
                            <connections>
                                <action selector="terminate:" target="hnw-xV-0zn" id="M1a-kx-BJY"/>
                            </connections>
                        </menuItem>
                    </items>
                </menu>
                <menu id="HZt-qn-3MK">
                    <items>
                        <menuItem title="Not Authorized to Control Your Computer" id="cBj-76-2E7">
                            <modifierMask key="keyEquivalentModifierMask"/>
                        </menuItem>
                        <menuItem title="Authorize…" id="VIf-4h-MJW">
                            <modifierMask key="keyEquivalentModifierMask"/>
                            <connections>
                                <action selector="authorizeAccessibility:" target="Voe-Tx-rLC" id="fFL-4P-f1a"/>
                            </connections>
                        </menuItem>
                        <menuItem isSeparatorItem="YES" id="9x0-IB-BiQ"/>
                        <menuItem title="About" id="jxe-nr-LDQ">
                            <modifierMask key="keyEquivalentModifierMask"/>
                            <connections>
                                <action selector="orderFrontStandardAboutPanel:" target="hnw-xV-0zn" id="o6b-d0-Qt8"/>
                            </connections>
                        </menuItem>
                        <menuItem title="Quit Rectangle" keyEquivalent="q" id="yvN-PE-bxn">
                            <connections>
                                <action selector="terminate:" target="hnw-xV-0zn" id="esy-Pm-oTg"/>
                            </connections>
                        </menuItem>
                    </items>
                </menu>
            </objects>
            <point key="canvasLocation" x="75" y="0.0"/>
        </scene>
        <!--Window Controller-->
        <scene sceneID="9jZ-4I-Wps">
            <objects>
                <windowController storyboardIdentifier="PrefsWindowController" showSeguePresentationStyle="single" id="fmq-Go-Xew" sceneMemberID="viewController">
                    <window key="window" title="Rectangle Preferences" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" frameAutosaveName="" animationBehavior="default" titleVisibility="hidden" id="STb-JK-oB1">
                        <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES"/>
                        <rect key="contentRect" x="245" y="301" width="433" height="270"/>
                        <rect key="screenRect" x="0.0" y="0.0" width="1680" height="1025"/>
                        <connections>
                            <outlet property="delegate" destination="fmq-Go-Xew" id="Jj5-zM-epV"/>
                        </connections>
                    </window>
                    <connections>
                        <segue destination="5pc-CV-2b9" kind="relationship" relationship="window.shadowedContentViewController" id="94P-kM-qwg"/>
                    </connections>
                </windowController>
                <customObject id="jE6-FX-lql" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="72.5" y="281"/>
        </scene>
        <!--Prefs View Controller-->
        <scene sceneID="vcH-Iu-BJm">
            <objects>
                <viewController storyboardIdentifier="PrefsViewController" showSeguePresentationStyle="single" id="zlF-FD-XEr" customClass="PrefsViewController" customModule="Rectangle" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" id="8J7-VI-pmF">
                        <rect key="frame" x="0.0" y="0.0" width="772" height="628"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <subviews>
                            <stackView distribution="fill" orientation="vertical" alignment="leading" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" horizontalHuggingPriority="251" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="CSq-gR-vah">
                                <rect key="frame" x="30" y="20" width="712" height="578"/>
                                <subviews>
                                    <stackView distribution="fillEqually" orientation="horizontal" alignment="top" spacing="43" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Qhf-Xx-S0m">
                                        <rect key="frame" x="0.0" y="339" width="712" height="239"/>
                                        <subviews>
                                            <stackView distribution="fill" orientation="vertical" alignment="trailing" spacing="7" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="gt6-OD-H04">
                                                <rect key="frame" x="0.0" y="0.0" width="324" height="239"/>
                                                <subviews>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="6dl-2T-B9P">
                                                        <rect key="frame" x="65" y="220" width="259" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="ce1-6G-Nkf">
                                                                <rect key="frame" x="0.0" y="2" width="81" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="oac-MY-1n1">
                                                                        <rect key="frame" x="-2" y="0.0" width="56" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" title="Left Half" id="Xc8-Sm-pig">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="FMg-QE-c8c">
                                                                        <rect key="frame" x="60" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="grA-Oc-QdZ"/>
                                                                            <constraint firstAttribute="width" constant="21" id="slt-a4-LiJ"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="leftHalfTemplate" id="niV-VO-dFO"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="bGP-y9-ToI" customClass="MASShortcutView">
                                                                <rect key="frame" x="99" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="HHy-nL-j7h"/>
                                                                    <constraint firstAttribute="width" constant="160" id="g6q-pY-eGi"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="hzQ-m9-MoW">
                                                        <rect key="frame" x="57" y="194" width="267" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="MN2-Yv-nag">
                                                                <rect key="frame" x="0.0" y="2" width="89" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="daG-bl-Dca">
                                                                        <rect key="frame" x="-2" y="0.0" width="64" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" title="Right Half" id="F8S-GI-LiB">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="qQH-OG-nFg">
                                                                        <rect key="frame" x="68" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="Hqn-G9-Fhg"/>
                                                                            <constraint firstAttribute="height" constant="14" id="cMo-S4-1Xr"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="rightHalfTemplate" id="jFw-PK-jEa"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="DJc-yE-qoJ" customClass="MASShortcutView">
                                                                <rect key="frame" x="107" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="gbX-O0-CQo"/>
                                                                    <constraint firstAttribute="height" constant="19" id="k1S-LY-sDu"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="mCe-iK-xay">
                                                        <rect key="frame" x="48" y="168" width="276" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="OYx-6S-b7J">
                                                                <rect key="frame" x="0.0" y="2" width="98" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="3T8-Nh-bKb">
                                                                        <rect key="frame" x="-2" y="0.0" width="73" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Center Half" id="bRX-dV-iAR">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="zuU-r6-8ZI">
                                                                        <rect key="frame" x="77" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="VfN-dW-VyH"/>
                                                                            <constraint firstAttribute="width" constant="21" id="wdM-Aa-wbu"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="halfWidthCenterTemplate" id="Nlm-wE-Ala"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="3Pa-H7-x3u" customClass="MASShortcutView">
                                                                <rect key="frame" x="116" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="Dx5-7f-weD"/>
                                                                    <constraint firstAttribute="height" constant="19" id="egB-px-g2J"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="UZI-sV-QY7">
                                                        <rect key="frame" x="66" y="142" width="258" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Us6-9p-1W0">
                                                                <rect key="frame" x="0.0" y="2" width="80" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="j1Z-dN-QdR">
                                                                        <rect key="frame" x="-2" y="0.0" width="55" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Top Half" id="d7y-s8-7GE">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ije-LR-mK5">
                                                                        <rect key="frame" x="59" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="B86-qt-NA5"/>
                                                                            <constraint firstAttribute="width" constant="21" id="PfA-94-kot"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="topHalfTemplate" id="hLe-BD-DS8"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="ppH-ve-v6N" customClass="MASShortcutView">
                                                                <rect key="frame" x="98" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="CYP-H5-scH"/>
                                                                    <constraint firstAttribute="height" constant="19" id="m73-1c-QCb"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="19" id="eyI-8P-Cor"/>
                                                        </constraints>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="mG2-Ie-LGc">
                                                        <rect key="frame" x="44" y="116" width="280" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="bJh-ua-m5c">
                                                                <rect key="frame" x="0.0" y="2" width="102" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="xwW-KA-j8t">
                                                                        <rect key="frame" x="-2" y="0.0" width="77" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Bottom Half" id="ec4-FB-fMa">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="a2i-42-WyD">
                                                                        <rect key="frame" x="81" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="NNa-bt-h3b"/>
                                                                            <constraint firstAttribute="height" constant="14" id="tEt-B4-ThP"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="bottomHalfTemplate" id="rhq-RH-9Dx"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="EhR-CV-u8d" customClass="MASShortcutView">
                                                                <rect key="frame" x="120" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="QPA-YK-9mN"/>
                                                                    <constraint firstAttribute="width" constant="160" id="eTK-wh-TKh"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="19" id="IIv-mh-mPl"/>
                                                        </constraints>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="vertical" alignment="leading" spacing="0.0" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="12W-gY-FHp">
                                                        <rect key="frame" x="0.0" y="104" width="324" height="5"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="5" id="lTi-x2-WTD"/>
                                                            <constraint firstAttribute="width" priority="750" constant="324" id="xZH-Ps-oFE"/>
                                                        </constraints>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="xSC-UJ-fXz">
                                                        <rect key="frame" x="67" y="78" width="257" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="2uc-Nz-b1g">
                                                                <rect key="frame" x="0.0" y="2" width="79" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="HKQ-k8-IOz">
                                                                        <rect key="frame" x="-2" y="0.0" width="54" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Top Left" id="adp-cN-qkh">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="KwF-pH-4ai">
                                                                        <rect key="frame" x="58" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="LUw-sU-CUy"/>
                                                                            <constraint firstAttribute="width" constant="21" id="y9f-o3-9cn"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="topLeftTemplate" id="vqt-7b-pdJ"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="XIn-7Q-Fuy" customClass="MASShortcutView">
                                                                <rect key="frame" x="97" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="L6q-We-uRa"/>
                                                                    <constraint firstAttribute="height" constant="19" id="XcC-xK-4Ve"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="19" id="JNW-qU-mwE"/>
                                                        </constraints>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="hng-5H-ol0">
                                                        <rect key="frame" x="59" y="52" width="265" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="hDT-ZP-q5C">
                                                                <rect key="frame" x="0.0" y="2" width="87" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="ZHg-2r-X9K">
                                                                        <rect key="frame" x="-2" y="0.0" width="62" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Top Right" id="0Ak-33-SM7">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="H9u-70-HWQ">
                                                                        <rect key="frame" x="66" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="6PM-sF-9Sv"/>
                                                                            <constraint firstAttribute="width" constant="21" id="QaT-Lr-c0r"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="topRightTemplate" id="dgI-LE-ah9"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="OgW-L0-nuS" customClass="MASShortcutView">
                                                                <rect key="frame" x="105" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="FKY-cN-MnA"/>
                                                                    <constraint firstAttribute="width" constant="160" id="OIf-0U-mLf"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="19" id="2KF-NP-6dZ"/>
                                                        </constraints>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="vdR-hP-Tpn">
                                                        <rect key="frame" x="45" y="26" width="279" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="xfy-3f-KPI">
                                                                <rect key="frame" x="0.0" y="2" width="101" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="QBW-Z2-1Xz">
                                                                        <rect key="frame" x="-2" y="0.0" width="76" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Bottom Left" id="6ma-hP-5xX">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="lO4-Ug-roG">
                                                                        <rect key="frame" x="80" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="NHJ-m0-4TR"/>
                                                                            <constraint firstAttribute="height" constant="14" id="tpg-zl-W32"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="bottomLeftTemplate" id="0lM-zf-6x0"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="whf-FK-Ywl" customClass="MASShortcutView">
                                                                <rect key="frame" x="119" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="V1a-Mu-hJH"/>
                                                                    <constraint firstAttribute="width" constant="160" id="f0u-D0-4GA"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="19" id="Ssy-WQ-uAG"/>
                                                        </constraints>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="0dG-4f-nfF">
                                                        <rect key="frame" x="37" y="0.0" width="287" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="quG-Lk-ueq">
                                                                <rect key="frame" x="0.0" y="2" width="109" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="EHe-Oa-YEU">
                                                                        <rect key="frame" x="-2" y="0.0" width="84" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Bottom Right" id="J6t-sg-Wwz">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="RIa-GH-YY4">
                                                                        <rect key="frame" x="88" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="VWC-SH-dZi"/>
                                                                            <constraint firstAttribute="width" constant="21" id="yMA-FS-FIG"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="bottomRightTemplate" id="G0E-dj-TgB"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="wi5-BQ-zY2" customClass="MASShortcutView">
                                                                <rect key="frame" x="127" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="PXT-Bz-cXI"/>
                                                                    <constraint firstAttribute="width" constant="160" id="w18-Io-DBZ"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="19" id="ddL-Ea-Wi5"/>
                                                        </constraints>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                </subviews>
                                                <visibilityPriorities>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                </visibilityPriorities>
                                                <customSpacing>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                </customSpacing>
                                            </stackView>
                                            <stackView distribution="fill" orientation="vertical" alignment="trailing" spacing="7" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="wDN-pM-8lR">
                                                <rect key="frame" x="367" y="0.0" width="345" height="239"/>
                                                <subviews>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="0Hd-CT-TxW">
                                                        <rect key="frame" x="81" y="220" width="264" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="TGC-hg-6yl">
                                                                <rect key="frame" x="0.0" y="2" width="86" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="71y-S7-PEN">
                                                                        <rect key="frame" x="-2" y="0.0" width="61" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Maximize" id="8oe-J2-oUU">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="uM1-5q-snm">
                                                                        <rect key="frame" x="65" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="3Sc-bP-vld"/>
                                                                            <constraint firstAttribute="height" constant="14" id="h1l-Kt-UdK"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="maximizeTemplate" id="3KL-eU-jJz"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="SqN-WJ-u3L" customClass="MASShortcutView">
                                                                <rect key="frame" x="104" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="YVB-GF-OOD"/>
                                                                    <constraint firstAttribute="height" constant="19" id="dJV-Q8-tpg"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="rVb-UL-NJE">
                                                        <rect key="frame" x="35" y="194" width="310" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="PRu-9D-G3q">
                                                                <rect key="frame" x="0.0" y="2" width="132" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="O5c-1u-Brt">
                                                                        <rect key="frame" x="-2" y="0.0" width="107" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Almost Maximize" id="e57-QJ-6bL">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="9SE-7f-5jR">
                                                                        <rect key="frame" x="111" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="Ejy-cH-6zA"/>
                                                                            <constraint firstAttribute="width" constant="21" id="fru-qY-qkZ"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="almostMaximizeTemplate" id="poR-JF-MPO"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="WOL-Ei-P6G" customClass="MASShortcutView">
                                                                <rect key="frame" x="150" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="69s-IR-1dm"/>
                                                                    <constraint firstAttribute="width" constant="160" id="DuK-8e-vpE"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="aRK-l3-f6q">
                                                        <rect key="frame" x="37" y="168" width="308" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="BVf-QY-tqC">
                                                                <rect key="frame" x="0.0" y="2" width="130" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="G6i-Or-FfE">
                                                                        <rect key="frame" x="-2" y="0.0" width="105" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Maximize Height" id="6DV-cd-fda">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Gcs-tm-d0H">
                                                                        <rect key="frame" x="109" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="V6c-5v-dFG"/>
                                                                            <constraint firstAttribute="width" constant="21" id="gF3-QM-XQb"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="maximizeHeightTemplate" id="pVz-ei-jjB"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="0L5-5g-6s0" customClass="MASShortcutView">
                                                                <rect key="frame" x="148" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="oPS-P2-srV"/>
                                                                    <constraint firstAttribute="width" constant="160" id="sjg-BQ-gdh"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="nLL-Xs-VHA">
                                                        <rect key="frame" x="56" y="142" width="289" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="E6f-24-Eit">
                                                                <rect key="frame" x="0.0" y="2" width="111" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="QmV-Ew-OQp">
                                                                        <rect key="frame" x="-2" y="0.0" width="86" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Make Smaller" id="MzN-CJ-ASD">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="RyQ-wp-8vL">
                                                                        <rect key="frame" x="90" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="BrG-bg-nVi"/>
                                                                            <constraint firstAttribute="width" constant="21" id="DbI-qY-r5b"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="makeSmallerTemplate" id="o8n-3u-an7"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="yrl-Hc-jn1" customClass="MASShortcutView">
                                                                <rect key="frame" x="129" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="31I-cC-V5H"/>
                                                                    <constraint firstAttribute="width" constant="160" id="Jmu-H8-S0o"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="POP-uw-KpE">
                                                        <rect key="frame" x="62" y="116" width="283" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="br5-Mn-jM0">
                                                                <rect key="frame" x="0.0" y="2" width="105" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="uM4-fm-qWP">
                                                                        <rect key="frame" x="-2" y="0.0" width="80" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Make Larger" id="Eah-KL-kbn">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="twe-Dq-kbl">
                                                                        <rect key="frame" x="84" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="7cQ-Xo-2uP"/>
                                                                            <constraint firstAttribute="width" constant="21" id="KNq-Vt-Dzk"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="makeLargerTemplate" id="bvX-en-6rj"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="Oab-Yb-TxI" customClass="MASShortcutView">
                                                                <rect key="frame" x="123" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="O6Q-LT-YZV"/>
                                                                    <constraint firstAttribute="height" constant="19" id="ffI-fE-1vD"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="qPO-Zc-MBX">
                                                        <rect key="frame" x="97" y="90" width="248" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Mo5-WX-MxB">
                                                                <rect key="frame" x="0.0" y="2" width="70" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="vFY-Bb-fYR">
                                                                        <rect key="frame" x="-2" y="0.0" width="45" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Center" id="8Bg-SZ-hDO">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ebk-0j-d08">
                                                                        <rect key="frame" x="49" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="6xm-3M-OJ8"/>
                                                                            <constraint firstAttribute="height" constant="14" id="jLm-eM-4bz"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="centerTemplate" id="kAS-SN-5Gz"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="WrB-f6-rnc" customClass="MASShortcutView">
                                                                <rect key="frame" x="88" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="7rp-wt-YNO"/>
                                                                    <constraint firstAttribute="height" constant="19" id="zCg-t7-Cdi"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="G0A-5v-FZT">
                                                        <rect key="frame" x="91" y="64" width="254" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="KIs-zp-YfP">
                                                                <rect key="frame" x="0.0" y="2" width="76" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="DVN-Fh-ZlH">
                                                                        <rect key="frame" x="-2" y="0.0" width="51" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Restore" id="C9v-g0-DH8">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="pZc-Z9-K4a">
                                                                        <rect key="frame" x="55" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="7w4-uF-F69"/>
                                                                            <constraint firstAttribute="height" constant="14" id="wd7-yu-qJQ"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="restoreTemplate" id="Wa4-eX-gne"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="lej-Pz-wb0" customClass="MASShortcutView">
                                                                <rect key="frame" x="94" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="fkn-Kg-zlU"/>
                                                                    <constraint firstAttribute="width" constant="160" id="pnl-cI-uM2"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="vertical" alignment="leading" spacing="0.0" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Dqh-Mk-l24">
                                                        <rect key="frame" x="0.0" y="52" width="345" height="5"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" priority="750" constant="345" id="Mab-5D-Ck3"/>
                                                            <constraint firstAttribute="height" constant="5" id="Y0S-8q-of8"/>
                                                        </constraints>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="NYl-g1-gHO">
                                                        <rect key="frame" x="62" y="26" width="283" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="W7V-QT-NjF">
                                                                <rect key="frame" x="0.0" y="2" width="105" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Ieo-Xi-OPd">
                                                                        <rect key="frame" x="-2" y="0.0" width="80" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Next Display" id="Jnd-Lc-nlh">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="cWJ-sy-CVQ">
                                                                        <rect key="frame" x="84" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="0ga-Ge-NU0"/>
                                                                            <constraint firstAttribute="height" constant="14" id="mM8-ag-JiV"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="nextDisplayTemplate" id="fwr-lX-ahw"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="Omo-F2-B0I" customClass="MASShortcutView">
                                                                <rect key="frame" x="123" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="Ftc-bL-dER"/>
                                                                    <constraint firstAttribute="width" constant="160" id="PnY-87-ZpF"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="NWy-gq-tX3">
                                                        <rect key="frame" x="38" y="0.0" width="307" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="C8I-tj-Qx4">
                                                                <rect key="frame" x="0.0" y="2" width="129" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="HXv-c3-Qyj">
                                                                        <rect key="frame" x="-2" y="0.0" width="104" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Previous Display" id="QwF-QN-YH7">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="QRu-od-YFu">
                                                                        <rect key="frame" x="108" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="OAI-tm-dPx"/>
                                                                            <constraint firstAttribute="height" constant="14" id="wy0-ee-3TC"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="prevDisplayTemplate" id="xHS-dq-fxB"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="daM-hW-c6u" customClass="MASShortcutView">
                                                                <rect key="frame" x="147" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="LcG-CM-Hse"/>
                                                                    <constraint firstAttribute="width" constant="160" id="vtL-IH-WLh"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                </subviews>
                                                <visibilityPriorities>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                </visibilityPriorities>
                                                <customSpacing>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                </customSpacing>
                                            </stackView>
                                        </subviews>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="10" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="gmc-Bu-ioG">
                                        <rect key="frame" x="0.0" y="299" width="137" height="32"/>
                                        <subviews>
                                            <button focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="WPl-J4-Y8A">
                                                <rect key="frame" x="0.0" y="8" width="31" height="16"/>
                                                <buttonCell key="cell" type="bevel" title="▶︎ ⋯" bezelStyle="rounded" imagePosition="right" alignment="center" focusRingType="none" imageScaling="proportionallyDown" inset="2" id="MKW-qf-Q2C">
                                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                    <font key="font" metaFont="system"/>
                                                </buttonCell>
                                                <connections>
                                                    <action selector="toggleShowMore:" target="zlF-FD-XEr" id="sGL-ip-4gD"/>
                                                </connections>
                                            </button>
                                            <box verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="rsX-Dg-tQM">
                                                <rect key="frame" x="41" y="14" width="96" height="5"/>
                                            </box>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="32" id="p6Q-mT-cam"/>
                                        </constraints>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                    <stackView distribution="fillEqually" orientation="horizontal" alignment="top" spacing="43" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="tCC-xX-WUq">
                                        <rect key="frame" x="0.0" y="0.0" width="712" height="291"/>
                                        <subviews>
                                            <stackView distribution="fill" orientation="vertical" alignment="trailing" spacing="7" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="su2-sR-KVz">
                                                <rect key="frame" x="0.0" y="0.0" width="324" height="291"/>
                                                <subviews>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="GgD-be-blq">
                                                        <rect key="frame" x="55" y="272" width="269" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="zjW-UX-cpn">
                                                                <rect key="frame" x="0.0" y="2" width="91" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="ujE-rY-lCg">
                                                                        <rect key="frame" x="-2" y="0.0" width="66" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="First Third" id="F12-EV-Lfz">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="8dZ-LA-26W">
                                                                        <rect key="frame" x="70" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="KRT-mh-Hr3"/>
                                                                            <constraint firstAttribute="width" constant="21" id="rzA-BI-soR"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="firstThirdTemplate" id="M2U-HR-bFU"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="I3d-KP-y1J" customClass="MASShortcutView">
                                                                <rect key="frame" x="109" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="6Q8-54-QyQ"/>
                                                                    <constraint firstAttribute="height" constant="19" id="fHF-nY-MLc"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="yo8-YB-lLj">
                                                        <rect key="frame" x="41" y="246" width="283" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="yxe-q4-7yW">
                                                                <rect key="frame" x="0.0" y="2" width="105" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="DDI-Yz-vr6">
                                                                        <rect key="frame" x="-2" y="0.0" width="80" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Center Third" id="7YK-9Z-lzw">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="iA4-0O-O4R">
                                                                        <rect key="frame" x="84" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="1Gm-Iu-vLj"/>
                                                                            <constraint firstAttribute="height" constant="14" id="7Kg-nE-mW2"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="centerThirdTemplate" id="Dge-ND-6Mv"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="G13-Q4-CV6" customClass="MASShortcutView">
                                                                <rect key="frame" x="123" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="4xq-rG-nKr"/>
                                                                    <constraint firstAttribute="width" constant="160" id="N44-iI-kwW"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="y0y-Y6-G9U">
                                                        <rect key="frame" x="56" y="220" width="268" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="02U-1U-ZNS">
                                                                <rect key="frame" x="0.0" y="2" width="90" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="bod-Jy-fPb">
                                                                        <rect key="frame" x="-2" y="0.0" width="65" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Last Third" id="cRm-wn-Yv6">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="RIa-pX-3Rh">
                                                                        <rect key="frame" x="69" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="nGD-UE-sXi"/>
                                                                            <constraint firstAttribute="width" constant="21" id="s2F-kc-t4U"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="lastThirdTemplate" id="QSd-aI-wsp"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="MYy-5x-boe" customClass="MASShortcutView">
                                                                <rect key="frame" x="108" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="3I7-hv-MZL"/>
                                                                    <constraint firstAttribute="width" constant="160" id="pgs-rz-DzD"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="kgg-z8-TD6">
                                                        <rect key="frame" x="20" y="194" width="304" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="xcG-kM-Fl9">
                                                                <rect key="frame" x="0.0" y="2" width="126" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="dRO-bH-qbF">
                                                                        <rect key="frame" x="-2" y="0.0" width="101" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="First Two Thirds" id="3zd-xE-oWl">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="gs2-vc-CpO">
                                                                        <rect key="frame" x="105" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="Zf3-LJ-GpE"/>
                                                                            <constraint firstAttribute="height" constant="14" id="wds-eL-dTs"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="firstTwoThirdsTemplate" id="lnz-XV-o1b"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="cLa-f6-RSt" customClass="MASShortcutView">
                                                                <rect key="frame" x="144" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="F1y-fr-HRS"/>
                                                                    <constraint firstAttribute="height" constant="19" id="kBw-oI-aC7"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="ZEb-ZE-AIt">
                                                        <rect key="frame" x="21" y="168" width="303" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Osf-zT-z4q">
                                                                <rect key="frame" x="0.0" y="2" width="125" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="EGh-4z-I6I">
                                                                        <rect key="frame" x="-2" y="0.0" width="100" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Last Two Thirds" id="08q-Ce-1QL">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="xd2-iN-nQ4">
                                                                        <rect key="frame" x="104" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="M48-KC-wbp"/>
                                                                            <constraint firstAttribute="height" constant="14" id="QUI-lg-fzW"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="lastTwoThirdsTemplate" id="8wD-93-SJy"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="7Km-ay-hPl" customClass="MASShortcutView">
                                                                <rect key="frame" x="143" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="dfA-2A-xPS"/>
                                                                    <constraint firstAttribute="width" constant="160" id="jeR-9I-8MC"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="vertical" alignment="leading" spacing="0.0" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="LJS-uP-vY4">
                                                        <rect key="frame" x="142" y="156" width="182" height="5"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="5" id="DyY-7Z-OcE"/>
                                                            <constraint firstAttribute="width" constant="182" id="Ebg-dJ-tCZ"/>
                                                        </constraints>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="i0F-hL-EAL">
                                                        <rect key="frame" x="47" y="130" width="277" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="FlD-9W-LFa">
                                                                <rect key="frame" x="0.0" y="2" width="99" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="faB-Wl-vsg">
                                                                        <rect key="frame" x="-2" y="0.0" width="74" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="First Fourth" id="Q6Q-6J-okH">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="5XU-Wr-xuR">
                                                                        <rect key="frame" x="78" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="FbO-ZR-GR2"/>
                                                                            <constraint firstAttribute="height" constant="14" id="QWv-MW-xrN"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="leftFourthTemplate" id="FaX-hr-0Hm"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="UH9-8R-0Vx" customClass="MASShortcutView">
                                                                <rect key="frame" x="117" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="c0m-1n-nyI"/>
                                                                    <constraint firstAttribute="width" constant="160" id="dID-4o-7fK"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="ZOe-3P-5oG">
                                                        <rect key="frame" x="27" y="104" width="297" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="DiQ-1C-qFw">
                                                                <rect key="frame" x="0.0" y="2" width="119" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="04O-aU-LP0">
                                                                        <rect key="frame" x="-2" y="0.0" width="94" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Second Fourth" id="Fko-xs-gN5">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Tg2-Aw-EuH">
                                                                        <rect key="frame" x="98" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="Idr-FQ-dFC"/>
                                                                            <constraint firstAttribute="height" constant="14" id="m6z-eN-0xN"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="centerLeftFourthTemplate" id="7dg-xK-wWw"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="K1S-Mg-vfI" customClass="MASShortcutView">
                                                                <rect key="frame" x="137" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="FTR-Z9-HX5"/>
                                                                    <constraint firstAttribute="height" constant="19" id="aeI-mH-hyO"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="bWz-EP-mWR">
                                                        <rect key="frame" x="42" y="78" width="282" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Bxo-Le-75Q">
                                                                <rect key="frame" x="0.0" y="2" width="104" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Goa-cw-5IL">
                                                                        <rect key="frame" x="-2" y="0.0" width="79" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Third Fourth" id="ZTK-rS-b17">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="FTU-yB-T2z">
                                                                        <rect key="frame" x="83" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="L2K-1v-X47"/>
                                                                            <constraint firstAttribute="width" constant="21" id="kW3-7U-LWl"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="centerRightFourthTemplate" id="MJE-qY-C1r"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="OmC-pU-vQt" customClass="MASShortcutView">
                                                                <rect key="frame" x="122" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="ViF-zj-jhZ"/>
                                                                    <constraint firstAttribute="height" constant="19" id="vEX-QX-yLU"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="svP-4y-qQI">
                                                        <rect key="frame" x="48" y="52" width="276" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="A5V-nW-fPz">
                                                                <rect key="frame" x="0.0" y="2" width="98" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="pAS-zA-VWv">
                                                                        <rect key="frame" x="-2" y="0.0" width="73" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Last Fourth" id="6HX-rn-VIp">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="WWT-hu-i8g">
                                                                        <rect key="frame" x="77" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="eDS-dr-LMO"/>
                                                                            <constraint firstAttribute="height" constant="14" id="py3-jZ-ifs"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="rightFourthTemplate" id="07y-KG-PIP"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="Icr-hA-oP5" customClass="MASShortcutView">
                                                                <rect key="frame" x="116" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="1Gv-IT-pLU"/>
                                                                    <constraint firstAttribute="width" constant="160" id="Y8d-XZ-1YL"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="rom-Em-VtW">
                                                        <rect key="frame" x="1" y="26" width="323" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="l0o-F2-Bkj">
                                                                <rect key="frame" x="0.0" y="2" width="145" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="kcf-dX-QpK">
                                                                        <rect key="frame" x="-2" y="0.0" width="120" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="First Three Fourths" id="T9Z-QF-gwc">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="MWZ-av-t6O">
                                                                        <rect key="frame" x="124" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="GFA-RA-cvF"/>
                                                                            <constraint firstAttribute="height" constant="14" id="aOL-3Y-SYb"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="firstThreeFourthsTemplate" id="KdF-lb-kf6"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="bRp-d2-DdY" customClass="MASShortcutView">
                                                                <rect key="frame" x="163" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="Byj-tv-JLA"/>
                                                                    <constraint firstAttribute="height" constant="19" id="ff2-JI-gwb"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="tdu-Q7-TBH">
                                                        <rect key="frame" x="2" y="0.0" width="322" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="8EJ-X1-sk8">
                                                                <rect key="frame" x="0.0" y="2" width="144" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="8sG-F8-9JB">
                                                                        <rect key="frame" x="-2" y="0.0" width="119" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Last Three Fourths" id="nwX-h6-fwm">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="o7Z-0e-T42">
                                                                        <rect key="frame" x="123" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="FMc-HT-sGO"/>
                                                                            <constraint firstAttribute="width" constant="21" id="w1q-HW-qa7"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="lastThreeFourthsTemplate" id="VkO-5k-P6A"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="54r-uU-1LO" customClass="MASShortcutView">
                                                                <rect key="frame" x="162" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="Ocm-Nc-eBI"/>
                                                                    <constraint firstAttribute="height" constant="19" id="q89-dZ-GEo"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                </subviews>
                                                <visibilityPriorities>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                </visibilityPriorities>
                                                <customSpacing>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                </customSpacing>
                                            </stackView>
                                            <stackView distribution="fill" orientation="vertical" alignment="trailing" spacing="7" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Er7-tt-cBg">
                                                <rect key="frame" x="367" y="26" width="345" height="265"/>
                                                <subviews>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="3Ku-sN-43t">
                                                        <rect key="frame" x="78" y="246" width="267" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="nzY-kb-hBq">
                                                                <rect key="frame" x="0.0" y="2" width="89" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="qe4-dZ-3cw">
                                                                        <rect key="frame" x="-2" y="0.0" width="64" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Move Left" id="v2f-bX-xiM">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Yfk-nX-Od6">
                                                                        <rect key="frame" x="68" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="1cK-8V-orq"/>
                                                                            <constraint firstAttribute="width" constant="21" id="t2Q-W1-cKY"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="moveLeftTemplate" id="OhK-Oz-uJU"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="wqe-dP-72p" customClass="MASShortcutView">
                                                                <rect key="frame" x="107" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="17L-ij-gdb"/>
                                                                    <constraint firstAttribute="height" constant="19" id="FLi-46-L0Y"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="J6f-Ug-bUW">
                                                        <rect key="frame" x="70" y="220" width="275" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="YuG-PK-sHB">
                                                                <rect key="frame" x="0.0" y="2" width="97" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="y9e-dj-00t">
                                                                        <rect key="frame" x="-2" y="0.0" width="72" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Move Right" id="rzr-Qq-702">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="eqF-aF-Oig">
                                                                        <rect key="frame" x="76" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="4L8-fg-wKc"/>
                                                                            <constraint firstAttribute="width" constant="21" id="ag4-Pv-HcF"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="moveRightTemplate" id="cqE-7J-pZn"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="Ksl-60-1S2" customClass="MASShortcutView">
                                                                <rect key="frame" x="115" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="JSg-uj-s3A"/>
                                                                    <constraint firstAttribute="width" constant="160" id="ehd-H1-Cph"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5Qa-C8-rbw">
                                                        <rect key="frame" x="84" y="194" width="261" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="GJ7-ha-Hpw">
                                                                <rect key="frame" x="0.0" y="2" width="83" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="5eO-XA-d6k">
                                                                        <rect key="frame" x="-2" y="0.0" width="58" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Move Up" id="HOm-BV-2jc">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="B5Q-Rh-2dT">
                                                                        <rect key="frame" x="62" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="7a4-K5-Zzv"/>
                                                                            <constraint firstAttribute="width" constant="21" id="u0t-Il-uNf"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="moveUpTemplate" id="p2E-Li-XTf"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="POq-dE-gsa" customClass="MASShortcutView">
                                                                <rect key="frame" x="101" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="2Pt-Bw-FEa"/>
                                                                    <constraint firstAttribute="height" constant="19" id="7f3-OZ-K7Q"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="VLV-Cc-em4">
                                                        <rect key="frame" x="67" y="168" width="278" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="A0d-Qz-1hL">
                                                                <rect key="frame" x="0.0" y="2" width="100" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="ItO-yj-ZjG">
                                                                        <rect key="frame" x="-2" y="0.0" width="75" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Move Down" id="1Rc-Od-eP5">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="iFJ-Pf-nFN">
                                                                        <rect key="frame" x="79" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="Hcj-Rj-kf0"/>
                                                                            <constraint firstAttribute="width" constant="21" id="QzZ-Qr-jY7"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="moveDownTemplate" id="fA4-0b-aAa"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="nyf-lf-cNc" customClass="MASShortcutView">
                                                                <rect key="frame" x="118" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="EoF-fh-9EU"/>
                                                                    <constraint firstAttribute="height" constant="19" id="gwb-Yb-c8M"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="vertical" alignment="leading" spacing="0.0" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="4Mt-E4-Qzu">
                                                        <rect key="frame" x="163" y="156" width="182" height="5"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="182" id="PP3-in-sf8"/>
                                                            <constraint firstAttribute="height" constant="5" id="SXQ-wc-GnU"/>
                                                        </constraints>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="HKo-Do-YBz">
                                                        <rect key="frame" x="54" y="130" width="291" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="JfE-ts-ccs">
                                                                <rect key="frame" x="0.0" y="2" width="113" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="fbO-14-Gb6">
                                                                        <rect key="frame" x="-2" y="0.0" width="88" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Top Left Sixth" id="mFt-Kg-UYG">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="eRi-B2-mbJ">
                                                                        <rect key="frame" x="92" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="LQI-mW-MUg"/>
                                                                            <constraint firstAttribute="height" constant="14" id="ZNQ-n2-w2q"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="topLeftSixthTemplate" id="ibz-h8-iZ4"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="lMl-Xc-93q" customClass="MASShortcutView">
                                                                <rect key="frame" x="131" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="8Qv-7d-U7a"/>
                                                                    <constraint firstAttribute="width" constant="160" id="nJE-XP-0yj"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="H75-04-DG2">
                                                        <rect key="frame" x="37" y="104" width="308" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="DW4-7K-gmf">
                                                                <rect key="frame" x="0.0" y="2" width="130" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="iJf-Tt-905">
                                                                        <rect key="frame" x="-2" y="0.0" width="105" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Top Center Sixth" id="TTx-7X-Wie">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="cft-9i-Lyw">
                                                                        <rect key="frame" x="109" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="MAf-aB-wvF"/>
                                                                            <constraint firstAttribute="width" constant="21" id="k20-ZJ-7tG"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="topCenterSixthTemplate" id="0Uo-4L-GKO"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="qlu-3c-eH6" customClass="MASShortcutView">
                                                                <rect key="frame" x="148" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="hPL-RB-OWH"/>
                                                                    <constraint firstAttribute="width" constant="160" id="jsS-9U-ElF"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="we8-NI-xRq">
                                                        <rect key="frame" x="46" y="78" width="299" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="iWv-FN-2KZ">
                                                                <rect key="frame" x="0.0" y="2" width="121" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="3kT-9Z-Px1">
                                                                        <rect key="frame" x="-2" y="0.0" width="96" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Top Right Sixth" id="f3Q-q7-Pcy">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="bHF-5w-us6">
                                                                        <rect key="frame" x="100" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="jiE-jB-vH3"/>
                                                                            <constraint firstAttribute="height" constant="14" id="m51-Sd-XQb"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="topRightSixthTemplate" id="Jeq-ru-BtC"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="kbl-TR-waH" customClass="MASShortcutView">
                                                                <rect key="frame" x="139" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="kzJ-Rz-vkN"/>
                                                                    <constraint firstAttribute="height" constant="19" id="x31-NN-kGZ"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="IAt-Kk-UZl">
                                                        <rect key="frame" x="32" y="52" width="313" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="4R2-AE-zlQ">
                                                                <rect key="frame" x="0.0" y="2" width="135" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="eGz-TZ-Y0q">
                                                                        <rect key="frame" x="-2" y="0.0" width="110" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Bottom Left Sixth" id="LqQ-pM-jRN">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="YB5-Te-1yi">
                                                                        <rect key="frame" x="114" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="V16-Pa-iii"/>
                                                                            <constraint firstAttribute="width" constant="21" id="qLE-Pd-Pxk"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="bottomLeftSixthTemplate" id="eqo-8j-Vca"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="i2i-uH-Bdw" customClass="MASShortcutView">
                                                                <rect key="frame" x="153" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="4j2-q3-PeM"/>
                                                                    <constraint firstAttribute="height" constant="19" id="ZdA-Ec-7uk"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="gSs-dt-vff">
                                                        <rect key="frame" x="15" y="26" width="330" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="xmk-zw-BkR">
                                                                <rect key="frame" x="0.0" y="2" width="152" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="kzS-bR-69J">
                                                                        <rect key="frame" x="-2" y="0.0" width="127" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Bottom Center Sixth" id="iOQ-1e-esP">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="4F5-Ko-f4D">
                                                                        <rect key="frame" x="131" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="height" constant="14" id="7Vo-H0-Eb0"/>
                                                                            <constraint firstAttribute="width" constant="21" id="Zi5-9v-ldN"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="bottomCenterSixthTemplate" id="yZL-f3-0e7"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="N5I-c3-Pld" customClass="MASShortcutView">
                                                                <rect key="frame" x="170" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="160" id="82q-LC-64Z"/>
                                                                    <constraint firstAttribute="height" constant="19" id="rOm-BC-FVd"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="18" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="fs7-bL-08k">
                                                        <rect key="frame" x="24" y="0.0" width="321" height="19"/>
                                                        <subviews>
                                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="aDV-lR-D1V">
                                                                <rect key="frame" x="0.0" y="2" width="143" height="16"/>
                                                                <subviews>
                                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="udM-US-yWD">
                                                                        <rect key="frame" x="-2" y="0.0" width="118" height="16"/>
                                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="Bottom Right Sixth" id="m2F-eA-g7w">
                                                                            <font key="font" usesAppearanceFont="YES"/>
                                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="unP-Ed-ePm">
                                                                        <rect key="frame" x="122" y="1" width="21" height="14"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="21" id="Drh-Aa-fad"/>
                                                                            <constraint firstAttribute="height" constant="14" id="qIi-aT-sSx"/>
                                                                        </constraints>
                                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="bottomRightSixthTemplate" id="7Gj-FO-b8g"/>
                                                                    </imageView>
                                                                </subviews>
                                                                <visibilityPriorities>
                                                                    <integer value="1000"/>
                                                                    <integer value="1000"/>
                                                                </visibilityPriorities>
                                                                <customSpacing>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                    <real value="3.4028234663852886e+38"/>
                                                                </customSpacing>
                                                            </stackView>
                                                            <customView translatesAutoresizingMaskIntoConstraints="NO" id="yRx-4l-5cf" customClass="MASShortcutView">
                                                                <rect key="frame" x="161" y="0.0" width="160" height="19"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="19" id="GFc-9O-1CK"/>
                                                                    <constraint firstAttribute="width" constant="160" id="MTR-Zl-J0c"/>
                                                                </constraints>
                                                            </customView>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                </subviews>
                                                <visibilityPriorities>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                </visibilityPriorities>
                                                <customSpacing>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                </customSpacing>
                                            </stackView>
                                        </subviews>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="Er7-tt-cBg" firstAttribute="trailing" secondItem="wDN-pM-8lR" secondAttribute="trailing" id="5zu-js-HGD"/>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="200" id="Cxd-Dj-xZN"/>
                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="200" id="Hb6-XM-rd7"/>
                                    <constraint firstAttribute="height" priority="750" constant="100" id="gKG-hi-Myd"/>
                                    <constraint firstAttribute="width" priority="250" constant="100" id="hSY-dI-mOR"/>
                                    <constraint firstItem="su2-sR-KVz" firstAttribute="trailing" secondItem="gt6-OD-H04" secondAttribute="trailing" id="iGo-Yg-2jL"/>
                                </constraints>
                                <visibilityPriorities>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                </visibilityPriorities>
                                <customSpacing>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                </customSpacing>
                            </stackView>
                        </subviews>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="CSq-gR-vah" secondAttribute="trailing" constant="30" id="gSb-tX-Im4"/>
                            <constraint firstItem="CSq-gR-vah" firstAttribute="top" secondItem="8J7-VI-pmF" secondAttribute="top" constant="30" id="q94-8A-W8m"/>
                            <constraint firstItem="CSq-gR-vah" firstAttribute="leading" secondItem="8J7-VI-pmF" secondAttribute="leading" constant="30" id="vc9-Ui-yxD"/>
                            <constraint firstAttribute="bottom" secondItem="CSq-gR-vah" secondAttribute="bottom" constant="20" symbolic="YES" id="vrS-oI-vXb"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="additionalShortcutsStackView" destination="tCC-xX-WUq" id="csg-Vg-OUJ"/>
                        <outlet property="almostMaximizeShortcutView" destination="WOL-Ei-P6G" id="cO4-B7-tWg"/>
                        <outlet property="bottomCenterSixthShortcutView" destination="N5I-c3-Pld" id="ovA-A8-raG"/>
                        <outlet property="bottomHalfShortcutView" destination="EhR-CV-u8d" id="ObW-aF-Qw2"/>
                        <outlet property="bottomLeftShortcutView" destination="whf-FK-Ywl" id="TdK-Ae-aus"/>
                        <outlet property="bottomLeftSixthShortcutView" destination="i2i-uH-Bdw" id="3sd-fm-QCi"/>
                        <outlet property="bottomRightShortcutView" destination="wi5-BQ-zY2" id="Qha-pc-jyd"/>
                        <outlet property="bottomRightSixthShortcutView" destination="yRx-4l-5cf" id="Y7u-v7-HwC"/>
                        <outlet property="centerHalfShortcutView" destination="3Pa-H7-x3u" id="zfM-NU-9h8"/>
                        <outlet property="centerShortcutView" destination="WrB-f6-rnc" id="aM3-dp-LM0"/>
                        <outlet property="centerThirdShortcutView" destination="G13-Q4-CV6" id="2pX-94-yJP"/>
                        <outlet property="firstFourthShortcutView" destination="UH9-8R-0Vx" id="09f-5b-lse"/>
                        <outlet property="firstThirdShortcutView" destination="I3d-KP-y1J" id="2Ef-1P-Qeq"/>
                        <outlet property="firstThreeFourthsShortcutView" destination="bRp-d2-DdY" id="zRK-Gz-eS2"/>
                        <outlet property="firstTwoThirdsShortcutView" destination="cLa-f6-RSt" id="ZEv-cR-Wtf"/>
                        <outlet property="lastFourthShortcutView" destination="Icr-hA-oP5" id="rgO-ON-K2c"/>
                        <outlet property="lastThirdShortcutView" destination="MYy-5x-boe" id="sKH-xT-Kwg"/>
                        <outlet property="lastThreeFourthsShortcutView" destination="54r-uU-1LO" id="Tam-Wc-O2Y"/>
                        <outlet property="lastTwoThirdsShortcutView" destination="7Km-ay-hPl" id="31d-uh-wZ2"/>
                        <outlet property="leftHalfShortcutView" destination="bGP-y9-ToI" id="XZG-TF-jqQ"/>
                        <outlet property="makeLargerShortcutView" destination="Oab-Yb-TxI" id="e6P-aI-4My"/>
                        <outlet property="makeSmallerShortcutView" destination="yrl-Hc-jn1" id="ZPy-F9-bUE"/>
                        <outlet property="maximizeHeightShortcutView" destination="0L5-5g-6s0" id="b6h-ZP-sM9"/>
                        <outlet property="maximizeShortcutView" destination="SqN-WJ-u3L" id="hYh-vr-eG2"/>
                        <outlet property="moveDownShortcutView" destination="nyf-lf-cNc" id="mfY-cx-SqL"/>
                        <outlet property="moveLeftShortcutView" destination="wqe-dP-72p" id="Ar8-Do-Jfr"/>
                        <outlet property="moveRightShortcutView" destination="Ksl-60-1S2" id="9D9-eO-irR"/>
                        <outlet property="moveUpShortcutView" destination="POq-dE-gsa" id="1JW-Of-NUE"/>
                        <outlet property="nextDisplayShortcutView" destination="Omo-F2-B0I" id="Bhe-69-9Hn"/>
                        <outlet property="previousDisplayShortcutView" destination="daM-hW-c6u" id="Gdd-dY-wHF"/>
                        <outlet property="restoreShortcutView" destination="lej-Pz-wb0" id="9p0-dP-tKu"/>
                        <outlet property="rightHalfShortcutView" destination="DJc-yE-qoJ" id="OJZ-8F-b8g"/>
                        <outlet property="secondFourthShortcutView" destination="K1S-Mg-vfI" id="7e4-36-qqr"/>
                        <outlet property="showMoreButton" destination="WPl-J4-Y8A" id="d55-iL-5P6"/>
                        <outlet property="thirdFourthShortcutView" destination="OmC-pU-vQt" id="ZLp-Im-PIE"/>
                        <outlet property="topCenterSixthShortcutView" destination="qlu-3c-eH6" id="JCd-zG-D9X"/>
                        <outlet property="topHalfShortcutView" destination="ppH-ve-v6N" id="JE4-GU-6w0"/>
                        <outlet property="topLeftShortcutView" destination="XIn-7Q-Fuy" id="5oj-Fa-48o"/>
                        <outlet property="topLeftSixthShortcutView" destination="lMl-Xc-93q" id="TKu-H9-gxR"/>
                        <outlet property="topRightShortcutView" destination="OgW-L0-nuS" id="akm-zB-cnx"/>
                        <outlet property="topRightSixthShortcutView" destination="kbl-TR-waH" id="2Hp-hx-3bw"/>
                    </connections>
                </viewController>
                <customObject id="BOw-l7-fkl" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-968" y="1350"/>
        </scene>
        <!--Tab View Controller-->
        <scene sceneID="b39-fJ-MZO">
            <objects>
                <tabViewController selectedTabViewItemIndex="0" tabStyle="toolbar" canPropagateSelectedChildViewControllerTitle="NO" id="5pc-CV-2b9" sceneMemberID="viewController">
                    <tabViewItems>
                        <tabViewItem label="Keyboard Shortcuts" image="keyboardToolbarTemplate" id="uw2-9W-2jq"/>
                        <tabViewItem label="Snap Areas" image="snapAreaTemplate" id="Fap-R2-Aj6"/>
                        <tabViewItem label="Settings" image="toolbarSettingsTemplate" id="gtf-PD-IHm"/>
                    </tabViewItems>
                    <viewControllerTransitionOptions key="transitionOptions" allowUserInteraction="YES"/>
                    <tabView key="tabView" type="noTabsNoBorder" id="Tya-24-HNL">
                        <rect key="frame" x="-126" y="0.0" width="453" height="261"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <font key="font" metaFont="system"/>
                        <connections>
                            <outlet property="delegate" destination="5pc-CV-2b9" id="as1-vk-M94"/>
                        </connections>
                    </tabView>
                    <connections>
                        <outlet property="tabView" destination="Tya-24-HNL" id="TDu-Z5-7uP"/>
                        <segue destination="zlF-FD-XEr" kind="relationship" relationship="tabItems" id="Q5z-Um-3xn"/>
                        <segue destination="t2d-Q7-RLy" kind="relationship" relationship="tabItems" id="Tnv-BY-sch"/>
                        <segue destination="yhc-gS-h02" kind="relationship" relationship="tabItems" id="LMQ-qb-ja6"/>
                    </connections>
                </tabViewController>
                <customObject id="CxR-5G-Y25" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="73" y="700"/>
        </scene>
        <!--Settings View Controller-->
        <scene sceneID="tRx-9g-sUa">
            <objects>
                <viewController storyboardIdentifier="SettingsViewController" id="yhc-gS-h02" customClass="SettingsViewController" customModule="Rectangle" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" id="mTk-eQ-4uf">
                        <rect key="frame" x="0.0" y="0.0" width="490" height="535"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <subviews>
                            <stackView distribution="fill" orientation="vertical" alignment="leading" spacing="10" horizontalStackHuggingPriority="1000" verticalStackHuggingPriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="tUi-Ja-evb">
                                <rect key="frame" x="20" y="20" width="450" height="495"/>
                                <subviews>
                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="10" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="7ew-iJ-cZQ">
                                        <rect key="frame" x="0.0" y="479" width="450" height="16"/>
                                        <subviews>
                                            <button verticalHuggingPriority="749" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="eQJ-O3-a8H">
                                                <rect key="frame" x="-2" y="-1" width="396" height="18"/>
                                                <buttonCell key="cell" type="check" title="Launch on login" bezelStyle="regularSquare" imagePosition="left" inset="2" id="e9j-DR-MEH">
                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                    <font key="font" metaFont="system"/>
                                                </buttonCell>
                                                <connections>
                                                    <action selector="toggleLaunchOnLogin:" target="yhc-gS-h02" id="ySg-6C-AGY"/>
                                                </connections>
                                            </button>
                                            <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Azi-Y9-9xa">
                                                <rect key="frame" x="402" y="0.0" width="50" height="16"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="16" id="0gp-ng-z8z"/>
                                                </constraints>
                                                <textFieldCell key="cell" lineBreakMode="clipping" title="Version" id="1zK-sf-CSX">
                                                    <font key="font" metaFont="system"/>
                                                    <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                </textFieldCell>
                                            </textField>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="16" id="ucw-7e-Ozt"/>
                                        </constraints>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                    <button verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="wBT-R4-q9s">
                                        <rect key="frame" x="-2" y="452" width="452" height="18"/>
                                        <buttonCell key="cell" type="check" title="Hide menu bar icon" bezelStyle="regularSquare" imagePosition="left" inset="2" id="qlg-kC-FMr">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <action selector="toggleHideMenuBarIcon:" target="yhc-gS-h02" id="eAA-Vd-0gY"/>
                                        </connections>
                                    </button>
                                    <textField focusRingType="none" horizontalHuggingPriority="249" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="gIp-Hu-6ns">
                                        <rect key="frame" x="-2" y="429" width="454" height="14"/>
                                        <textFieldCell key="cell" title="When the menu bar icon is hidden, relaunch Rectangle from Finder to open" id="ltc-mf-BHr">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="10" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="AmL-wm-e8w">
                                        <rect key="frame" x="0.0" y="398" width="450" height="21"/>
                                        <subviews>
                                            <button horizontalHuggingPriority="249" verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="HcU-0y-wJU">
                                                <rect key="frame" x="-2" y="2" width="298" height="18"/>
                                                <buttonCell key="cell" type="check" title="Check for updates automatically" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="rmV-YD-Hzj">
                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                    <font key="font" metaFont="system"/>
                                                </buttonCell>
                                            </button>
                                            <button verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="bn6-rz-AHw">
                                                <rect key="frame" x="299" y="-6" width="158" height="32"/>
                                                <buttonCell key="cell" type="push" title="Check for Updates…" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="74m-kw-w1f">
                                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                    <font key="font" metaFont="system"/>
                                                </buttonCell>
                                                <connections>
                                                    <action selector="checkForUpdates:" target="yhc-gS-h02" id="Gln-SX-iYO"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="21" id="gzn-PM-OIu"/>
                                        </constraints>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                    <box verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="ujo-nl-syC">
                                        <rect key="frame" x="0.0" y="366" width="450" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="20" id="HTM-FQ-j4S"/>
                                        </constraints>
                                    </box>
                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="10" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="FFt-uh-04m">
                                        <rect key="frame" x="0.0" y="338" width="450" height="20"/>
                                        <subviews>
                                            <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="5An-8B-vsH">
                                                <rect key="frame" x="-2" y="2" width="132" height="16"/>
                                                <textFieldCell key="cell" lineBreakMode="clipping" title="Repeated commands" id="2Zm-fl-PcC">
                                                    <font key="font" metaFont="system"/>
                                                    <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                </textFieldCell>
                                            </textField>
                                            <popUpButton verticalHuggingPriority="750" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="pVH-s3-FHn">
                                                <rect key="frame" x="135" y="-4" width="319" height="25"/>
                                                <popUpButtonCell key="cell" type="push" title="move to adjacent on left/right, or cycle size on half" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="3" imageScaling="proportionallyDown" inset="2" selectedItem="3GE-la-fAZ" id="ccx-Gx-MGO">
                                                    <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                    <font key="font" size="12" name="HelveticaNeue"/>
                                                    <menu key="menu" id="TkU-PT-5p8">
                                                        <items>
                                                            <menuItem title="do nothing" tag="2" id="jww-Ju-S3d"/>
                                                            <menuItem title="cycle through displays" tag="4" id="XlM-ch-cLG"/>
                                                            <menuItem title="cycle sizes on half actions" id="gHH-BV-5kP"/>
                                                            <menuItem title="move to adjacent display on left or right" tag="1" id="Z9d-Rl-RVq"/>
                                                            <menuItem title="move to adjacent on left/right, or cycle size on half" state="on" tag="3" id="3GE-la-fAZ"/>
                                                        </items>
                                                    </menu>
                                                </popUpButtonCell>
                                                <connections>
                                                    <action selector="setSubsequentExecutionBehavior:" target="yhc-gS-h02" id="BUk-Is-27P"/>
                                                </connections>
                                            </popUpButton>
                                        </subviews>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                    <stackView hidden="YES" distribution="fill" orientation="horizontal" alignment="top" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" verticalClippingResistancePriority="999" translatesAutoresizingMaskIntoConstraints="NO" id="er1-EQ-YIF">
                                        <rect key="frame" x="0.0" y="338" width="163" height="0.0"/>
                                        <subviews>
                                            <customView horizontalHuggingPriority="249" placeholderIntrinsicWidth="163" placeholderIntrinsicHeight="0.0" translatesAutoresizingMaskIntoConstraints="NO" id="coA-qg-nul">
                                                <rect key="frame" x="0.0" y="0.0" width="163" height="0.0"/>
                                            </customView>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="height" id="IXv-8o-a6i"/>
                                        </constraints>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                    <stackView distribution="fill" orientation="horizontal" alignment="top" spacing="10" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Xcu-aJ-4bm">
                                        <rect key="frame" x="0.0" y="308" width="450" height="20"/>
                                        <subviews>
                                            <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="WkQ-lb-VGR">
                                                <rect key="frame" x="-2" y="4" width="147" height="16"/>
                                                <textFieldCell key="cell" lineBreakMode="clipping" title="Gaps between windows" id="bg9-nw-YvU">
                                                    <font key="font" metaFont="system"/>
                                                    <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                </textFieldCell>
                                            </textField>
                                            <slider verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="O9H-ZD-bqL">
                                                <rect key="frame" x="151" y="-6" width="265" height="28"/>
                                                <sliderCell key="cell" state="on" alignment="left" maxValue="100" tickMarkPosition="above" sliderType="linear" id="LAE-OT-g05"/>
                                                <connections>
                                                    <action selector="gapSliderChanged:" target="yhc-gS-h02" id="qCV-j7-EEw"/>
                                                </connections>
                                            </slider>
                                            <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="jd8-SJ-lza">
                                                <rect key="frame" x="422" y="4" width="30" height="16"/>
                                                <textFieldCell key="cell" lineBreakMode="clipping" title="0 px" id="0eh-6G-rMp">
                                                    <font key="font" metaFont="system"/>
                                                    <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                </textFieldCell>
                                            </textField>
                                        </subviews>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                    <button verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="UY7-Nt-G3K">
                                        <rect key="frame" x="-2" y="281" width="452" height="18"/>
                                        <buttonCell key="cell" type="check" title="Remove keyboard shortcut restrictions" bezelStyle="regularSquare" imagePosition="left" inset="2" id="n4U-FC-L9s">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <action selector="toggleAllowAnyShortcut:" target="yhc-gS-h02" id="dXP-rV-Pqk"/>
                                        </connections>
                                    </button>
                                    <button verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="WUS-pH-Rxv">
                                        <rect key="frame" x="-2" y="255" width="452" height="18"/>
                                        <buttonCell key="cell" type="check" title="Move cursor along with window across displays" bezelStyle="regularSquare" imagePosition="left" inset="2" id="Pbz-DF-hgG">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <action selector="toggleCursorMove:" target="yhc-gS-h02" id="9IZ-1w-iWL"/>
                                        </connections>
                                    </button>
                                    <button verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="hB7-uu-XeP" userLabel="Double-click Title Bar Checkbox">
                                        <rect key="frame" x="-2" y="229" width="452" height="18"/>
                                        <buttonCell key="cell" type="check" title="Double-click window title bar to maximize/restore" bezelStyle="regularSquare" imagePosition="left" inset="2" id="heT-W6-Fyf">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <action selector="toggleDoubleClickTitleBar:" target="yhc-gS-h02" id="q5b-BO-7n2"/>
                                        </connections>
                                    </button>
                                    <box verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="evn-f6-2bW">
                                        <rect key="frame" x="0.0" y="198" width="450" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="20" id="bBf-fp-7rI"/>
                                        </constraints>
                                    </box>
                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="5" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Ipm-Bt-PDm">
                                        <rect key="frame" x="0.0" y="174" width="450" height="16"/>
                                        <subviews>
                                            <button horizontalHuggingPriority="750" verticalHuggingPriority="750" horizontalCompressionResistancePriority="249" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="0PP-0x-QWc">
                                                <rect key="frame" x="-2" y="-1" width="182" height="18"/>
                                                <buttonCell key="cell" type="check" title="Show Todo Mode in menu" bezelStyle="regularSquare" imagePosition="left" inset="2" id="7yS-wj-uWD">
                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                    <font key="font" metaFont="system"/>
                                                </buttonCell>
                                                <connections>
                                                    <action selector="toggleTodoMode:" target="yhc-gS-h02" id="Wfa-A2-2YS"/>
                                                </connections>
                                            </button>
                                            <button horizontalHuggingPriority="1000" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="m1L-BJ-1Jg">
                                                <rect key="frame" x="185" y="0.0" width="17" height="16"/>
                                                <buttonCell key="cell" type="smallSquare" title="ⓘ" bezelStyle="smallSquare" imagePosition="overlaps" alignment="center" lineBreakMode="truncatingTail" state="on" imageScaling="proportionallyDown" inset="2" id="Vfd-fe-hCe">
                                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                    <font key="font" metaFont="system"/>
                                                </buttonCell>
                                                <connections>
                                                    <action selector="showTodoModeHelp:" target="yhc-gS-h02" id="TjK-4M-3ow"/>
                                                </connections>
                                            </button>
                                            <stackView distribution="fill" orientation="vertical" alignment="leading" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" horizontalCompressionResistancePriority="248" verticalCompressionResistancePriority="248" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Pf3-3Q-uE8">
                                                <rect key="frame" x="207" y="0.0" width="243" height="16"/>
                                            </stackView>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="Pf3-3Q-uE8" firstAttribute="top" secondItem="Ipm-Bt-PDm" secondAttribute="top" id="cTW-jC-lQg"/>
                                        </constraints>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                    <textField focusRingType="none" horizontalHuggingPriority="249" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="Q4P-mY-521">
                                        <rect key="frame" x="-2" y="150" width="454" height="14"/>
                                        <textFieldCell key="cell" title="Keep a chosen application visible on the right of your primary screen at all times" id="FCh-1Q-Xms">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <stackView distribution="fill" orientation="vertical" alignment="leading" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" verticalClippingResistancePriority="999" translatesAutoresizingMaskIntoConstraints="NO" id="lRr-k7-YZR">
                                        <rect key="frame" x="0.0" y="140" width="450" height="0.0"/>
                                        <subviews>
                                            <stackView identifier="Todo app width" distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="Ntf-YR-7Gr">
                                                <rect key="frame" x="0.0" y="-21" width="450" height="21"/>
                                                <subviews>
                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="90f-Y4-sgi">
                                                        <rect key="frame" x="-2" y="3" width="98" height="16"/>
                                                        <textFieldCell key="cell" lineBreakMode="clipping" title="Todo app width" id="6e0-ji-qXw">
                                                            <font key="font" metaFont="system"/>
                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                        </textFieldCell>
                                                    </textField>
                                                    <textField focusRingType="none" verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="9VW-Hc-lh2" customClass="AutoSaveFloatField" customModule="Rectangle" customModuleProvider="target">
                                                        <rect key="frame" x="102" y="0.0" width="100" height="21"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="100" id="A6q-mA-Xaq"/>
                                                            <constraint firstAttribute="width" priority="250" constant="100" id="Ebz-nb-cys"/>
                                                        </constraints>
                                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" alignment="right" title="400" drawsBackground="YES" id="lUu-Hs-vuk">
                                                            <numberFormatter key="formatter" formatterBehavior="default10_4" numberStyle="decimal" formatWidth="-1" minimumIntegerDigits="1" maximumIntegerDigits="2000000000" maximumFractionDigits="3" id="O5b-ir-jyO"/>
                                                            <font key="font" metaFont="system"/>
                                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                        </textFieldCell>
                                                    </textField>
                                                    <textField focusRingType="none" horizontalHuggingPriority="249" verticalHuggingPriority="750" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="eTN-Lz-EDf">
                                                        <rect key="frame" x="208" y="3" width="105" height="16"/>
                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="left" title="px" id="HVG-6v-eJH">
                                                            <font key="font" metaFont="system"/>
                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                        </textFieldCell>
                                                    </textField>
                                                    <stackView distribution="fill" orientation="horizontal" alignment="centerY" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" translatesAutoresizingMaskIntoConstraints="NO" id="Pon-Fc-IkL">
                                                        <rect key="frame" x="319" y="1" width="131" height="20"/>
                                                        <subviews>
                                                            <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="ZQD-06-YVI">
                                                                <rect key="frame" x="-2" y="2" width="63" height="16"/>
                                                                <textFieldCell key="cell" lineBreakMode="clipping" title="Todo side" id="O9a-3Q-HB1">
                                                                    <font key="font" metaFont="system"/>
                                                                    <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                </textFieldCell>
                                                            </textField>
                                                            <popUpButton verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="rB8-CF-Yrt">
                                                                <rect key="frame" x="64" y="-4" width="71" height="25"/>
                                                                <popUpButtonCell key="cell" type="push" title="Right" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="1" imageScaling="proportionallyDown" inset="2" selectedItem="DG2-BN-GPE" id="wsh-YF-QuF">
                                                                    <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                                    <font key="font" metaFont="message"/>
                                                                    <menu key="menu" id="8Vk-9N-Pkg">
                                                                        <items>
                                                                            <menuItem title="Left" tag="2" id="mpl-sd-2Wc"/>
                                                                            <menuItem title="Right" state="on" tag="1" id="DG2-BN-GPE"/>
                                                                        </items>
                                                                    </menu>
                                                                </popUpButtonCell>
                                                                <connections>
                                                                    <action selector="setTodoAppSide:" target="yhc-gS-h02" id="N1M-tN-4eI"/>
                                                                </connections>
                                                            </popUpButton>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                </subviews>
                                                <visibilityPriorities>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                </visibilityPriorities>
                                                <customSpacing>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                </customSpacing>
                                            </stackView>
                                            <stackView identifier="Toggle todo" distribution="fill" orientation="horizontal" alignment="centerY" spacing="10" horizontalStackHuggingPriority="1000" verticalStackHuggingPriority="249.99998474121094" horizontalHuggingPriority="1000" horizontalCompressionResistancePriority="250" verticalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="nsh-GW-hpI">
                                                <rect key="frame" x="0.0" y="-48" width="450" height="19"/>
                                                <subviews>
                                                    <textField focusRingType="none" horizontalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="cEi-pr-62P">
                                                        <rect key="frame" x="-2" y="2" width="79" height="16"/>
                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="left" title="Toggle Todo" id="DHt-cE-Bl0">
                                                            <font key="font" metaFont="system"/>
                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                        </textFieldCell>
                                                    </textField>
                                                    <customView horizontalHuggingPriority="249" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="uca-0m-naE" customClass="MASShortcutView">
                                                        <rect key="frame" x="85" y="0.0" width="160" height="19"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="19" id="OaK-b4-dxO"/>
                                                            <constraint firstAttribute="width" constant="160" id="tiw-8m-NjR"/>
                                                        </constraints>
                                                    </customView>
                                                    <stackView distribution="fill" orientation="vertical" alignment="leading" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" verticalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="qC3-Q0-xyP">
                                                        <rect key="frame" x="255" y="0.0" width="195" height="19"/>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="qC3-Q0-xyP" firstAttribute="top" secondItem="nsh-GW-hpI" secondAttribute="top" id="qJs-uS-q2u"/>
                                                </constraints>
                                                <visibilityPriorities>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                </visibilityPriorities>
                                                <customSpacing>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                </customSpacing>
                                            </stackView>
                                            <stackView identifier="Reflow todo" distribution="fill" orientation="horizontal" alignment="centerY" spacing="10" horizontalStackHuggingPriority="1000" verticalStackHuggingPriority="249.99998474121094" horizontalHuggingPriority="1000" horizontalCompressionResistancePriority="250" verticalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="83w-h7-hhH">
                                                <rect key="frame" x="0.0" y="-75" width="450" height="19"/>
                                                <subviews>
                                                    <textField focusRingType="none" horizontalHuggingPriority="1000" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="Cf5-4Q-E4I">
                                                        <rect key="frame" x="-2" y="2" width="79" height="16"/>
                                                        <textFieldCell key="cell" lineBreakMode="clipping" alignment="left" title="Reflow Todo" id="Fx0-sm-DrT">
                                                            <font key="font" metaFont="system"/>
                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                        </textFieldCell>
                                                    </textField>
                                                    <customView horizontalHuggingPriority="249" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="QTZ-pv-mwo" customClass="MASShortcutView">
                                                        <rect key="frame" x="85" y="0.0" width="160" height="19"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="19" id="Prr-fE-EFT"/>
                                                            <constraint firstAttribute="width" constant="160" id="cxk-A1-9US"/>
                                                        </constraints>
                                                    </customView>
                                                    <stackView distribution="fill" orientation="vertical" alignment="leading" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" verticalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="42U-If-Yqd">
                                                        <rect key="frame" x="255" y="0.0" width="195" height="19"/>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="42U-If-Yqd" firstAttribute="top" secondItem="83w-h7-hhH" secondAttribute="top" id="iv8-9p-mSw"/>
                                                </constraints>
                                                <visibilityPriorities>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                </visibilityPriorities>
                                                <customSpacing>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                </customSpacing>
                                            </stackView>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="height" id="89R-oK-9JB"/>
                                            <constraint firstItem="83w-h7-hhH" firstAttribute="leading" secondItem="lRr-k7-YZR" secondAttribute="leading" id="P2T-B5-KFP"/>
                                            <constraint firstAttribute="trailing" secondItem="83w-h7-hhH" secondAttribute="trailing" id="X1r-di-PDO"/>
                                            <constraint firstAttribute="trailing" secondItem="nsh-GW-hpI" secondAttribute="trailing" id="Y1m-rF-C5Q"/>
                                            <constraint firstItem="nsh-GW-hpI" firstAttribute="leading" secondItem="lRr-k7-YZR" secondAttribute="leading" id="hme-DZ-Fhn"/>
                                        </constraints>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                    <box verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="t10-e5-ZGI">
                                        <rect key="frame" x="0.0" y="108" width="450" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="20" id="Ujh-NX-kuq"/>
                                        </constraints>
                                    </box>
                                    <stackView distribution="fill" orientation="vertical" alignment="leading" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="sGP-9h-TJl" userLabel="Stage View">
                                        <rect key="frame" x="0.0" y="30" width="450" height="70"/>
                                        <subviews>
                                            <stackView distribution="fill" orientation="horizontal" alignment="top" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Yf4-Ks-DJM">
                                                <rect key="frame" x="0.0" y="50" width="450" height="20"/>
                                                <subviews>
                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="K3R-Hk-tq8">
                                                        <rect key="frame" x="-2" y="4" width="202" height="16"/>
                                                        <textFieldCell key="cell" lineBreakMode="clipping" title="Stage Manager recent apps area" id="ayu-YO-10p">
                                                            <font key="font" metaFont="system"/>
                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                        </textFieldCell>
                                                    </textField>
                                                    <slider verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="JTT-40-Rtw" userLabel="Stage Slider">
                                                        <rect key="frame" x="204" y="-6" width="199" height="28"/>
                                                        <sliderCell key="cell" state="on" alignment="left" maxValue="250" doubleValue="190" tickMarkPosition="above" sliderType="linear" id="VER-nt-CGZ"/>
                                                        <connections>
                                                            <action selector="stageSliderChanged:" target="yhc-gS-h02" id="H5H-Mc-y4r"/>
                                                        </connections>
                                                    </slider>
                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="751" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="eJ0-SU-XN3" userLabel="Stage Label">
                                                        <rect key="frame" x="407" y="4" width="45" height="16"/>
                                                        <textFieldCell key="cell" lineBreakMode="clipping" title="190 px" id="AEr-NX-9wW">
                                                            <font key="font" metaFont="system"/>
                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                        </textFieldCell>
                                                    </textField>
                                                </subviews>
                                                <visibilityPriorities>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                </visibilityPriorities>
                                                <customSpacing>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                </customSpacing>
                                            </stackView>
                                            <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="jKv-us-rNg">
                                                <rect key="frame" x="-2" y="28" width="264" height="14"/>
                                                <textFieldCell key="cell" title="If the area is too small, recent apps will be hidden" id="xE6-jw-EPt">
                                                    <font key="font" metaFont="message" size="11"/>
                                                    <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                </textFieldCell>
                                            </textField>
                                            <box verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="MZ0-H8-zIy">
                                                <rect key="frame" x="0.0" y="-2" width="450" height="24"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="20" id="OPh-xE-1zO"/>
                                                </constraints>
                                            </box>
                                        </subviews>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                    <stackView distribution="equalSpacing" orientation="horizontal" alignment="top" spacing="0.0" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="OhI-U9-bZb">
                                        <rect key="frame" x="0.0" y="0.0" width="450" height="20"/>
                                        <subviews>
                                            <button verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="An0-XX-dT6">
                                                <rect key="frame" x="-7" y="-7" width="276" height="32"/>
                                                <buttonCell key="cell" type="push" title="Restore Default Shortcuts &amp; Snap Areas" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="uLF-Uf-tBt">
                                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                    <font key="font" metaFont="system"/>
                                                </buttonCell>
                                                <connections>
                                                    <action selector="restoreDefaults:" target="yhc-gS-h02" id="Xfb-Cc-xFM"/>
                                                </connections>
                                            </button>
                                            <stackView distribution="fill" orientation="horizontal" alignment="top" spacing="10" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="TgL-lF-gWJ">
                                                <rect key="frame" x="280" y="0.0" width="170" height="20"/>
                                                <subviews>
                                                    <button verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="1qy-0d-Se1">
                                                        <rect key="frame" x="-7" y="-7" width="94" height="32"/>
                                                        <buttonCell key="cell" type="push" title="Import" bezelStyle="rounded" image="square.and.arrow.down" imagePosition="left" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="RgZ-Jw-XQZ">
                                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                            <font key="font" metaFont="system"/>
                                                        </buttonCell>
                                                        <connections>
                                                            <action selector="importConfig:" target="yhc-gS-h02" id="GGM-3r-onY"/>
                                                        </connections>
                                                    </button>
                                                    <button verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="hJG-z6-2Z1">
                                                        <rect key="frame" x="83" y="-7" width="94" height="32"/>
                                                        <buttonCell key="cell" type="push" title="Export" bezelStyle="rounded" image="square.and.arrow.up" imagePosition="left" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="IO3-Hi-7gC">
                                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                            <font key="font" metaFont="system"/>
                                                        </buttonCell>
                                                        <connections>
                                                            <action selector="exportConfig:" target="yhc-gS-h02" id="UTa-bz-fXM"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                                <visibilityPriorities>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                </visibilityPriorities>
                                                <customSpacing>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                </customSpacing>
                                            </stackView>
                                        </subviews>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="width" priority="999" constant="450" id="Db3-zB-dtQ"/>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="450" id="OsX-gR-mRY"/>
                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="150" id="UiW-yY-DW4"/>
                                    <constraint firstAttribute="height" priority="750" constant="150" id="bDD-of-gYL"/>
                                </constraints>
                                <visibilityPriorities>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                </visibilityPriorities>
                                <customSpacing>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                </customSpacing>
                            </stackView>
                        </subviews>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="tUi-Ja-evb" secondAttribute="bottom" constant="20" symbolic="YES" id="8bF-fp-d6W"/>
                            <constraint firstItem="tUi-Ja-evb" firstAttribute="leading" secondItem="mTk-eQ-4uf" secondAttribute="leading" constant="20" symbolic="YES" id="PU9-U5-pGX"/>
                            <constraint firstAttribute="trailing" secondItem="tUi-Ja-evb" secondAttribute="trailing" constant="20" symbolic="YES" id="R8R-By-UBl"/>
                            <constraint firstItem="tUi-Ja-evb" firstAttribute="top" secondItem="mTk-eQ-4uf" secondAttribute="top" constant="20" symbolic="YES" id="bII-EW-hnd"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="allowAnyShortcutCheckbox" destination="UY7-Nt-G3K" id="0dH-iu-gSs"/>
                        <outlet property="checkForUpdatesAutomaticallyCheckbox" destination="HcU-0y-wJU" id="ueY-S8-Ec0"/>
                        <outlet property="checkForUpdatesButton" destination="bn6-rz-AHw" id="9jf-az-Lvw"/>
                        <outlet property="cursorAcrossCheckbox" destination="WUS-pH-Rxv" id="xTd-uX-foF"/>
                        <outlet property="cycleSizesView" destination="er1-EQ-YIF" id="yHn-9t-gqI"/>
                        <outlet property="cycleSizesViewHeightConstraint" destination="IXv-8o-a6i" id="zjD-0U-P8y"/>
                        <outlet property="doubleClickTitleBarCheckbox" destination="hB7-uu-XeP" id="eNC-NI-Wmg"/>
                        <outlet property="gapLabel" destination="jd8-SJ-lza" id="pqG-fr-nwW"/>
                        <outlet property="gapSlider" destination="O9H-ZD-bqL" id="H7T-4Y-g8e"/>
                        <outlet property="hideMenuBarIconCheckbox" destination="wBT-R4-q9s" id="bkO-zn-yAZ"/>
                        <outlet property="launchOnLoginCheckbox" destination="eQJ-O3-a8H" id="iF3-bq-l1j"/>
                        <outlet property="reflowTodoShortcutView" destination="QTZ-pv-mwo" id="VYl-Tv-gFy"/>
                        <outlet property="stageLabel" destination="eJ0-SU-XN3" id="NbI-hl-Tg2"/>
                        <outlet property="stageSlider" destination="JTT-40-Rtw" id="9UK-b3-ph7"/>
                        <outlet property="stageView" destination="sGP-9h-TJl" id="B72-cy-f3F"/>
                        <outlet property="subsequentExecutionPopUpButton" destination="pVH-s3-FHn" id="tdS-iJ-EXc"/>
                        <outlet property="todoAppSidePopUpButton" destination="rB8-CF-Yrt" id="oNn-QO-3IC"/>
                        <outlet property="todoAppWidthField" destination="9VW-Hc-lh2" id="o0g-BR-XOK"/>
                        <outlet property="todoCheckbox" destination="0PP-0x-QWc" id="F1A-VF-cT5"/>
                        <outlet property="todoView" destination="lRr-k7-YZR" id="Hcn-7a-WBS"/>
                        <outlet property="todoViewHeightConstraint" destination="89R-oK-9JB" id="xTV-rY-WiU"/>
                        <outlet property="toggleTodoShortcutView" destination="uca-0m-naE" id="tnB-hD-SfB"/>
                        <outlet property="unsnapRestoreButton" destination="9Ed-T3-hCA" id="3UI-gj-R73"/>
                        <outlet property="versionLabel" destination="Azi-Y9-9xa" id="P8e-ZA-J6X"/>
                        <outlet property="windowSnappingCheckbox" destination="3wO-pf-nsb" id="00K-AW-LYi"/>
                    </connections>
                </viewController>
                <customObject id="c9e-yT-Dp7" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="415" y="1330"/>
        </scene>
        <!--Window Controller-->
        <scene sceneID="lEt-lj-rQC">
            <objects>
                <windowController storyboardIdentifier="AccessibilityWindowController" id="GjB-3W-g3G" customClass="AccessibilityWindowController" customModule="Rectangle" customModuleProvider="target" sceneMemberID="viewController">
                    <window key="window" title="Authorize Rectangle" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" frameAutosaveName="" animationBehavior="default" titlebarAppearsTransparent="YES" titleVisibility="hidden" id="9JD-tZ-7jf">
                        <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" fullSizeContentView="YES"/>
                        <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
                        <rect key="contentRect" x="245" y="301" width="480" height="270"/>
                        <rect key="screenRect" x="0.0" y="0.0" width="1680" height="1025"/>
                        <connections>
                            <outlet property="delegate" destination="GjB-3W-g3G" id="mlD-s5-EEm"/>
                        </connections>
                    </window>
                    <connections>
                        <segue destination="5D9-0a-Mbi" kind="relationship" relationship="window.shadowedContentViewController" id="8wX-oi-1HB"/>
                    </connections>
                </windowController>
                <customObject id="8dD-1m-YGC" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="656" y="251"/>
        </scene>
        <!--Accessibility View Controller-->
        <scene sceneID="yOZ-qp-n9s">
            <objects>
                <viewController id="5D9-0a-Mbi" customClass="AccessibilityViewController" customModule="Rectangle" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" id="1ZR-Nv-NGc">
                        <rect key="frame" x="0.0" y="0.0" width="290" height="396"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <subviews>
                            <stackView distribution="fill" orientation="vertical" alignment="centerX" spacing="22" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="4I0-IC-dnS">
                                <rect key="frame" x="20" y="20" width="250" height="346"/>
                                <subviews>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="751" translatesAutoresizingMaskIntoConstraints="NO" id="8BY-qu-jQ8">
                                        <rect key="frame" x="27" y="320" width="196" height="26"/>
                                        <textFieldCell key="cell" lineBreakMode="clipping" title="Authorize Rectangle" id="iXo-XL-T6q">
                                            <font key="font" metaFont="system" size="22"/>
                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="5m6-XJ-hq8">
                                        <rect key="frame" x="95" y="238" width="60" height="60"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="60" id="KxT-kJ-YPv"/>
                                            <constraint firstAttribute="height" constant="60" id="svN-TS-n5K"/>
                                        </constraints>
                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="Untilted" id="dlp-3B-rHa"/>
                                    </imageView>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="749" translatesAutoresizingMaskIntoConstraints="NO" id="AdB-Z9-aJk">
                                        <rect key="frame" x="-2" y="184" width="254" height="32"/>
                                        <textFieldCell key="cell" alignment="center" title="Rectangle needs your permission to control your window positions." id="gyg-xl-dPn">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="749" translatesAutoresizingMaskIntoConstraints="NO" id="XRC-ST-jLi">
                                        <rect key="frame" x="-2" y="134" width="254" height="28"/>
                                        <textFieldCell key="cell" alignment="center" title="Go to System Preferences → Security &amp; Privacy → Privacy → Accessibility" id="lgE-cR-cQ5">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="LtJ-oN-QeI">
                                        <rect key="frame" x="33" y="88" width="184" height="27"/>
                                        <buttonCell key="cell" type="bevel" title="Open System Preferences" bezelStyle="regularSquare" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="iWV-c2-BJD">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="system"/>
                                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                                        </buttonCell>
                                        <connections>
                                            <action selector="openSystemPrefs:" target="5D9-0a-Mbi" id="6dM-UK-KBu"/>
                                        </connections>
                                    </button>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="TlJ-Di-kby">
                                        <rect key="frame" x="57" y="54" width="136" height="16"/>
                                        <textFieldCell key="cell" lineBreakMode="clipping" title="Enable Rectangle.app" id="t7n-mU-75I">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="749" translatesAutoresizingMaskIntoConstraints="NO" id="6GN-iB-M1L">
                                        <rect key="frame" x="-2" y="0.0" width="254" height="32"/>
                                        <textFieldCell key="cell" alignment="center" title="If the checkbox is disabled, click the padlock and enter your password" id="9XM-Zb-HEb">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" priority="750" constant="250" id="Txs-9d-Su6"/>
                                    <constraint firstAttribute="width" priority="750" constant="250" id="ija-bQ-0qq"/>
                                </constraints>
                                <visibilityPriorities>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                </visibilityPriorities>
                                <customSpacing>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                </customSpacing>
                            </stackView>
                        </subviews>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="4I0-IC-dnS" secondAttribute="bottom" constant="20" symbolic="YES" id="6VM-hI-r3b"/>
                            <constraint firstItem="4I0-IC-dnS" firstAttribute="leading" secondItem="1ZR-Nv-NGc" secondAttribute="leading" constant="20" symbolic="YES" id="Gcc-RY-bAG"/>
                            <constraint firstAttribute="trailing" secondItem="4I0-IC-dnS" secondAttribute="trailing" constant="20" symbolic="YES" id="avA-k7-RbF"/>
                            <constraint firstItem="4I0-IC-dnS" firstAttribute="top" secondItem="1ZR-Nv-NGc" secondAttribute="top" constant="30" id="jJz-xW-me7"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="openSysPrefsButton" destination="LtJ-oN-QeI" id="8qu-hY-Yqt"/>
                        <outlet property="padlockField" destination="6GN-iB-M1L" id="sDW-zz-so8"/>
                        <outlet property="sysPrefsPathField" destination="XRC-ST-jLi" id="dsy-lz-XdE"/>
                    </connections>
                </viewController>
                <customObject id="Pz3-OM-dTg" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="679" y="767"/>
        </scene>
        <!--View Controller-->
        <scene sceneID="s8a-91-b01">
            <objects>
                <viewController id="6vv-bW-t7F" sceneMemberID="viewController">
                    <view key="view" id="3mg-n6-fQy">
                        <rect key="frame" x="0.0" y="0.0" width="340" height="424"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <subviews>
                            <stackView distribution="fill" orientation="vertical" alignment="centerX" spacing="22" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="W6p-pE-JE0">
                                <rect key="frame" x="20" y="20" width="300" height="374"/>
                                <subviews>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="751" translatesAutoresizingMaskIntoConstraints="NO" id="rpO-9I-Owx">
                                        <rect key="frame" x="63" y="348" width="174" height="26"/>
                                        <textFieldCell key="cell" lineBreakMode="clipping" title="About Todo Mode" id="ZVi-DR-1zj">
                                            <font key="font" metaFont="system" size="22"/>
                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ylF-B2-MK0">
                                        <rect key="frame" x="120" y="266" width="60" height="60"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="60" id="Jog-8E-fGF"/>
                                            <constraint firstAttribute="width" constant="60" id="SAz-Qi-u4X"/>
                                        </constraints>
                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="Untilted" id="6Pu-jH-EHJ"/>
                                    </imageView>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="749" translatesAutoresizingMaskIntoConstraints="NO" id="F9O-o8-0gu">
                                        <rect key="frame" x="-2" y="212" width="304" height="32"/>
                                        <textFieldCell key="cell" alignment="center" title="Keep a chosen application visible on the right side of your primary screen at all times" id="N2U-pY-CLq">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <stackView distribution="fill" orientation="vertical" alignment="leading" spacing="22" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Di0-sy-qj8">
                                        <rect key="frame" x="7" y="82" width="287" height="108"/>
                                        <subviews>
                                            <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="749" translatesAutoresizingMaskIntoConstraints="NO" id="KUM-bh-8es">
                                                <rect key="frame" x="-2" y="92" width="291" height="16"/>
                                                <textFieldCell key="cell" alignment="left" title="1. Bring your chosen todo application frontmost" id="qze-7p-m1X">
                                                    <font key="font" metaFont="system"/>
                                                    <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                </textFieldCell>
                                            </textField>
                                            <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="749" translatesAutoresizingMaskIntoConstraints="NO" id="SZD-vs-5hS">
                                                <rect key="frame" x="-2" y="38" width="201" height="32"/>
                                                <textFieldCell key="cell" alignment="left" id="xNi-9K-fnJ">
                                                    <font key="font" metaFont="system"/>
                                                    <string key="title">2. In the Rectangle menu, select
"Use [Application] as Todo App"</string>
                                                    <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                </textFieldCell>
                                            </textField>
                                            <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="749" translatesAutoresizingMaskIntoConstraints="NO" id="WN5-xt-f1V">
                                                <rect key="frame" x="-2" y="0.0" width="278" height="16"/>
                                                <textFieldCell key="cell" alignment="left" title="3. In the Rectangle menu, enable Todo Mode." id="8dv-v2-SPu">
                                                    <font key="font" metaFont="system"/>
                                                    <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                </textFieldCell>
                                            </textField>
                                        </subviews>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="749" translatesAutoresizingMaskIntoConstraints="NO" id="s4a-Yd-3qn">
                                        <rect key="frame" x="-2" y="0.0" width="304" height="60"/>
                                        <textFieldCell key="cell" alignment="left" id="q9C-qZ-xw5">
                                            <font key="font" metaFont="cellTitle"/>
                                            <string key="title">While in Todo Mode, you can refresh the Todo Mode layout by selecting "Reflow Todo" in the Rectangle menu or executing the associated keyboard shortcut.</string>
                                            <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="width" priority="750" constant="300" id="Nxj-e2-I72"/>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" priority="750" constant="250" id="Qmv-zk-2UP"/>
                                </constraints>
                                <visibilityPriorities>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                </visibilityPriorities>
                                <customSpacing>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                </customSpacing>
                            </stackView>
                        </subviews>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="W6p-pE-JE0" secondAttribute="trailing" constant="20" symbolic="YES" id="2g1-LL-ZFM"/>
                            <constraint firstAttribute="bottom" secondItem="W6p-pE-JE0" secondAttribute="bottom" constant="20" symbolic="YES" id="Un6-Cf-6MY"/>
                            <constraint firstItem="W6p-pE-JE0" firstAttribute="leading" secondItem="3mg-n6-fQy" secondAttribute="leading" constant="20" symbolic="YES" id="pH9-6n-vh5"/>
                            <constraint firstItem="W6p-pE-JE0" firstAttribute="top" secondItem="3mg-n6-fQy" secondAttribute="top" constant="30" id="pbw-t3-8tK"/>
                        </constraints>
                    </view>
                </viewController>
                <customObject id="gYM-Vl-Ier" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1504" y="1362"/>
        </scene>
        <!--Window Controller-->
        <scene sceneID="vbx-pw-SUZ">
            <objects>
                <windowController storyboardIdentifier="AboutTodoWindowController" id="yaK-Dt-a87" sceneMemberID="viewController">
                    <window key="window" title="About Todo Mode" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" frameAutosaveName="" animationBehavior="default" titlebarAppearsTransparent="YES" titleVisibility="hidden" id="MDd-Sm-sUx">
                        <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" fullSizeContentView="YES"/>
                        <rect key="contentRect" x="425" y="462" width="480" height="270"/>
                        <rect key="screenRect" x="0.0" y="0.0" width="2560" height="1415"/>
                        <view key="contentView" id="IQ2-6g-d9Z">
                            <rect key="frame" x="0.0" y="0.0" width="480" height="270"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </view>
                        <connections>
                            <outlet property="delegate" destination="yaK-Dt-a87" id="Bwx-sa-dAG"/>
                        </connections>
                    </window>
                    <connections>
                        <segue destination="6vv-bW-t7F" kind="relationship" relationship="window.shadowedContentViewController" id="NZk-Jq-tJh"/>
                    </connections>
                </windowController>
                <customObject id="EuJ-07-wZi" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="973" y="1362"/>
        </scene>
        <!--Window Controller-->
        <scene sceneID="QT5-BH-OxD">
            <objects>
                <windowController storyboardIdentifier="WelcomeWindowController" id="QOv-q0-4lf" sceneMemberID="viewController">
                    <window key="window" title="Welcome!" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" frameAutosaveName="" animationBehavior="default" titlebarAppearsTransparent="YES" titleVisibility="hidden" id="HtH-yF-lBR">
                        <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" fullSizeContentView="YES"/>
                        <rect key="contentRect" x="245" y="301" width="480" height="270"/>
                        <rect key="screenRect" x="0.0" y="0.0" width="1680" height="1025"/>
                        <connections>
                            <outlet property="delegate" destination="QOv-q0-4lf" id="2a5-83-wY9"/>
                        </connections>
                    </window>
                    <connections>
                        <segue destination="suu-55-jFR" kind="relationship" relationship="window.shadowedContentViewController" id="naP-lh-keh"/>
                    </connections>
                </windowController>
                <customObject id="OlH-rB-ig8" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1196" y="356"/>
        </scene>
        <!--Welcome View Controller-->
        <scene sceneID="fYD-GK-ZAf">
            <objects>
                <viewController storyboardIdentifier="WelcomeViewController" id="suu-55-jFR" customClass="WelcomeViewController" customModule="Rectangle" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" id="BAr-aA-3ku">
                        <rect key="frame" x="0.0" y="0.0" width="290" height="314"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <subviews>
                            <stackView distribution="fill" orientation="vertical" alignment="centerX" spacing="22" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="YO5-zp-VfI">
                                <rect key="frame" x="20" y="20" width="250" height="264"/>
                                <subviews>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="C9S-bS-jaE">
                                        <rect key="frame" x="13" y="238" width="224" height="26"/>
                                        <textFieldCell key="cell" lineBreakMode="clipping" title="Welcome to Rectangle!" id="kYm-Ye-gOR">
                                            <font key="font" metaFont="system" size="22"/>
                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="749" translatesAutoresizingMaskIntoConstraints="NO" id="g2U-cD-gRF">
                                        <rect key="frame" x="-2" y="184" width="254" height="32"/>
                                        <textFieldCell key="cell" alignment="center" title="Please select your default shortcuts and behavior" id="gEd-S9-Cfp">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <stackView distribution="fill" orientation="vertical" alignment="centerX" spacing="22" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="NR8-n2-Yhs">
                                        <rect key="frame" x="60" y="100" width="131" height="62"/>
                                        <subviews>
                                            <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="mYO-is-OgK">
                                                <rect key="frame" x="-7" y="35" width="145" height="32"/>
                                                <buttonCell key="cell" type="push" title="Recommended" bezelStyle="rounded" image="Untilted" imagePosition="left" alignment="center" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="HOp-Kd-vhY">
                                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                    <font key="font" metaFont="system"/>
                                                    <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                                                </buttonCell>
                                                <connections>
                                                    <action selector="selectRecommended:" target="suu-55-jFR" id="yQE-qI-owh"/>
                                                </connections>
                                            </button>
                                            <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="6Dw-0R-Da9">
                                                <rect key="frame" x="-7" y="-7" width="145" height="32"/>
                                                <buttonCell key="cell" type="push" title="Spectacle" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="2XJ-Ca-9di">
                                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                    <font key="font" metaFont="system"/>
                                                </buttonCell>
                                                <connections>
                                                    <action selector="selectSpectacle:" target="suu-55-jFR" id="FqP-IO-RUQ"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="6Dw-0R-Da9" firstAttribute="trailing" secondItem="mYO-is-OgK" secondAttribute="trailing" id="RrI-ig-8Jn"/>
                                            <constraint firstItem="6Dw-0R-Da9" firstAttribute="leading" secondItem="mYO-is-OgK" secondAttribute="leading" id="rah-Kq-8aZ"/>
                                        </constraints>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="749" translatesAutoresizingMaskIntoConstraints="NO" id="Uey-tt-ecv">
                                        <rect key="frame" x="-2" y="50" width="254" height="28"/>
                                        <textFieldCell key="cell" alignment="center" title="Spectacle shortcuts are more likely to conflict with other shortcuts" id="kXi-dT-zSF">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="749" translatesAutoresizingMaskIntoConstraints="NO" id="Y0r-Cp-GEM">
                                        <rect key="frame" x="-2" y="0.0" width="254" height="28"/>
                                        <textFieldCell key="cell" alignment="center" title="Choosing Spectacle will also cycle 1/2, 2/3, and 1/3 window widths on repeated shortcuts" id="xcE-uL-2J0">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="width" priority="750" constant="250" id="9Te-qi-H02"/>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" priority="750" constant="250" id="Uhe-KD-lTI"/>
                                </constraints>
                                <visibilityPriorities>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                </visibilityPriorities>
                                <customSpacing>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                </customSpacing>
                            </stackView>
                        </subviews>
                        <constraints>
                            <constraint firstItem="YO5-zp-VfI" firstAttribute="top" secondItem="BAr-aA-3ku" secondAttribute="top" constant="30" id="6dg-pJ-fuJ"/>
                            <constraint firstAttribute="trailing" secondItem="YO5-zp-VfI" secondAttribute="trailing" constant="20" symbolic="YES" id="Ca7-3S-B6a"/>
                            <constraint firstItem="YO5-zp-VfI" firstAttribute="leading" secondItem="BAr-aA-3ku" secondAttribute="leading" constant="20" symbolic="YES" id="fXv-Pw-H1B"/>
                            <constraint firstAttribute="bottom" secondItem="YO5-zp-VfI" secondAttribute="bottom" constant="20" symbolic="YES" id="iug-er-2mw"/>
                        </constraints>
                    </view>
                </viewController>
                <customObject id="iTO-LQ-hyN" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1196" y="776"/>
        </scene>
        <!--Snap Area View Controller-->
        <scene sceneID="WiD-Tk-7pr">
            <objects>
                <viewController storyboardIdentifier="HookshotConfigViewController" id="t2d-Q7-RLy" customClass="SnapAreaViewController" customModule="Rectangle" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" id="yfQ-gV-dm5">
                        <rect key="frame" x="0.0" y="0.0" width="654" height="757"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <subviews>
                            <stackView distribution="fill" orientation="vertical" alignment="centerX" spacing="30" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="9T6-Lr-8m1">
                                <rect key="frame" x="20" y="30" width="614" height="697"/>
                                <subviews>
                                    <stackView distribution="fill" orientation="vertical" alignment="leading" spacing="20" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5k8-dN-bzX">
                                        <rect key="frame" x="20" y="557" width="574" height="140"/>
                                        <subviews>
                                            <stackView distribution="fill" orientation="vertical" alignment="leading" spacing="10" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5Le-Om-VLZ">
                                                <rect key="frame" x="0.0" y="20" width="267" height="120"/>
                                                <subviews>
                                                    <button verticalHuggingPriority="750" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="3wO-pf-nsb">
                                                        <rect key="frame" x="-2" y="103" width="189" height="18"/>
                                                        <buttonCell key="cell" type="check" title="Snap windows by dragging" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="1ui-PL-TkR">
                                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                            <font key="font" metaFont="system"/>
                                                        </buttonCell>
                                                        <connections>
                                                            <action selector="toggleWindowSnapping:" target="t2d-Q7-RLy" id="hiC-vy-gm4"/>
                                                        </connections>
                                                    </button>
                                                    <button verticalHuggingPriority="750" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="9Ed-T3-hCA">
                                                        <rect key="frame" x="-2" y="77" width="257" height="18"/>
                                                        <buttonCell key="cell" type="check" title="Restore window size when unsnapped" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="UZP-5q-D5Y">
                                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                            <font key="font" metaFont="system"/>
                                                        </buttonCell>
                                                        <connections>
                                                            <action selector="toggleUnsnapRestore:" target="t2d-Q7-RLy" id="3vt-Lw-NJs"/>
                                                        </connections>
                                                    </button>
                                                    <button verticalHuggingPriority="750" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="cbx-fa-jJh">
                                                        <rect key="frame" x="-2" y="51" width="132" height="18"/>
                                                        <buttonCell key="cell" type="check" title="Animate footprint" bezelStyle="regularSquare" imagePosition="left" inset="2" id="kx2-tZ-lk8">
                                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                            <font key="font" metaFont="system"/>
                                                        </buttonCell>
                                                        <connections>
                                                            <action selector="toggleAnimateFootprint:" target="t2d-Q7-RLy" id="qo7-sw-gbY"/>
                                                        </connections>
                                                    </button>
                                                    <button verticalHuggingPriority="750" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="idz-Fq-8Qx">
                                                        <rect key="frame" x="-2" y="25" width="126" height="18"/>
                                                        <buttonCell key="cell" type="check" title="Haptic feedback" bezelStyle="regularSquare" imagePosition="left" inset="2" id="r2Y-cY-tgn">
                                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                            <font key="font" metaFont="system"/>
                                                        </buttonCell>
                                                        <connections>
                                                            <action selector="toggleHapticFeedback:" target="t2d-Q7-RLy" id="fYe-DO-EgM"/>
                                                        </connections>
                                                    </button>
                                                    <button verticalHuggingPriority="750" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="vrs-iM-XVL" userLabel="Mission Control Dragging Checkbox">
                                                        <rect key="frame" x="-2" y="-1" width="269" height="18"/>
                                                        <buttonCell key="cell" type="check" title="Disable fast dragging to Mission Control" bezelStyle="regularSquare" imagePosition="left" inset="2" id="3hZ-Cs-EZ6">
                                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                            <font key="font" metaFont="system"/>
                                                        </buttonCell>
                                                        <connections>
                                                            <action selector="toggleMissionControlDragging:" target="t2d-Q7-RLy" id="iGt-Mk-K1Y"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                                <visibilityPriorities>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                </visibilityPriorities>
                                                <customSpacing>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                </customSpacing>
                                            </stackView>
                                            <box verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="RAB-qk-O6q">
                                                <rect key="frame" x="0.0" y="-2" width="96" height="4"/>
                                            </box>
                                        </subviews>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                    <stackView distribution="fill" orientation="horizontal" alignment="top" spacing="14" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Vcp-1H-jc9">
                                        <rect key="frame" x="23" y="336" width="568" height="191"/>
                                        <subviews>
                                            <stackView distribution="fill" orientation="vertical" alignment="leading" spacing="65" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="oWJ-Ie-4bk">
                                                <rect key="frame" x="0.0" y="1" width="170" height="190"/>
                                                <subviews>
                                                    <popUpButton tag="1" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Dvs-uM-6VU">
                                                        <rect key="frame" x="-3" y="166" width="177" height="25"/>
                                                        <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="EBt-5H-0z6" id="82y-37-rlM">
                                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                            <font key="font" metaFont="message"/>
                                                            <menu key="menu" id="g7p-EV-39L">
                                                                <items>
                                                                    <menuItem title="-" state="on" tag="-1" id="EBt-5H-0z6"/>
                                                                </items>
                                                            </menu>
                                                        </popUpButtonCell>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="170" id="YL6-f5-Cb2"/>
                                                        </constraints>
                                                        <connections>
                                                            <action selector="setLandscapeSnapArea:" target="t2d-Q7-RLy" id="PHq-Gs-ONZ"/>
                                                        </connections>
                                                    </popUpButton>
                                                    <popUpButton tag="4" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="jqR-m9-Kh7">
                                                        <rect key="frame" x="-3" y="81" width="177" height="25"/>
                                                        <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="xBU-le-7cX" id="4d5-Se-z1S">
                                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                            <font key="font" metaFont="message"/>
                                                            <menu key="menu" id="5WZ-bt-0Fn">
                                                                <items>
                                                                    <menuItem title="-" state="on" tag="-1" id="xBU-le-7cX"/>
                                                                </items>
                                                            </menu>
                                                        </popUpButtonCell>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="170" id="8eG-6N-9WW"/>
                                                        </constraints>
                                                        <connections>
                                                            <action selector="setLandscapeSnapArea:" target="t2d-Q7-RLy" id="al4-TZ-b2U"/>
                                                        </connections>
                                                    </popUpButton>
                                                    <popUpButton tag="6" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="bVZ-3z-DYF">
                                                        <rect key="frame" x="-3" y="-4" width="177" height="25"/>
                                                        <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="I97-ob-ue4" id="AkL-7b-8Up">
                                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                            <font key="font" metaFont="message"/>
                                                            <menu key="menu" id="hHJ-nm-Msn">
                                                                <items>
                                                                    <menuItem title="-" state="on" tag="-1" id="I97-ob-ue4"/>
                                                                </items>
                                                            </menu>
                                                        </popUpButtonCell>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="170" id="eeJ-SY-aXK"/>
                                                        </constraints>
                                                        <connections>
                                                            <action selector="setLandscapeSnapArea:" target="t2d-Q7-RLy" id="2yz-dG-Dod"/>
                                                        </connections>
                                                    </popUpButton>
                                                </subviews>
                                                <visibilityPriorities>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                </visibilityPriorities>
                                                <customSpacing>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                </customSpacing>
                                            </stackView>
                                            <stackView distribution="fill" orientation="vertical" alignment="centerX" spacing="13" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Hbp-4G-5mJ">
                                                <rect key="frame" x="184" y="0.0" width="200" height="191"/>
                                                <subviews>
                                                    <popUpButton tag="2" verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="qa7-5C-PSI">
                                                        <rect key="frame" x="12" y="167" width="177" height="25"/>
                                                        <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="NMu-W8-Awa" id="yaC-wx-qst">
                                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                            <font key="font" metaFont="message"/>
                                                            <menu key="menu" id="pf5-Uy-uaq">
                                                                <items>
                                                                    <menuItem title="-" state="on" tag="-1" id="NMu-W8-Awa"/>
                                                                </items>
                                                            </menu>
                                                        </popUpButtonCell>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="170" id="uMh-Lo-5N5"/>
                                                        </constraints>
                                                        <connections>
                                                            <action selector="setLandscapeSnapArea:" target="t2d-Q7-RLy" id="cXd-By-Jle"/>
                                                        </connections>
                                                    </popUpButton>
                                                    <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="EyR-o0-dgo">
                                                        <rect key="frame" x="-3" y="30" width="206" height="131"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="200" id="7YZ-ww-UVh"/>
                                                            <constraint firstAttribute="height" constant="125" id="Xte-V6-YWz"/>
                                                        </constraints>
                                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" imageFrameStyle="grayBezel" image="wallpaperTiger" id="EGh-Nz-7rJ"/>
                                                    </imageView>
                                                    <popUpButton tag="7" verticalHuggingPriority="750" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="3c6-3h-J52">
                                                        <rect key="frame" x="12" y="-4" width="177" height="25"/>
                                                        <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="r4Q-q9-FLt" id="FoW-W1-Wr4">
                                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                            <font key="font" metaFont="message"/>
                                                            <menu key="menu" id="fCp-nQ-uAe">
                                                                <items>
                                                                    <menuItem title="-" state="on" tag="-1" id="r4Q-q9-FLt"/>
                                                                </items>
                                                            </menu>
                                                        </popUpButtonCell>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="170" id="yaA-Id-Y1d"/>
                                                        </constraints>
                                                        <connections>
                                                            <action selector="setLandscapeSnapArea:" target="t2d-Q7-RLy" id="Wao-XB-BCD"/>
                                                        </connections>
                                                    </popUpButton>
                                                </subviews>
                                                <visibilityPriorities>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                </visibilityPriorities>
                                                <customSpacing>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                </customSpacing>
                                            </stackView>
                                            <stackView distribution="fill" orientation="vertical" alignment="trailing" spacing="65" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="H40-PM-o61">
                                                <rect key="frame" x="398" y="1" width="170" height="190"/>
                                                <subviews>
                                                    <popUpButton tag="3" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="f8h-NV-mPC">
                                                        <rect key="frame" x="-3" y="166" width="177" height="25"/>
                                                        <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="Tt8-3X-UpT" id="VUm-bx-uRE">
                                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                            <font key="font" metaFont="message"/>
                                                            <menu key="menu" id="O2T-B6-jyn">
                                                                <items>
                                                                    <menuItem title="-" state="on" tag="-1" id="Tt8-3X-UpT"/>
                                                                </items>
                                                            </menu>
                                                        </popUpButtonCell>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="170" id="LeD-YV-5Br"/>
                                                        </constraints>
                                                        <connections>
                                                            <action selector="setLandscapeSnapArea:" target="t2d-Q7-RLy" id="869-tM-t4p"/>
                                                        </connections>
                                                    </popUpButton>
                                                    <popUpButton tag="5" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="HM2-rd-Wka">
                                                        <rect key="frame" x="-3" y="81" width="177" height="25"/>
                                                        <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="oEQ-RN-TTJ" id="ycZ-Le-vhx">
                                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                            <font key="font" metaFont="message"/>
                                                            <menu key="menu" id="wSR-D9-eze">
                                                                <items>
                                                                    <menuItem title="-" state="on" tag="-1" id="oEQ-RN-TTJ"/>
                                                                </items>
                                                            </menu>
                                                        </popUpButtonCell>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="170" id="nK1-No-QVf"/>
                                                        </constraints>
                                                        <connections>
                                                            <action selector="setLandscapeSnapArea:" target="t2d-Q7-RLy" id="Q8o-7b-RSd"/>
                                                        </connections>
                                                    </popUpButton>
                                                    <popUpButton tag="8" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="jVF-3c-sL0">
                                                        <rect key="frame" x="-3" y="-4" width="177" height="25"/>
                                                        <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="KfQ-dw-3jk" id="7h2-cE-0M2">
                                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                            <font key="font" metaFont="message"/>
                                                            <menu key="menu" id="Sam-p4-uj0">
                                                                <items>
                                                                    <menuItem title="-" state="on" tag="-1" id="KfQ-dw-3jk"/>
                                                                </items>
                                                            </menu>
                                                        </popUpButtonCell>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="170" id="1Tr-s8-bxh"/>
                                                        </constraints>
                                                        <connections>
                                                            <action selector="setLandscapeSnapArea:" target="t2d-Q7-RLy" id="mtt-GM-BzO"/>
                                                        </connections>
                                                    </popUpButton>
                                                </subviews>
                                                <visibilityPriorities>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                </visibilityPriorities>
                                                <customSpacing>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                </customSpacing>
                                            </stackView>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="qa7-5C-PSI" firstAttribute="top" secondItem="Dvs-uM-6VU" secondAttribute="top" id="ARD-Up-4LA"/>
                                            <constraint firstItem="f8h-NV-mPC" firstAttribute="top" secondItem="Dvs-uM-6VU" secondAttribute="top" id="aid-5O-AzU"/>
                                        </constraints>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                    <stackView distribution="equalCentering" orientation="vertical" alignment="centerX" spacing="40" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="3fZ-2P-Yw8">
                                        <rect key="frame" x="40" y="0.0" width="534" height="306"/>
                                        <subviews>
                                            <box verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="PH1-h4-ZhC">
                                                <rect key="frame" x="65" y="304" width="404" height="4"/>
                                            </box>
                                            <stackView distribution="fill" orientation="horizontal" alignment="centerY" spacing="12" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="MnW-gb-EXP">
                                                <rect key="frame" x="0.0" y="0.0" width="534" height="266"/>
                                                <subviews>
                                                    <stackView distribution="equalCentering" orientation="vertical" alignment="leading" spacing="65" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="aRa-8h-39S">
                                                        <rect key="frame" x="0.0" y="0.0" width="170" height="266"/>
                                                        <subviews>
                                                            <popUpButton tag="1" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Ytt-rI-KMq">
                                                                <rect key="frame" x="-3" y="242" width="177" height="25"/>
                                                                <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="xK0-Bn-yqf" id="RTg-Kq-V2E">
                                                                    <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                                    <font key="font" metaFont="message"/>
                                                                    <menu key="menu" id="HxQ-fS-Z8p">
                                                                        <items>
                                                                            <menuItem title="-" state="on" tag="-1" id="xK0-Bn-yqf"/>
                                                                        </items>
                                                                    </menu>
                                                                </popUpButtonCell>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="170" id="VhY-YE-zqg"/>
                                                                </constraints>
                                                                <connections>
                                                                    <action selector="setPortraitSnapArea:" target="t2d-Q7-RLy" id="ctL-Ka-Z7V"/>
                                                                </connections>
                                                            </popUpButton>
                                                            <popUpButton tag="4" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="NlH-2s-Bb2">
                                                                <rect key="frame" x="-3" y="119" width="177" height="25"/>
                                                                <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="5WR-Ui-uYB" id="pg4-zC-ivJ">
                                                                    <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                                    <font key="font" metaFont="message"/>
                                                                    <menu key="menu" id="H9m-bZ-eKV">
                                                                        <items>
                                                                            <menuItem title="-" state="on" tag="-1" id="5WR-Ui-uYB"/>
                                                                        </items>
                                                                    </menu>
                                                                </popUpButtonCell>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="170" id="McE-Ba-5jp"/>
                                                                </constraints>
                                                                <connections>
                                                                    <action selector="setPortraitSnapArea:" target="t2d-Q7-RLy" id="Xnn-6W-ToT"/>
                                                                </connections>
                                                            </popUpButton>
                                                            <popUpButton tag="6" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="DPC-eZ-HIy">
                                                                <rect key="frame" x="-3" y="-4" width="177" height="25"/>
                                                                <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="Q2j-YZ-1jU" id="JXR-La-eQ0">
                                                                    <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                                    <font key="font" metaFont="message"/>
                                                                    <menu key="menu" id="WNl-e0-w5C">
                                                                        <items>
                                                                            <menuItem title="-" state="on" tag="-1" id="Q2j-YZ-1jU"/>
                                                                        </items>
                                                                    </menu>
                                                                </popUpButtonCell>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="170" id="g8b-oS-9oQ"/>
                                                                </constraints>
                                                                <connections>
                                                                    <action selector="setPortraitSnapArea:" target="t2d-Q7-RLy" id="sZU-iY-Tjs"/>
                                                                </connections>
                                                            </popUpButton>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="equalCentering" orientation="vertical" alignment="centerX" spacing="13" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="3Ki-JA-AZb">
                                                        <rect key="frame" x="182" y="0.0" width="170" height="266"/>
                                                        <subviews>
                                                            <popUpButton tag="2" verticalHuggingPriority="750" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="rs4-Tg-Omn">
                                                                <rect key="frame" x="-3" y="242" width="177" height="25"/>
                                                                <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="2ue-Mv-5Wh" id="4gU-9A-TnP">
                                                                    <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                                    <font key="font" metaFont="message"/>
                                                                    <menu key="menu" id="xr0-Bg-vHD">
                                                                        <items>
                                                                            <menuItem title="-" state="on" tag="-1" id="2ue-Mv-5Wh"/>
                                                                        </items>
                                                                    </menu>
                                                                </popUpButtonCell>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="170" id="ROc-Ns-vAP"/>
                                                                </constraints>
                                                                <connections>
                                                                    <action selector="setPortraitSnapArea:" target="t2d-Q7-RLy" id="QvF-QG-HdI"/>
                                                                </connections>
                                                            </popUpButton>
                                                            <imageView horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ZBk-gP-LPR">
                                                                <rect key="frame" x="20" y="30" width="131" height="206"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="200" id="L6K-rA-7ef"/>
                                                                    <constraint firstAttribute="width" constant="125" id="q6Z-S0-e4Z"/>
                                                                </constraints>
                                                                <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" imageFrameStyle="grayBezel" image="wallpaperTigerVertical" id="uxh-iv-ahv"/>
                                                            </imageView>
                                                            <popUpButton tag="7" verticalHuggingPriority="750" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="ZYO-D7-C2a">
                                                                <rect key="frame" x="-3" y="-4" width="177" height="25"/>
                                                                <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="FLh-CI-WNK" id="8Ck-e6-dvF">
                                                                    <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                                    <font key="font" metaFont="message"/>
                                                                    <menu key="menu" id="kwv-U1-cQe">
                                                                        <items>
                                                                            <menuItem title="-" state="on" tag="-1" id="FLh-CI-WNK"/>
                                                                        </items>
                                                                    </menu>
                                                                </popUpButtonCell>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="170" id="mPN-N7-3n2"/>
                                                                </constraints>
                                                                <connections>
                                                                    <action selector="setPortraitSnapArea:" target="t2d-Q7-RLy" id="ArK-3M-bC2"/>
                                                                </connections>
                                                            </popUpButton>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                    <stackView distribution="equalCentering" orientation="vertical" alignment="centerX" spacing="65" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="7xm-ZM-Gqk">
                                                        <rect key="frame" x="364" y="0.0" width="170" height="266"/>
                                                        <subviews>
                                                            <popUpButton tag="3" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="pYw-mV-KH2">
                                                                <rect key="frame" x="-3" y="242" width="177" height="25"/>
                                                                <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="qBm-QP-s63" id="GEM-Db-kKE">
                                                                    <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                                    <font key="font" metaFont="message"/>
                                                                    <menu key="menu" id="432-ys-RXR">
                                                                        <items>
                                                                            <menuItem title="-" state="on" tag="-1" id="qBm-QP-s63"/>
                                                                        </items>
                                                                    </menu>
                                                                </popUpButtonCell>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="170" id="xY9-Lp-sSm"/>
                                                                </constraints>
                                                                <connections>
                                                                    <action selector="setPortraitSnapArea:" target="t2d-Q7-RLy" id="U8d-ad-SCC"/>
                                                                </connections>
                                                            </popUpButton>
                                                            <popUpButton tag="5" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="efR-xi-okh">
                                                                <rect key="frame" x="-3" y="119" width="177" height="25"/>
                                                                <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="JSl-FY-MF0" id="sKz-wv-Rtz">
                                                                    <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                                    <font key="font" metaFont="message"/>
                                                                    <menu key="menu" id="u14-Im-koA">
                                                                        <items>
                                                                            <menuItem title="-" state="on" tag="-1" id="JSl-FY-MF0"/>
                                                                        </items>
                                                                    </menu>
                                                                </popUpButtonCell>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="170" id="D5l-DR-cPS"/>
                                                                </constraints>
                                                                <connections>
                                                                    <action selector="setPortraitSnapArea:" target="t2d-Q7-RLy" id="Yza-RI-8mz"/>
                                                                </connections>
                                                            </popUpButton>
                                                            <popUpButton tag="8" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="lje-Rc-9Hj">
                                                                <rect key="frame" x="-3" y="-4" width="177" height="25"/>
                                                                <popUpButtonCell key="cell" type="push" title="-" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="-1" imageScaling="proportionallyDown" inset="2" selectedItem="fL5-gz-pOD" id="X4M-df-26I">
                                                                    <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                                    <font key="font" metaFont="message"/>
                                                                    <menu key="menu" id="8lV-Zy-44g">
                                                                        <items>
                                                                            <menuItem title="-" state="on" tag="-1" id="fL5-gz-pOD"/>
                                                                        </items>
                                                                    </menu>
                                                                </popUpButtonCell>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="170" id="0oQ-OQ-cnQ"/>
                                                                </constraints>
                                                                <connections>
                                                                    <action selector="setPortraitSnapArea:" target="t2d-Q7-RLy" id="OH3-Ry-3Ss"/>
                                                                </connections>
                                                            </popUpButton>
                                                        </subviews>
                                                        <visibilityPriorities>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                            <integer value="1000"/>
                                                        </visibilityPriorities>
                                                        <customSpacing>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                            <real value="3.4028234663852886e+38"/>
                                                        </customSpacing>
                                                    </stackView>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstItem="pYw-mV-KH2" firstAttribute="top" secondItem="Ytt-rI-KMq" secondAttribute="top" id="7kg-bT-uCf"/>
                                                    <constraint firstItem="rs4-Tg-Omn" firstAttribute="top" secondItem="Ytt-rI-KMq" secondAttribute="top" id="Mci-KR-QSG"/>
                                                </constraints>
                                                <visibilityPriorities>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                    <integer value="1000"/>
                                                </visibilityPriorities>
                                                <customSpacing>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                    <real value="3.4028234663852886e+38"/>
                                                </customSpacing>
                                            </stackView>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="PH1-h4-ZhC" firstAttribute="leading" secondItem="3fZ-2P-Yw8" secondAttribute="leading" constant="65" id="Dlt-CM-UWe"/>
                                        </constraints>
                                        <visibilityPriorities>
                                            <integer value="1000"/>
                                            <integer value="1000"/>
                                        </visibilityPriorities>
                                        <customSpacing>
                                            <real value="3.4028234663852886e+38"/>
                                            <real value="3.4028234663852886e+38"/>
                                        </customSpacing>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="100" id="0p9-e7-C6B"/>
                                    <constraint firstItem="5k8-dN-bzX" firstAttribute="leading" secondItem="9T6-Lr-8m1" secondAttribute="leading" constant="20" symbolic="YES" id="4jz-c4-0m9"/>
                                    <constraint firstAttribute="height" priority="750" constant="100" id="Gde-pc-Mrq"/>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="306" id="PeA-h0-vh6"/>
                                    <constraint firstAttribute="width" priority="750" constant="306" id="aYF-GN-dmL"/>
                                    <constraint firstItem="3fZ-2P-Yw8" firstAttribute="centerX" secondItem="9T6-Lr-8m1" secondAttribute="centerX" id="gIv-0a-YeJ"/>
                                    <constraint firstItem="3fZ-2P-Yw8" firstAttribute="leading" secondItem="9T6-Lr-8m1" secondAttribute="leading" constant="40" id="jfY-5s-dFp"/>
                                </constraints>
                                <visibilityPriorities>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                    <integer value="1000"/>
                                </visibilityPriorities>
                                <customSpacing>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                    <real value="3.4028234663852886e+38"/>
                                </customSpacing>
                            </stackView>
                        </subviews>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="9T6-Lr-8m1" secondAttribute="trailing" constant="20" symbolic="YES" id="9n5-RF-cpZ"/>
                            <constraint firstItem="9T6-Lr-8m1" firstAttribute="leading" secondItem="yfQ-gV-dm5" secondAttribute="leading" constant="20" symbolic="YES" id="I1m-Ek-y93"/>
                            <constraint firstItem="9T6-Lr-8m1" firstAttribute="top" secondItem="yfQ-gV-dm5" secondAttribute="top" constant="30" id="KZ2-wj-yLC"/>
                            <constraint firstAttribute="bottom" secondItem="9T6-Lr-8m1" secondAttribute="bottom" constant="30" id="ftC-22-GdB"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="animateFootprintCheckbox" destination="cbx-fa-jJh" id="KmN-pe-s68"/>
                        <outlet property="bottomLandscapeSelect" destination="3c6-3h-J52" id="1a0-LW-wrh"/>
                        <outlet property="bottomLeftLandscapeSelect" destination="bVZ-3z-DYF" id="1Ql-jU-1fz"/>
                        <outlet property="bottomLeftPortraitSelect" destination="DPC-eZ-HIy" id="jdO-8U-dyU"/>
                        <outlet property="bottomPortraitSelect" destination="ZYO-D7-C2a" id="pZd-mp-0yk"/>
                        <outlet property="bottomRightLandscapeSelect" destination="jVF-3c-sL0" id="eYi-gy-RF3"/>
                        <outlet property="bottomRightPortraitSelect" destination="lje-Rc-9Hj" id="bxP-nE-cgS"/>
                        <outlet property="hapticFeedbackCheckbox" destination="idz-Fq-8Qx" id="5Wb-6e-kmA"/>
                        <outlet property="leftLandscapeSelect" destination="jqR-m9-Kh7" id="wBI-Th-vtz"/>
                        <outlet property="leftPortraitSelect" destination="NlH-2s-Bb2" id="r9T-BM-YbN"/>
                        <outlet property="missionControlDraggingCheckbox" destination="vrs-iM-XVL" id="xgC-0R-SeA"/>
                        <outlet property="portraitStackView" destination="3fZ-2P-Yw8" id="b8R-Wb-nlY"/>
                        <outlet property="rightLandscapeSelect" destination="HM2-rd-Wka" id="Oud-Xf-anx"/>
                        <outlet property="rightPortraitSelect" destination="efR-xi-okh" id="K5g-I0-0e4"/>
                        <outlet property="topLandscapeSelect" destination="qa7-5C-PSI" id="vmH-jq-zMx"/>
                        <outlet property="topLeftLandscapeSelect" destination="Dvs-uM-6VU" id="Z3D-Pn-896"/>
                        <outlet property="topLeftPortraitSelect" destination="Ytt-rI-KMq" id="e9V-3B-ws1"/>
                        <outlet property="topPortraitSelect" destination="rs4-Tg-Omn" id="GT1-5g-pK7"/>
                        <outlet property="topRightLandscapeSelect" destination="f8h-NV-mPC" id="bC7-di-TTY"/>
                        <outlet property="topRightPortraitSelect" destination="pYw-mV-KH2" id="m2S-dc-ncv"/>
                        <outlet property="unsnapRestoreButton" destination="9Ed-T3-hCA" id="6Qf-nc-VWE"/>
                        <outlet property="windowSnappingCheckbox" destination="3wO-pf-nsb" id="INd-4Z-LaK"/>
                    </connections>
                </viewController>
                <customObject id="bNa-zL-IZp" userLabel="First Responder" customClass="NSResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-202" y="1400"/>
        </scene>
    </scenes>
    <resources>
        <image name="Untilted" width="256" height="256"/>
        <image name="almostMaximizeTemplate" width="30" height="20"/>
        <image name="bottomCenterSixthTemplate" width="30" height="20"/>
        <image name="bottomHalfTemplate" width="30" height="20"/>
        <image name="bottomLeftSixthTemplate" width="30" height="20"/>
        <image name="bottomLeftTemplate" width="30" height="20"/>
        <image name="bottomRightSixthTemplate" width="30" height="20"/>
        <image name="bottomRightTemplate" width="30" height="20"/>
        <image name="centerLeftFourthTemplate" width="30" height="20"/>
        <image name="centerRightFourthTemplate" width="30" height="20"/>
        <image name="centerTemplate" width="30" height="20"/>
        <image name="centerThirdTemplate" width="30" height="20"/>
        <image name="firstThirdTemplate" width="30" height="20"/>
        <image name="firstThreeFourthsTemplate" width="30" height="20"/>
        <image name="firstTwoThirdsTemplate" width="30" height="20"/>
        <image name="halfWidthCenterTemplate" width="30" height="20"/>
        <image name="keyboardToolbarTemplate" width="72" height="72"/>
        <image name="lastThirdTemplate" width="30" height="20"/>
        <image name="lastThreeFourthsTemplate" width="30" height="20"/>
        <image name="lastTwoThirdsTemplate" width="30" height="20"/>
        <image name="leftFourthTemplate" width="30" height="20"/>
        <image name="leftHalfTemplate" width="30" height="20"/>
        <image name="makeLargerTemplate" width="30" height="20"/>
        <image name="makeSmallerTemplate" width="30" height="20"/>
        <image name="maximizeHeightTemplate" width="30" height="20"/>
        <image name="maximizeTemplate" width="30" height="20"/>
        <image name="moveDownTemplate" width="30" height="20"/>
        <image name="moveLeftTemplate" width="30" height="20"/>
        <image name="moveRightTemplate" width="30" height="20"/>
        <image name="moveUpTemplate" width="30" height="20"/>
        <image name="nextDisplayTemplate" width="30" height="20"/>
        <image name="prevDisplayTemplate" width="30" height="20"/>
        <image name="restoreTemplate" width="30" height="20"/>
        <image name="rightFourthTemplate" width="30" height="20"/>
        <image name="rightHalfTemplate" width="30" height="20"/>
        <image name="snapAreaTemplate" width="72" height="72"/>
        <image name="square.and.arrow.down" width="55" height="56"/>
        <image name="square.and.arrow.up" width="55" height="56"/>
        <image name="toolbarSettingsTemplate" width="50" height="50"/>
        <image name="topCenterSixthTemplate" width="30" height="20"/>
        <image name="topHalfTemplate" width="30" height="20"/>
        <image name="topLeftSixthTemplate" width="30" height="20"/>
        <image name="topLeftTemplate" width="30" height="20"/>
        <image name="topRightSixthTemplate" width="30" height="20"/>
        <image name="topRightTemplate" width="30" height="20"/>
        <image name="wallpaperTiger" width="400" height="250"/>
        <image name="wallpaperTigerVertical" width="250" height="400"/>
    </resources>
</document>
