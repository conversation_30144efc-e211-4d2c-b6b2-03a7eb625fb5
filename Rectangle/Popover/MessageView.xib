<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="13771" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES" customObjectInstantitationMethod="direct">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="13771"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="MessageView" customModule="Multitouch" customModuleProvider="target">
            <connections>
                <outlet property="messageField" destination="ds5-LV-T8n" id="pqA-IN-h0N"/>
                <outlet property="view" destination="c22-O7-iKe" id="LLD-Ih-329"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <customView id="c22-O7-iKe">
            <rect key="frame" x="0.0" y="0.0" width="195" height="34"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
            <subviews>
                <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="ds5-LV-T8n">
                    <rect key="frame" x="8" y="10" width="179" height="14"/>
                    <textFieldCell key="cell" controlSize="mini" sendsActionOnEndEditing="YES" alignment="left" title="Multiline Label" id="w43-yH-KSN">
                        <font key="font" metaFont="smallSystem"/>
                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
            </subviews>
            <constraints>
                <constraint firstItem="ds5-LV-T8n" firstAttribute="leading" secondItem="c22-O7-iKe" secondAttribute="leading" constant="10" id="Lij-lR-R0h"/>
                <constraint firstAttribute="trailing" secondItem="ds5-LV-T8n" secondAttribute="trailing" constant="10" id="Zgb-ou-LI7"/>
                <constraint firstAttribute="bottom" secondItem="ds5-LV-T8n" secondAttribute="bottom" constant="10" id="hWM-KZ-zjk"/>
                <constraint firstItem="ds5-LV-T8n" firstAttribute="top" secondItem="c22-O7-iKe" secondAttribute="top" constant="10" id="kgf-9U-ATL"/>
            </constraints>
            <point key="canvasLocation" x="-3.5" y="89.5"/>
        </customView>
    </objects>
</document>
