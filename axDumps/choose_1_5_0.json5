{
  "AXActivationPoint" : "<AXValue 0x600003dc6340> {value = x:510.000000 y:243.000000 type = kAXValueCGPointType}",
  "AXCancelButton" : null,
  "AXCloseButton" : null,
  "AXDefaultButton" : null,
  "AXDocument" : null,
  "AXFocused" : 0,
  "AXFrame" : "<AXValue 0x60000269ae40> {value = x:500.000000 y:229.000000 w:800.000000 h:387.000000 type = kAXValueCGRectType}",
  "AXFullScreen" : 0,
  "AXFullScreenButton" : null,
  "AXGrowArea" : null,
  "AXMain" : 1,
  "AXMinimizeButton" : null,
  "AXMinimized" : 0,
  "AXModal" : 0,
  "AXParent" : "<AXUIElement Application 0x600003dc6340> {pid=14046}",
  "AXPosition" : "<AXValue 0x600003df8930> {value = x:500.000000 y:229.000000 type = kAXValueCGPointType}",
  "AXProxy" : null,
  "AXRole" : "AXWindow",
  "AXSections" : [
    "{\n    SectionDescription = Content;\n    SectionObject = \"<AXUIElement 0x600003dc6e50> {pid=14046}\";\n    SectionUniqueID = AXContent;\n}",
    "{\n    SectionObject = \"<AXUIElement 0x600003dc7720> {pid=14046}\";\n    SectionUniqueID = AXContainer;\n}"
  ],
  "AXSize" : "<AXValue 0x600003df8570> {value = w:800.000000 h:387.000000 type = kAXValueCGSizeType}",
  "AXSubrole" : "AXStandardWindow",
  "AXTitle" : "",
  "AXTitleUIElement" : "AXUIElement(AxWindowId=836, title=nil, role=\"AXStaticText\", subrole=nil)",
  "AXToolbarButton" : null,
  "AXZoomButton" : null,
  "Aero.AXApp" : {
    "AXExtrasMenuBar" : null,
    "AXFocusedUIElement" : "AXUIElement(AxWindowId=836, title=nil, role=\"AXTextField\", subrole=nil)",
    "AXFocusedWindow" : "AXUIElement(AxWindowId=836, title=\"\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXFrame" : null,
    "AXFrontmost" : 1,
    "AXFunctionRowTopLevelElements" : [

    ],
    "AXMainWindow" : "AXUIElement(AxWindowId=836, title=\"\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXMenuBar" : "<AXUIElement 0x600003dc74e0> {pid=14046}",
    "AXPosition" : null,
    "AXRole" : "AXApplication",
    "AXSize" : null,
    "AXTitle" : "choose",
    "AXWindows" : [
      "AXUIElement(AxWindowId=836, title=\"\", role=\"AXWindow\", subrole=\"AXStandardWindow\")"
    ],
    "Aero.AxIgnored" : "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"
  },
  "Aero.App.appBundleId" : null,
  "Aero.App.nsApp.activationPolicy" : "accessory",
  "Aero.App.version" : null, // choose -v => 1.5.0
  "Aero.App.versionShort" : null,
  "Aero.AxIgnored" : "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription",
  "Aero.axWindowId" : 836,
  "Aero.isDialogHeuristic" : true,
  "Aero.isWindowHeuristic" : true,
  "Aero.treeNodeParent" : "AppBundle.Workspace",
  "Aero.workspace" : "M"
}
