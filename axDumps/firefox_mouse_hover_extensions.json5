{"AXActivationPoint": "<AXValue 0x6000019fbf60> {value = x:-1.000000 y:1170.000000 type = kAXValueCGPointType}", "AXCancelButton": null, "AXCloseButton": null, "AXDefaultButton": null, "AXDocument": null, "AXFocused": 0, "AXFrame": "<AXValue 0x60000029ff80> {value = x:1583.000000 y:125.000000 w:69.000000 h:18.000000 type = kAXValueCGRectType}", "AXFullScreen": 0, "AXFullScreenButton": null, "AXGrowArea": null, "AXMain": 0, "AXMinimizeButton": null, "AXMinimized": 0, "AXModal": 0, "AXParent": "<AXUIElement Application 0x6000019fbf60> {pid=1405}", "AXPosition": "<AXValue 0x60000193af70> {value = x:1583.000000 y:125.000000 type = kAXValueCGPointType}", "AXProxy": null, "AXRole": "AXWindow", "AXSections": [], "AXSize": "<AXValue 0x6000019fbf60> {value = w:69.000000 h:18.000000 type = kAXValueCGSizeType}", "AXSubrole": "AXUnknown", "AXTitle": "Extensions", "AXTitleUIElement": null, "AXToolbarButton": null, "AXZoomButton": null, "Aero.AXApp": {"AXExtrasMenuBar": null, "AXFocusedUIElement": "AXUIElement(AxWindowId=13054, title=nil, role=\"AXWebArea\", subrole=nil)", "AXFocusedWindow": "AXUIElement(AxWindowId=13054, title=\"The small float tips in firefox will be trated as a window in v0.17.0-Beta · nikitabobko/AeroSpace · Discussion #1115\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXFrame": null, "AXFrontmost": 1, "AXFunctionRowTopLevelElements": [], "AXMainWindow": "AXUIElement(AxWindowId=13054, title=\"The small float tips in firefox will be trated as a window in v0.17.0-Beta · nikitabobko/AeroSpace · Discussion #1115\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXMenuBar": "<AXUIElement 0x60000193bbd0> {pid=1405}", "AXPosition": null, "AXRole": "AXApplication", "AXSize": null, "AXTitle": "Firefox", "AXWindows": ["AXUIElement(AxWindowId=13196, title=\"Extensions\", role=\"AXWindow\", subrole=\"AXUnknown\")", "AXUIElement(AxWindowId=13054, title=\"The small float tips in firefox will be trated as a window in v0.17.0-Beta · nikitabobko/AeroSpace · Discussion #1115\", role=\"AXWindow\", subrole=\"AXStandardWindow\")"], "Aero.AxIgnored": "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"}, "Aero.App.appBundleId": "org.mozilla.firefox", "Aero.App.nsApp.activationPolicy": "regular", "Aero.App.nsApp.execPath": "file:///Applications/Firefox.app/Contents/MacOS/firefox", "Aero.App.version": "13725.4.14", "Aero.App.versionShort": "137.0.2", "Aero.AxIgnored": "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription", "Aero.axWindowId": 13196, "Aero.isDialogHeuristic": true, "Aero.isWindowHeuristic": false, "Aero.on-window-detected": [], "Aero.treeNodeParent": "AppBundle.MacosPopupWindowsContainer", "Aero.workspace": "nil"}