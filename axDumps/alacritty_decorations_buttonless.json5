// ~/.alacritty.toml:
//     [window]
//     decorations = "Buttonless"
{
  "AXActivationPoint" : "<AXValue 0x6000031adef0> {value = x:10.000000 y:58.000000 type = kAXValueCGPointType}",
  "AXCancelButton" : null,
  "AXCloseButton" : {
    "AXEnabled" : 1,
    "AXParent" : "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXRole" : "AXButton",
    "AXSubrole" : "AXCloseButton",
    "AXTitle" : null,
    "AXTopLevelUIElement" : "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXWindow" : "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "Aero.AxIgnored" : "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription, AXEdited"
  },
  "AXDefaultButton" : null,
  "AXDocument" : null,
  "AXFocused" : 1,
  "AXFrame" : "<AXValue 0x600002abf680> {value = x:0.000000 y:44.000000 w:1800.000000 h:1124.000000 type = kAXValueCGRectType}",
  "AXFullScreen" : 0,
  "AXFullScreenButton" : {
    "AXEnabled" : 1,
    "AXParent" : "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXRole" : "AXButton",
    "AXSubrole" : "AXFullScreenButton",
    "AXTitle" : null,
    "AXTopLevelUIElement" : "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXWindow" : "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "Aero.AxIgnored" : "AXFrame, AXSize, AXFocused, AXChildren, AXHelp, AXPosition, AXRoleDescription"
  },
  "AXGrowArea" : null,
  "AXMain" : 1,
  "AXMinimizeButton" : {
    "AXEnabled" : 1,
    "AXParent" : "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXRole" : "AXButton",
    "AXSubrole" : "AXMinimizeButton",
    "AXTitle" : null,
    "AXTopLevelUIElement" : "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXWindow" : "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "Aero.AxIgnored" : "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription"
  },
  "AXMinimized" : 0,
  "AXModal" : 0,
  "AXParent" : "<AXUIElement Application 0x600003190240> {pid=1835}",
  "AXPosition" : "<AXValue 0x6000031adef0> {value = x:0.000000 y:44.000000 type = kAXValueCGPointType}",
  "AXProxy" : null,
  "AXRole" : "AXWindow",
  "AXSections" : [

  ],
  "AXSize" : "<AXValue 0x6000031ae160> {value = w:1800.000000 h:1124.000000 type = kAXValueCGSizeType}",
  "AXSubrole" : "AXStandardWindow",
  "AXTitle" : "ta ~",
  "AXTitleUIElement" : null,
  "AXToolbarButton" : null,
  "AXZoomButton" : {
    "AXEnabled" : 1,
    "AXParent" : "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXRole" : "AXButton",
    "AXSubrole" : "AXFullScreenButton",
    "AXTitle" : null,
    "AXTopLevelUIElement" : "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXWindow" : "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "Aero.AxIgnored" : "AXFrame, AXSize, AXFocused, AXChildren, AXHelp, AXPosition, AXRoleDescription"
  },
  "Aero.AXApp" : {
    "AXExtrasMenuBar" : null,
    "AXFocusedUIElement" : "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXFocusedWindow" : "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXFrame" : null,
    "AXFrontmost" : 1,
    "AXFunctionRowTopLevelElements" : [

    ],
    "AXMainWindow" : "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXMenuBar" : "<AXUIElement 0x600003190420> {pid=1835}",
    "AXPosition" : null,
    "AXRole" : "AXApplication",
    "AXSize" : null,
    "AXTitle" : "Alacritty",
    "AXWindows" : [
      "AXUIElement(AxWindowId=197, title=\"ta ~\", role=\"AXWindow\", subrole=\"AXStandardWindow\")"
    ],
    "Aero.AxIgnored" : "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"
  },
  "Aero.App.appBundleId" : "org.alacritty",
  "Aero.App.nsApp.activationPolicy" : "regular",
  "Aero.App.version" : "1",
  "Aero.App.versionShort" : "0.15.1",
  "Aero.AxIgnored" : "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription",
  "Aero.axWindowId" : "197",
  "Aero.isDialogHeuristic" : false,
  "Aero.isWindowHeuristic" : true,
  "Aero.treeNodeParent" : "AppBundle.TilingContainer",
  "Aero.workspace" : "M"
}
