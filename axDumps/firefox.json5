{"AXActivationPoint": "<AXValue 0x6000019fbf60> {value = x:10.000000 y:59.000000 type = kAXValueCGPointType}", "AXCancelButton": null, "AXCloseButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=13054, title=\"Mozilla Firefox\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXCloseButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=13054, title=\"Mozilla Firefox\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=13054, title=\"Mozilla Firefox\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription, AXEdited"}, "AXDefaultButton": null, "AXDocument": null, "AXFocused": 0, "AXFrame": "<AXValue 0x6000002cbc00> {value = x:0.000000 y:44.000000 w:1800.000000 h:1124.000000 type = kAXValueCGRectType}", "AXFullScreen": 0, "AXFullScreenButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=13054, title=\"Mozilla Firefox\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXFullScreenButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=13054, title=\"Mozilla Firefox\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=13054, title=\"Mozilla Firefox\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXChildren, AXHelp, AXPosition, AXRoleDescription"}, "AXGrowArea": null, "AXMain": 1, "AXMinimizeButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=13054, title=\"Mozilla Firefox\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXMinimizeButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=13054, title=\"Mozilla Firefox\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=13054, title=\"Mozilla Firefox\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription"}, "AXMinimized": 0, "AXModal": 0, "AXParent": "<AXUIElement Application 0x6000019fbf60> {pid=1405}", "AXPosition": "<AXValue 0x6000019fa190> {value = x:0.000000 y:44.000000 type = kAXValueCGPointType}", "AXProxy": null, "AXRole": "AXWindow", "AXSections": ["{\n    SectionDescription = Content;\n    SectionObject = \"<AXUIElement 0x6000019898c0> {pid=1405}\";\n    SectionUniqueID = AXContent;\n}", "{\n    SectionObject = \"<AXUIElement 0x60000198b210> {pid=1405}\";\n    SectionUniqueID = AXContainer;\n}"], "AXSize": "<AXValue 0x60000198b8a0> {value = w:1800.000000 h:1124.000000 type = kAXValueCGSizeType}", "AXSubrole": "AXStandardWindow", "AXTitle": "Mozilla Firefox", "AXTitleUIElement": null, "AXToolbarButton": null, "AXZoomButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=13054, title=\"Mozilla Firefox\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXFullScreenButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=13054, title=\"Mozilla Firefox\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=13054, title=\"Mozilla Firefox\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXChildren, AXHelp, AXPosition, AXRoleDescription"}, "Aero.AXApp": {"AXExtrasMenuBar": null, "AXFocusedUIElement": "AXUIElement(AxWindowId=13054, title=\"\", role=\"AXTextField\", subrole=\"AXUnknown\")", "AXFocusedWindow": "AXUIElement(AxWindowId=13054, title=\"Mozilla Firefox\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXFrame": null, "AXFrontmost": 1, "AXFunctionRowTopLevelElements": [], "AXMainWindow": "AXUIElement(AxWindowId=13054, title=\"Mozilla Firefox\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXMenuBar": "<AXUIElement 0x60000198a100> {pid=1405}", "AXPosition": null, "AXRole": "AXApplication", "AXSize": null, "AXTitle": "Firefox", "AXWindows": ["AXUIElement(AxWindowId=13054, title=\"Mozilla Firefox\", role=\"AXWindow\", subrole=\"AXStandardWindow\")"], "Aero.AxIgnored": "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"}, "Aero.App.appBundleId": "org.mozilla.firefox", "Aero.App.nsApp.activationPolicy": "regular", "Aero.App.nsApp.execPath": "file:///Applications/Firefox.app/Contents/MacOS/firefox", "Aero.App.version": "13725.4.14", "Aero.App.versionShort": "137.0.2", "Aero.AxIgnored": "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription", "Aero.axWindowId": 13054, "Aero.isDialogHeuristic": false, "Aero.isWindowHeuristic": true, "Aero.on-window-detected": [], "Aero.treeNodeParent": "AppBundle.TilingContainer", "Aero.workspace": "W"}