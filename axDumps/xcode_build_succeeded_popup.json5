{"AXActivationPoint": "<AXValue 0x600001933a20> {value = x:-1.000000 y:1170.000000 type = kAXValueCGPointType}", "AXCancelButton": null, "AXCloseButton": null, "AXDefaultButton": null, "AXDocument": null, "AXFocused": 0, "AXFrame": "<AXValue 0x6000002b1200> {value = x:800.000000 y:829.000000 w:200.000000 h:200.000000 type = kAXValueCGRectType}", "AXFullScreen": 0, "AXFullScreenButton": null, "AXGrowArea": null, "AXMain": 0, "AXMinimizeButton": null, "AXMinimized": 0, "AXModal": 0, "AXParent": "<AXUIElement Application 0x600001939110> {pid=27529}", "AXPosition": "<AXValue 0x600001933a20> {value = x:800.000000 y:829.000000 type = kAXValueCGPointType}", "AXProxy": null, "AXRole": "AXWindow", "AXSections": ["{\n    SectionDescription = Content;\n    SectionObject = \"<AXUIElement 0x600001931950> {pid=27529}\";\n    SectionUniqueID = AXContent;\n}"], "AXSize": "<AXValue 0x600001939110> {value = w:200.000000 h:200.000000 type = kAXValueCGSizeType}", "AXSubrole": "AXSystemDialog", "AXTitle": "Build Succeeded", "AXTitleUIElement": null, "AXToolbarButton": null, "AXZoomButton": null, "Aero.AXApp": {"AXExtrasMenuBar": null, "AXFocusedUIElement": "AXUIElement(AxWindowId=14010, title=nil, role=\"AXTextArea\", subrole=nil)", "AXFocusedWindow": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXFrame": null, "AXFrontmost": 1, "AXFunctionRowTopLevelElements": [], "AXMainWindow": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXMenuBar": "<AXUIElement 0x600001938f90> {pid=27529}", "AXPosition": null, "AXRole": "AXApplication", "AXSize": null, "AXTitle": "Xcode", "AXWindows": ["AXUIElement(AxWindowId=14483, title=\"Build Succeeded\", role=\"AXWindow\", subrole=\"AXSystemDialog\")", "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")"], "Aero.AxIgnored": "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"}, "Aero.App.appBundleId": "com.apple.dt.Xcode", "Aero.App.nsApp.activationPolicy": "regular", "Aero.App.nsApp.execPath": "file:///Applications/Xcode.app/Contents/MacOS/Xcode", "Aero.App.version": "23785", "Aero.App.versionShort": "16.3", "Aero.AxIgnored": "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription", "Aero.axWindowId": 14483, "Aero.isDialogHeuristic": true, "Aero.isWindowHeuristic": false, "Aero.on-window-detected": [], "Aero.treeNodeParent": "AppBundle.MacosPopupWindowsContainer", "Aero.workspace": "nil"}