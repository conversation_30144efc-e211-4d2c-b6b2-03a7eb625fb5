{"AXActivationPoint": "<AXValue 0x6000019c1950> {value = x:10.000000 y:58.000000 type = kAXValueCGPointType}", "AXCancelButton": null, "AXCloseButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXCloseButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription, AXEdited"}, "AXDefaultButton": null, "AXDocument": "file:///Users/<USER>/a/AeroSpace/Sources/AppBundleTests/AxWindowKindTest.swift", "AXFocused": 0, "AXFrame": "<AXValue 0x6000002a52c0> {value = x:0.000000 y:44.000000 w:1800.000000 h:1124.000000 type = kAXValueCGRectType}", "AXFullScreen": 0, "AXFullScreenButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXFullScreenButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXChildren, AXHelp, AXPosition, AXRoleDescription"}, "AXGrowArea": null, "AXIdentifier": "Xcode.WorkspaceWindow", "AXMain": 1, "AXMinimizeButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXMinimizeButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription"}, "AXMinimized": 0, "AXModal": 0, "AXParent": "<AXUIElement Application 0x6000019c1950> {pid=27529}", "AXPosition": "<AXValue 0x6000019fba50> {value = x:0.000000 y:44.000000 type = kAXValueCGPointType}", "AXProxy": null, "AXRole": "AXWindow", "AXSections": ["{\n    SectionDescription = Toolbar;\n    SectionObject = \"<AXUIElement 0x6000019c31b0> {pid=27529}\";\n    SectionUniqueID = AXToolbar;\n}", "{\n    SectionObject = \"<AXUIElement 0x6000019c2190> {pid=27529}\";\n}", "{\n    SectionObject = \"<AXUIElement 0x6000019c3780> {pid=27529}\";\n    SectionUniqueID = AXContent;\n}", "{\n    SectionObject = \"<AXUIElement 0x6000019c1800> {pid=27529}\";\n    SectionUniqueID = AXContent;\n}"], "AXSize": "<AXValue 0x6000019c2190> {value = w:1800.000000 h:1124.000000 type = kAXValueCGSizeType}", "AXSubrole": "AXStandardWindow", "AXTitle": "AeroSpace — AxWindowKindTest.swift", "AXTitleUIElement": null, "AXToolbarButton": null, "AXZoomButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXFullScreenButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXChildren, AXHelp, AXPosition, AXRoleDescription"}, "Aero.AXApp": {"AXExtrasMenuBar": null, "AXFocusedUIElement": "AXUIElement(AxWindowId=14010, title=nil, role=\"AXTextArea\", subrole=nil)", "AXFocusedWindow": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXFrame": null, "AXFrontmost": 1, "AXFunctionRowTopLevelElements": [], "AXMainWindow": "AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXMenuBar": "<AXUIElement 0x6000018cb750> {pid=27529}", "AXPosition": null, "AXRole": "AXApplication", "AXSize": null, "AXTitle": "Xcode", "AXWindows": ["AXUIElement(AxWindowId=14010, title=\"AeroSpace — AxWindowKindTest.swift\", role=\"AXWindow\", subrole=\"AXStandardWindow\")"], "Aero.AxIgnored": "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"}, "Aero.App.appBundleId": "com.apple.dt.Xcode", "Aero.App.nsApp.activationPolicy": "regular", "Aero.App.nsApp.execPath": "file:///Applications/Xcode.app/Contents/MacOS/Xcode", "Aero.App.version": "23785", "Aero.App.versionShort": "16.3", "Aero.AxIgnored": "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription", "Aero.axWindowId": 14010, "Aero.isDialogHeuristic": false, "Aero.isWindowHeuristic": true, "Aero.on-window-detected": [], "Aero.treeNodeParent": "AppBundle.TilingContainer", "Aero.workspace": "X"}