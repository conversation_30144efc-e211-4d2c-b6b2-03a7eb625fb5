// Find Action... -> Rebase
{
  "AXActivationPoint" : "<AXValue 0x6000018c7030> {value = x:745.000000 y:557.000000 type = kAXValueCGPointType}",
  "AXCancelButton" : null,
  "AXCloseButton" : {
    "AXEnabled" : 1,
    "AXParent" : "AXUIElement(AxWindowId=14799, title=\"Rebase\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXRole" : "AXButton",
    "AXSubrole" : "AXCloseButton",
    "AXTitle" : null,
    "AXTopLevelUIElement" : "AXUIElement(AxWindowId=14799, title=\"Rebase\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXWindow" : "AXUIElement(AxWindowId=14799, title=\"Rebase\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "Aero.AxIgnored" : "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription, AXEdited"
  },
  "AXDefaultButton" : null,
  "AXDocument" : null,
  "AXFocused" : 0,
  "AXFrame" : "<AXValue 0x6000002cbdc0> {value = x:675.000000 y:543.000000 w:479.000000 h:125.000000 type = kAXValueCGRectType}",
  "AXFullScreen" : 0,
  "AXFullScreenButton" : null,
  "AXGrowArea" : null,
  "AXMain" : 1,
  "AXMinimizeButton" : {
    "AXEnabled" : 0,
    "AXParent" : "AXUIElement(AxWindowId=14799, title=\"Rebase\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXRole" : "AXButton",
    "AXSubrole" : "AXMinimizeButton",
    "AXTitle" : null,
    "AXTopLevelUIElement" : "AXUIElement(AxWindowId=14799, title=\"Rebase\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXWindow" : "AXUIElement(AxWindowId=14799, title=\"Rebase\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "Aero.AxIgnored" : "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription"
  },
  "AXMinimized" : 0,
  "AXModal" : 0,
  "AXParent" : "<AXUIElement Application 0x6000018c7030> {pid=8947}",
  "AXPosition" : "<AXValue 0x6000018c7600> {value = x:675.000000 y:543.000000 type = kAXValueCGPointType}",
  "AXProxy" : null,
  "AXRole" : "AXWindow",
  "AXSections" : [
    "{\n    SectionDescription = Content;\n    SectionObject = \"<AXUIElement 0x60000193a910> {pid=8947}\";\n    SectionUniqueID = AXContent;\n}",
    "{\n    SectionObject = \"<AXUIElement 0x600001938ab0> {pid=8947}\";\n    SectionUniqueID = AXContainer;\n}"
  ],
  "AXSize" : "<AXValue 0x60000193ad60> {value = w:479.000000 h:125.000000 type = kAXValueCGSizeType}",
  "AXSubrole" : "AXStandardWindow",
  "AXTitle" : "Rebase",
  "AXTitleUIElement" : "AXUIElement(AxWindowId=14799, title=nil, role=\"AXStaticText\", subrole=nil)",
  "AXToolbarButton" : null,
  "AXZoomButton" : {
    "AXEnabled" : 1,
    "AXParent" : "AXUIElement(AxWindowId=14799, title=\"Rebase\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXRole" : "AXButton",
    "AXSubrole" : "AXZoomButton",
    "AXTitle" : null,
    "AXTopLevelUIElement" : "AXUIElement(AxWindowId=14799, title=\"Rebase\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXWindow" : "AXUIElement(AxWindowId=14799, title=\"Rebase\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "Aero.AxIgnored" : "AXFrame, AXSize, AXFocused, AXChildren, AXHelp, AXPosition, AXRoleDescription"
  },
  "Aero.AXApp" : {
    "AXExtrasMenuBar" : null,
    "AXFocusedUIElement" : "AXUIElement(AxWindowId=14799, title=nil, role=\"AXTextArea\", subrole=nil)",
    "AXFocusedWindow" : "AXUIElement(AxWindowId=14799, title=\"Rebase\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXFrame" : null,
    "AXFrontmost" : 1,
    "AXFunctionRowTopLevelElements" : [

    ],
    "AXMainWindow" : "AXUIElement(AxWindowId=14799, title=\"Rebase\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXMenuBar" : "<AXUIElement 0x6000018c6d60> {pid=8947}",
    "AXPosition" : null,
    "AXRole" : "AXApplication",
    "AXSize" : null,
    "AXTitle" : "IntelliJ IDEA",
    "AXWindows" : [
      "AXUIElement(AxWindowId=14799, title=\"Rebase\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
      "AXUIElement(AxWindowId=13585, title=\"jvm-gradle-sandbox [~/a/jvm-gradle-sandbox] – /Users/<USER>/a/jvm-gradle-sandbox/app/src/test/kotlin/jvm/gradle/sandbox/AppTest.kt\", role=\"AXWindow\", subrole=\"AXDialog\")",
      "AXUIElement(AxWindowId=13583, title=\"jvm-sandbox [~/a/jvm-sandbox] – /Users/<USER>/a/jvm-sandbox/src/main/kotlin/main.kt\", role=\"AXWindow\", subrole=\"AXDialog\")"
    ],
    "Aero.AxIgnored" : "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"
  },
  "Aero.App.appBundleId" : "com.jetbrains.intellij",
  "Aero.App.nsApp.activationPolicy" : "regular",
  "Aero.App.nsApp.execPath" : "file:///Users/<USER>/Applications/ij-241.app/Contents/MacOS/idea",
  "Aero.App.version" : "IU-241.17011.79",
  "Aero.App.versionShort" : "2024.1.2",
  "Aero.AxIgnored" : "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription",
  "Aero.axWindowId" : 14799,
  "Aero.isDialogHeuristic" : true,
  "Aero.isWindowHeuristic" : true,
  "Aero.on-window-detected" : [

  ],
  "Aero.treeNodeParent" : "AppBundle.Workspace",
  "Aero.workspace" : "I"
}
