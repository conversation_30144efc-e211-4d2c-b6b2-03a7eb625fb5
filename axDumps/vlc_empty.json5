{"AXActivationPoint": "<AXValue 0x6000018c41b0> {value = x:703.000000 y:795.000000 type = kAXValueCGPointType}", "AXCancelButton": null, "AXCloseButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14277, title=\"VLC media player\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXCloseButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14277, title=\"VLC media player\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14277, title=\"VLC media player\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription, AXEdited"}, "AXDefaultButton": null, "AXDocument": null, "AXFocused": 0, "AXFrame": "<AXValue 0x600000db6b80> {value = x:633.000000 y:781.000000 w:725.000000 h:64.000000 type = kAXValueCGRectType}", "AXFullScreen": 0, "AXFullScreenButton": null, "AXGrowArea": null, "AXIdentifier": "_NS:122", "AXMain": 1, "AXMinimizeButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14277, title=\"VLC media player\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXMinimizeButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14277, title=\"VLC media player\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14277, title=\"VLC media player\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription"}, "AXMinimized": 0, "AXModal": 0, "AXParent": "<AXUIElement Application 0x60000193eca0> {pid=38681}", "AXPosition": "<AXValue 0x60000194ff60> {value = x:633.000000 y:781.000000 type = kAXValueCGPointType}", "AXProxy": null, "AXRole": "AXWindow", "AXSections": ["{\n    SectionDescription = Content;\n    SectionObject = \"<AXUIElement 0x60000193eb20> {pid=38681}\";\n    SectionUniqueID = AXContent;\n}"], "AXSize": "<AXValue 0x6000018c41b0> {value = w:725.000000 h:64.000000 type = kAXValueCGSizeType}", "AXSubrole": "AXStandardWindow", "AXTitle": "VLC media player", "AXTitleUIElement": "AXUIElement(AxWindowId=14277, title=nil, role=\"AXStaticText\", subrole=nil)", "AXToolbarButton": null, "AXZoomButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14277, title=\"VLC media player\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXZoomButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14277, title=\"VLC media player\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14277, title=\"VLC media player\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXChildren, AXHelp, AXPosition, AXRoleDescription"}, "Aero.AXApp": {"AXExtrasMenuBar": "<AXUIElement 0x6000018c4840> {pid=38681}", "AXFocusedUIElement": "AXUIElement(AxWindowId=14277, title=nil, role=\"AXTextField\", subrole=\"AXSearchField\")", "AXFocusedWindow": "AXUIElement(AxWindowId=14277, title=\"VLC media player\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXFrame": null, "AXFrontmost": 1, "AXFunctionRowTopLevelElements": [], "AXMainWindow": "AXUIElement(AxWindowId=14277, title=\"VLC media player\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXMenuBar": "<AXUIElement 0x6000018c3ed0> {pid=38681}", "AXPosition": null, "AXRole": "AXApplication", "AXSize": null, "AXTitle": "VLC", "AXWindows": ["AXUIElement(AxWindowId=14277, title=\"VLC media player\", role=\"AXWindow\", subrole=\"AXStandardWindow\")"], "Aero.AxIgnored": "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"}, "Aero.App.appBundleId": "org.videolan.vlc", "Aero.App.nsApp.activationPolicy": "regular", "Aero.App.nsApp.execPath": "file:///Applications/VLC.app/Contents/MacOS/VLC", "Aero.App.version": "3.0.21", "Aero.App.versionShort": "3.0.21", "Aero.AxIgnored": "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription", "Aero.axWindowId": 14277, "Aero.isDialogHeuristic": true, "Aero.isWindowHeuristic": true, "Aero.on-window-detected": [], "Aero.treeNodeParent": "AppBundle.Workspace", "Aero.workspace": "F"}