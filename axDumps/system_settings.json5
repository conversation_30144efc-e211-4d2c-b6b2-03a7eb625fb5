{"AXActivationPoint": "<AXValue 0x600001938ba0> {value = x:314.000000 y:94.000000 type = kAXValueCGPointType}", "AXCancelButton": null, "AXCloseButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14534, title=\"\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXCloseButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14534, title=\"\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14534, title=\"\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription, AXEdited"}, "AXDefaultButton": null, "AXDocument": null, "AXFocused": 0, "AXFrame": "<AXValue 0x600000d42ac0> {value = x:304.000000 y:80.000000 w:715.000000 h:856.000000 type = kAXValueCGRectType}", "AXFullScreen": 0, "AXFullScreenButton": null, "AXGrowArea": null, "AXIdentifier": "main", "AXMain": 1, "AXMinimizeButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14534, title=\"\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXMinimizeButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14534, title=\"\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14534, title=\"\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription"}, "AXMinimized": 0, "AXModal": 0, "AXParent": "<AXUIElement Application 0x600001938ba0> {pid=41135}", "AXPosition": "<AXValue 0x60000193bb70> {value = x:304.000000 y:80.000000 type = kAXValueCGPointType}", "AXProxy": null, "AXRole": "AXWindow", "AXSections": ["{\n    SectionDescription = Content;\n    SectionObject = \"<AXUIElement 0x600001933780> {pid=41135}\";\n    SectionUniqueID = AXContent;\n}", "{\n    SectionDescription = Toolbar;\n    SectionObject = \"<AXUIElement 0x6000019333c0> {pid=41135}\";\n    SectionUniqueID = AXToolbar;\n}", "{\n    SectionDescription = Search;\n    SectionObject = \"<AXUIElement 0x600001933fc0> {pid=41135}\";\n    SectionUniqueID = AXSearch;\n}", "{\n    SectionDescription = \"Content Navigator\";\n    SectionObject = \"<AXUIElement 0x600001931890> {pid=41135}\";\n    SectionUniqueID = AXContentNavigator;\n}"], "AXSize": "<AXValue 0x60000193bf00> {value = w:715.000000 h:856.000000 type = kAXValueCGSizeType}", "AXSubrole": "AXStandardWindow", "AXTitle": "", "AXTitleUIElement": "AXUIElement(AxWindowId=14534, title=nil, role=\"AXStaticText\", subrole=nil)", "AXToolbarButton": null, "AXZoomButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14534, title=\"\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXZoomButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14534, title=\"\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14534, title=\"\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXChildren, AXHelp, AXPosition, AXRoleDescription"}, "Aero.AXApp": {"AXExtrasMenuBar": null, "AXFocusedUIElement": "AXUIElement(AxWindowId=14534, title=nil, role=\"AXOutline\", subrole=nil)", "AXFocusedWindow": "AXUIElement(AxWindowId=14534, title=\"\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXFrame": null, "AXFrontmost": 1, "AXFunctionRowTopLevelElements": [], "AXMainWindow": "AXUIElement(AxWindowId=14534, title=\"\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXMenuBar": "<AXUIElement 0x600001933f00> {pid=41135}", "AXPosition": null, "AXRole": "AXApplication", "AXSize": null, "AXTitle": "System Settings", "AXWindows": ["AXUIElement(AxWindowId=14534, title=\"\", role=\"AXWindow\", subrole=\"AXStandardWindow\")"], "Aero.AxIgnored": "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"}, "Aero.App.appBundleId": "com.apple.systempreferences", "Aero.App.nsApp.activationPolicy": "regular", "Aero.App.nsApp.execPath": "file:///System/Applications/System%20Settings.app/Contents/MacOS/System%20Settings", "Aero.App.version": "15.0", "Aero.App.versionShort": "15.0", "Aero.AxIgnored": "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription", "Aero.axWindowId": 14534, "Aero.isDialogHeuristic": true, "Aero.isWindowHeuristic": true, "Aero.on-window-detected": [], "Aero.treeNodeParent": "AppBundle.Workspace", "Aero.workspace": "M"}