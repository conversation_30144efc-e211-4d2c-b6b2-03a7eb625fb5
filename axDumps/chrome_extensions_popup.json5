// Top right corner -> Extensions
{
  "AXActivationPoint" : "<AXValue 0x60000198b9c0> {value = x:-1.000000 y:1170.000000 type = kAXValueCGPointType}",
  "AXCancelButton" : null,
  "AXCloseButton" : null,
  "AXDefaultButton" : null,
  "AXDocument" : "",
  "AXFocused" : 0,
  "AXFrame" : "<AXValue 0x600000de7f80> {value = x:1385.000000 y:124.000000 w:320.000000 h:537.000000 type = kAXValueCGRectType}",
  "AXFullScreen" : 0,
  "AXFullScreenButton" : null,
  "AXGrowArea" : null,
  "AXMain" : 0,
  "AXMinimizeButton" : null,
  "AXMinimized" : 0,
  "AXModal" : 0,
  "AXParent" : "<AXUIElement Application 0x6000018d6790> {pid=34617}",
  "AXPosition" : "<AXValue 0x6000018d6790> {value = x:1385.000000 y:124.000000 type = kAXValueCGPointType}",
  "AXProxy" : null,
  "AXRole" : "AXWindow",
  "AXSections" : [
    "{\n    SectionDescription = Content;\n    SectionObject = \"<AXUIElement 0x6000018d7390> {pid=34617}\";\n    SectionUniqueID = AXContent;\n}",
    "{\n    SectionObject = \"<AXUIElement 0x6000018d5740> {pid=34617}\";\n    SectionUniqueID = AXContainer;\n}"
  ],
  "AXSize" : "<AXValue 0x6000018d6790> {value = w:320.000000 h:537.000000 type = kAXValueCGSizeType}",
  "AXSubrole" : "AXUnknown",
  "AXTitle" : "",
  "AXTitleUIElement" : null,
  "AXToolbarButton" : null,
  "AXZoomButton" : null,
  "Aero.AXApp" : {
    "AXExtrasMenuBar" : null,
    "AXFocusedUIElement" : null,
    "AXFocusedWindow" : "AXUIElement(AxWindowId=14076, title=\"\", role=\"AXWindow\", subrole=\"AXUnknown\")",
    "AXFrame" : null,
    "AXFrontmost" : 1,
    "AXFunctionRowTopLevelElements" : [

    ],
    "AXMainWindow" : "AXUIElement(AxWindowId=14057, title=\"New Tab - Google Chrome\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
    "AXMenuBar" : "<AXUIElement 0x6000019884b0> {pid=34617}",
    "AXPosition" : null,
    "AXRole" : "AXApplication",
    "AXSize" : null,
    "AXTitle" : "Chrome",
    "AXWindows" : [
      "AXUIElement(AxWindowId=14076, title=\"\", role=\"AXWindow\", subrole=\"AXUnknown\")",
      "AXUIElement(AxWindowId=14057, title=\"New Tab - Google Chrome\", role=\"AXWindow\", subrole=\"AXStandardWindow\")"
    ],
    "Aero.AxIgnored" : "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"
  },
  "Aero.App.appBundleId" : "com.google.Chrome",
  "Aero.App.nsApp.activationPolicy" : "regular",
  "Aero.App.nsApp.execPath" : "file:///Applications/Google%20Chrome.app/Contents/MacOS/Google%20Chrome",
  "Aero.App.version" : "7049.116",
  "Aero.App.versionShort" : "135.0.7049.116",
  "Aero.AxIgnored" : "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription",
  "Aero.axWindowId" : 14076,
  "Aero.isDialogHeuristic" : true,
  "Aero.isWindowHeuristic" : false,
  "Aero.on-window-detected" : [
    {
      "commands" : "move-node-to-workspace F",
      "matcher" : "appId=\"com.google.Chrome\""
    }
  ],
  "Aero.treeNodeParent" : "AppBundle.MacosPopupWindowsContainer",
  "Aero.workspace" : "nil"
}
