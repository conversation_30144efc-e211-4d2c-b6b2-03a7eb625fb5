{"AXActivationPoint": "<AXValue 0x6000019f9a40> {value = x:-1.000000 y:1170.000000 type = kAXValueCGPointType}", "AXCancelButton": null, "AXCloseButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14228, title=\"YONAKA - PREDATOR\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXCloseButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14228, title=\"YONAKA - PREDATOR\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14228, title=\"YONAKA - PREDATOR\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription, AXEdited"}, "AXDefaultButton": null, "AXDocument": "https://xpui.app.spotify.com/index.html", "AXFocused": 0, "AXFrame": "<AXValue 0x600000de7100> {value = x:0.000000 y:44.000000 w:1800.000000 h:1124.000000 type = kAXValueCGRectType}", "AXFullScreen": 0, "AXFullScreenButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14228, title=\"YONAKA - PREDATOR\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXFullScreenButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14228, title=\"YONAKA - PREDATOR\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14228, title=\"YONAKA - PREDATOR\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXChildren, AXHelp, AXPosition, AXRoleDescription"}, "AXGrowArea": null, "AXMain": 1, "AXMinimizeButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14228, title=\"YONAKA - PREDATOR\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXMinimizeButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14228, title=\"YONAKA - PREDATOR\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14228, title=\"YONAKA - PREDATOR\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription"}, "AXMinimized": 0, "AXModal": 0, "AXParent": "<AXUIElement Application 0x6000019f9a40> {pid=37876}", "AXPosition": "<AXValue 0x6000019c27f0> {value = x:0.000000 y:44.000000 type = kAXValueCGPointType}", "AXProxy": null, "AXRole": "AXWindow", "AXSections": ["{\n    SectionDescription = Content;\n    SectionObject = \"<AXUIElement 0x6000019f9470> {pid=37876}\";\n    SectionUniqueID = AXContent;\n}", "{\n    SectionObject = \"<AXUIElement 0x6000019fb9f0> {pid=37876}\";\n    SectionUniqueID = AXContainer;\n}"], "AXSize": "<AXValue 0x6000019c6f10> {value = w:1800.000000 h:1124.000000 type = kAXValueCGSizeType}", "AXSubrole": "AXStandardWindow", "AXTitle": "YONAKA - PREDATOR", "AXTitleUIElement": null, "AXToolbarButton": null, "AXZoomButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14228, title=\"YONAKA - PREDATOR\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXFullScreenButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14228, title=\"YONAKA - PREDATOR\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14228, title=\"YONAKA - PREDATOR\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXChildren, AXHelp, AXPosition, AXRoleDescription"}, "Aero.AXApp": {"AXExtrasMenuBar": null, "AXFocusedUIElement": null, "AXFocusedWindow": "AXUIElement(AxWindowId=14228, title=\"YONAKA - PREDATOR\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXFrame": null, "AXFrontmost": 1, "AXFunctionRowTopLevelElements": [], "AXMainWindow": "AXUIElement(AxWindowId=14228, title=\"YONAKA - PREDATOR\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXMenuBar": "<AXUIElement 0x6000019c6a00> {pid=37876}", "AXPosition": null, "AXRole": "AXApplication", "AXSize": null, "AXTitle": "Spotify", "AXWindows": ["AXUIElement(AxWindowId=14228, title=\"YONAKA - PREDATOR\", role=\"AXWindow\", subrole=\"AXStandardWindow\")"], "Aero.AxIgnored": "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"}, "Aero.App.appBundleId": "com.spotify.client", "Aero.App.nsApp.activationPolicy": "regular", "Aero.App.nsApp.execPath": "file:///Applications/Spotify.app/Contents/MacOS/Spotify", "Aero.App.version": "1.2.61.443", "Aero.App.versionShort": "1.2.61.443", "Aero.AxIgnored": "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription", "Aero.axWindowId": 14228, "Aero.isDialogHeuristic": false, "Aero.isWindowHeuristic": true, "Aero.on-window-detected": [], "Aero.treeNodeParent": "AppBundle.TilingContainer", "Aero.workspace": "B"}