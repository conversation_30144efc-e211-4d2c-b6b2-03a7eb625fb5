{"AXActivationPoint": "<AXValue 0x600001931d40> {value = x:1210.000000 y:58.000000 type = kAXValueCGPointType}", "AXCancelButton": null, "AXCloseButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXCloseButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription, AXEdited"}, "AXDefaultButton": null, "AXDocument": null, "AXFocused": 1, "AXFrame": "<AXValue 0x6000002897c0> {value = x:1200.000000 y:44.000000 w:599.000000 h:337.000000 type = kAXValueCGRectType}", "AXFullScreen": 0, "AXFullScreenButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXFullScreenButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXChildren, AXHelp, AXPosition, AXRoleDescription"}, "AXGrowArea": null, "AXMain": 1, "AXMinimizeButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXMinimizeButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription"}, "AXMinimized": 0, "AXModal": 0, "AXParent": "<AXUIElement Application 0x6000019fb2d0> {pid=38857}", "AXPosition": "<AXValue 0x6000018cdb60> {value = x:1200.000000 y:44.000000 type = kAXValueCGPointType}", "AXProxy": null, "AXRole": "AXWindow", "AXSections": ["{\n    SectionDescription = Content;\n    SectionObject = \"<AXUIElement 0x6000018d07e0> {pid=38857}\";\n    SectionUniqueID = AXContent;\n}"], "AXSize": "<AXValue 0x6000019fb2d0> {value = w:599.000000 h:337.000000 type = kAXValueCGSizeType}", "AXSubrole": "AXStandardWindow", "AXTitle": "The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv", "AXTitleUIElement": "AXUIElement(AxWindowId=14286, title=nil, role=\"AXStaticText\", subrole=nil)", "AXToolbarButton": null, "AXZoomButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXFullScreenButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXChildren, AXHelp, AXPosition, AXRoleDescription"}, "Aero.AXApp": {"AXExtrasMenuBar": null, "AXFocusedUIElement": "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXFocusedWindow": "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXFrame": null, "AXFrontmost": 1, "AXFunctionRowTopLevelElements": [], "AXMainWindow": "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXMenuBar": "<AXUIElement 0x6000018d2010> {pid=38857}", "AXPosition": null, "AXRole": "AXApplication", "AXSize": null, "AXTitle": "mpv", "AXWindows": ["AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - <PERSON> (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXStandardWindow\")"], "Aero.AxIgnored": "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"}, "Aero.App.appBundleId": "NULL-APP-BUNDLE-ID", "Aero.App.nsApp.activationPolicy": "regular", "Aero.App.nsApp.execPath": "file:///opt/homebrew/bin/mpv", "Aero.App.version": null, "Aero.App.versionShort": null, "Aero.AxIgnored": "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription", "Aero.axWindowId": 14286, "Aero.isDialogHeuristic": false, "Aero.isWindowHeuristic": true, "Aero.on-window-detected": [], "Aero.treeNodeParent": "AppBundle.TilingContainer", "Aero.workspace": "M"}