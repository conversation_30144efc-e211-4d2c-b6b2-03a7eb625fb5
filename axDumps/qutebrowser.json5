{"AXActivationPoint": "<AXValue 0x6000018d1140> {value = x:970.000000 y:58.000000 type = kAXValueCGPointType}", "AXCancelButton": null, "AXCloseButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXCloseButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription, AXEdited"}, "AXDefaultButton": null, "AXDocument": null, "AXFocused": 0, "AXFrame": "<AXValue 0x60000029d680> {value = x:900.000000 y:44.000000 w:900.000000 h:1124.000000 type = kAXValueCGRectType}", "AXFullScreen": 0, "AXFullScreenButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXFullScreenButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXChildren, AXHelp, AXPosition, AXRoleDescription"}, "AXGrowArea": null, "AXMain": 1, "AXMinimizeButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXMinimizeButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXHelp, AXPosition, AXRoleDescription"}, "AXMinimized": 0, "AXModal": 0, "AXParent": "<AXUIElement Application 0x60000191ac70> {pid=45633}", "AXPosition": "<AXValue 0x6000019ac6c0> {value = x:900.000000 y:44.000000 type = kAXValueCGPointType}", "AXProxy": null, "AXRole": "AXWindow", "AXSections": ["{\n    SectionDescription = Content;\n    SectionObject = \"<AXUIElement 0x6000018d31b0> {pid=45633}\";\n    SectionUniqueID = AXContent;\n}", "{\n    SectionObject = \"<AXUIElement 0x6000018d3ae0> {pid=45633}\";\n    SectionUniqueID = AXContainer;\n}", "{\n    SectionObject = \"<AXUIElement 0x6000018d3600> {pid=45633}\";\n    SectionUniqueID = AXContainer;\n}"], "AXSize": "<AXValue 0x60000191ac70> {value = w:900.000000 h:1124.000000 type = kAXValueCGSizeType}", "AXSubrole": "AXStandardWindow", "AXTitle": "qutebrowser quickstart | qutebrowser - qutebrowser", "AXTitleUIElement": "AXUIElement(AxWindowId=14631, title=nil, role=\"AXStaticText\", subrole=nil)", "AXToolbarButton": null, "AXZoomButton": {"AXEnabled": 1, "AXParent": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXRole": "AXButton", "AXSubrole": "AXFullScreenButton", "AXTitle": null, "AXTopLevelUIElement": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXWindow": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "Aero.AxIgnored": "AXFrame, AXSize, AXFocused, AXChildren, AXHelp, AXPosition, AXRoleDescription"}, "Aero.AXApp": {"AXExtrasMenuBar": null, "AXFocusedUIElement": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser\", role=\"AXGroup\", subrole=nil)", "AXFocusedWindow": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXFrame": null, "AXFrontmost": 1, "AXFunctionRowTopLevelElements": [], "AXMainWindow": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXMenuBar": "<AXUIElement 0x6000018c0840> {pid=45633}", "AXPosition": null, "AXRole": "AXApplication", "AXSize": null, "AXTitle": "qute<PERSON><PERSON>er", "AXWindows": ["AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")"], "Aero.AxIgnored": "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"}, "Aero.App.appBundleId": "org.qutebrowser.qutebrowser", "Aero.App.nsApp.activationPolicy": "regular", "Aero.App.nsApp.execPath": "file:///Applications/qutebrowser.app/Contents/MacOS/qutebrowser", "Aero.App.version": "3.5.0", "Aero.App.versionShort": "3.5.0", "Aero.AxIgnored": "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription", "Aero.axWindowId": 14631, "Aero.isDialogHeuristic": false, "Aero.isWindowHeuristic": true, "Aero.on-window-detected": [], "Aero.treeNodeParent": "AppBundle.TilingContainer", "Aero.workspace": "M"}