// File -> Open...
{
  "AXActivationPoint" : "<AXValue 0x60000193b5a0> {value = x:-1.000000 y:1170.000000 type = kAXValueCGPointType}",
  "AXCancelButton" : null,
  "AXCloseButton" : null,
  "AXDefaultButton" : null,
  "AXDocument" : null,
  "AXFocused" : 0,
  "AXFrame" : "<AXValue 0x60000029bf00> {value = x:311.000000 y:45.000000 w:1177.000000 h:1124.000000 type = kAXValueCGRectType}",
  "AXFullScreen" : 0,
  "AXFullScreenButton" : null,
  "AXGrowArea" : null,
  "AXIdentifier" : "open-panel",
  "AXMain" : 1,
  "AXMinimizeButton" : null,
  "AXMinimized" : 0,
  "AXModal" : 1,
  "AXParent" : "<AXUIElement Application 0x60000193b5a0> {pid=8947}",
  "AXPosition" : "<AXValue 0x600001931980> {value = x:311.000000 y:45.000000 type = kAXValueCGPointType}",
  "AXProxy" : null,
  "AXRole" : "AXWindow",
  "AXSections" : null,
  "AXSize" : "<AXValue 0x60000193b5a0> {value = w:1177.000000 h:1124.000000 type = kAXValueCGSizeType}",
  "AXSubrole" : "AXDialog",
  "AXTitle" : "Open File or Project",
  "AXTitleUIElement" : null,
  "AXToolbarButton" : null,
  "AXZoomButton" : null,
  "Aero.AXApp" : {
    "AXExtrasMenuBar" : null,
    "AXFocusedUIElement" : "AXUIElement(AxWindowId=14487, title=nil, role=\"AXList\", subrole=nil)",
    "AXFocusedWindow" : "AXUIElement(AxWindowId=14486, title=\"Open File or Project\", role=\"AXWindow\", subrole=\"AXDialog\")",
    "AXFrame" : null,
    "AXFrontmost" : 1,
    "AXFunctionRowTopLevelElements" : [

    ],
    "AXMainWindow" : "AXUIElement(AxWindowId=14486, title=\"Open File or Project\", role=\"AXWindow\", subrole=\"AXDialog\")",
    "AXMenuBar" : "<AXUIElement 0x600001932f10> {pid=8947}",
    "AXPosition" : null,
    "AXRole" : "AXApplication",
    "AXSize" : null,
    "AXTitle" : "IntelliJ IDEA",
    "AXWindows" : [
      "AXUIElement(AxWindowId=14486, title=\"Open File or Project\", role=\"AXWindow\", subrole=\"AXDialog\")",
      "AXUIElement(AxWindowId=13585, title=\"jvm-gradle-sandbox [~/a/jvm-gradle-sandbox] – /Users/<USER>/a/jvm-gradle-sandbox/app/src/main/kotlin/jvm/gradle/sandbox/main.kt\", role=\"AXWindow\", subrole=\"AXStandardWindow\")",
      "AXUIElement(AxWindowId=13583, title=\"jvm-sandbox [~/a/jvm-sandbox] – /Users/<USER>/a/jvm-sandbox/src/main/kotlin/main.kt\", role=\"AXWindow\", subrole=\"AXStandardWindow\")"
    ],
    "Aero.AxIgnored" : "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"
  },
  "Aero.App.appBundleId" : "com.jetbrains.intellij",
  "Aero.App.nsApp.activationPolicy" : "regular",
  "Aero.App.nsApp.execPath" : "file:///Users/<USER>/Applications/ij-241.app/Contents/MacOS/idea",
  "Aero.App.version" : "IU-241.17011.79",
  "Aero.App.versionShort" : "2024.1.2",
  "Aero.AxIgnored" : "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription",
  "Aero.axWindowId" : 14486,
  "Aero.isDialogHeuristic" : true,
  "Aero.isWindowHeuristic" : true,
  "Aero.on-window-detected" : [

  ],
  "Aero.treeNodeParent" : "AppBundle.Workspace",
  "Aero.workspace" : "I"
}
