{"AXActivationPoint": "<AXValue 0x6000019f99b0> {value = x:-1.000000 y:1170.000000 type = kAXValueCGPointType}", "AXCancelButton": null, "AXCloseButton": null, "AXDefaultButton": null, "AXDocument": null, "AXFocused": 0, "AXFrame": "<AXValue 0x600000d50640> {value = x:569.000000 y:86.000000 w:280.000000 h:182.000000 type = kAXValueCGRectType}", "AXFullScreen": 0, "AXFullScreenButton": null, "AXGrowArea": null, "AXMain": 0, "AXMinimizeButton": null, "AXMinimized": 0, "AXModal": 0, "AXParent": "<AXUIElement Application 0x60000193bb70> {pid=1405}", "AXPosition": "<AXValue 0x6000019f99b0> {value = x:569.000000 y:86.000000 type = kAXValueCGPointType}", "AXProxy": null, "AXRole": "AXWindow", "AXSections": [], "AXSize": "<AXValue 0x600001932640> {value = w:280.000000 h:182.000000 type = kAXValueCGSizeType}", "AXSubrole": "AXUnknown", "AXTitle": "", "AXTitleUIElement": null, "AXToolbarButton": null, "AXZoomButton": null, "Aero.AXApp": {"AXExtrasMenuBar": null, "AXFocusedUIElement": "AXUIElement(AxWindowId=13054, title=nil, role=\"AXWebArea\", subrole=nil)", "AXFocusedWindow": "AXUIElement(AxWindowId=13054, title=\"Chrome extensions popup is mistakenly being detected a window. 0.18.0 regression · Issue #1324 · nikitabobko/AeroSpace\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXFrame": null, "AXFrontmost": 1, "AXFunctionRowTopLevelElements": [], "AXMainWindow": "AXUIElement(AxWindowId=13054, title=\"Chrome extensions popup is mistakenly being detected a window. 0.18.0 regression · Issue #1324 · nikitabobko/AeroSpace\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXMenuBar": "<AXUIElement 0x60000193b330> {pid=1405}", "AXPosition": null, "AXRole": "AXApplication", "AXSize": null, "AXTitle": "Firefox", "AXWindows": ["AXUIElement(AxWindowId=12431, title=\"\", role=\"AXWindow\", subrole=\"AXUnknown\")", "AXUIElement(AxWindowId=13054, title=\"Chrome extensions popup is mistakenly being detected a window. 0.18.0 regression · Issue #1324 · nikitabobko/AeroSpace\", role=\"AXWindow\", subrole=\"AXStandardWindow\")"], "Aero.AxIgnored": "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"}, "Aero.App.appBundleId": "org.mozilla.firefox", "Aero.App.nsApp.activationPolicy": "regular", "Aero.App.nsApp.execPath": "file:///Applications/Firefox.app/Contents/MacOS/firefox", "Aero.App.version": "13725.4.14", "Aero.App.versionShort": "137.0.2", "Aero.AxIgnored": "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription", "Aero.axWindowId": 12431, "Aero.isDialogHeuristic": true, "Aero.isWindowHeuristic": false, "Aero.on-window-detected": [], "Aero.treeNodeParent": "AppBundle.MacosPopupWindowsContainer", "Aero.workspace": "nil"}