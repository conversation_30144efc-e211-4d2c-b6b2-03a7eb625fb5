{"AXActivationPoint": "<AXValue 0x600001997900> {value = x:-1.000000 y:1170.000000 type = kAXValueCGPointType}", "AXCancelButton": null, "AXCloseButton": null, "AXDefaultButton": null, "AXDocument": null, "AXFocused": 0, "AXFrame": "<AXValue 0x600000d942c0> {value = x:1400.000000 y:469.000000 w:166.000000 h:122.000000 type = kAXValueCGRectType}", "AXFullScreen": 0, "AXFullScreenButton": null, "AXGrowArea": null, "AXMain": 0, "AXMinimizeButton": null, "AXMinimized": 0, "AXModal": 0, "AXParent": "<AXUIElement Application 0x6000018d09f0> {pid=45633}", "AXPosition": "<AXValue 0x6000018d09f0> {value = x:1400.000000 y:469.000000 type = kAXValueCGPointType}", "AXProxy": null, "AXRole": "AXWindow", "AXSections": ["{\n    SectionDescription = Content;\n    SectionObject = \"<AXUIElement 0x600001994300> {pid=45633}\";\n    SectionUniqueID = AXContent;\n}"], "AXSize": "<AXValue 0x6000019aa070> {value = w:166.000000 h:122.000000 type = kAXValueCGSizeType}", "AXSubrole": "AXDialog", "AXTitle": "", "AXTitleUIElement": null, "AXToolbarButton": null, "AXZoomButton": null, "Aero.AXApp": {"AXExtrasMenuBar": null, "AXFocusedUIElement": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser\", role=\"AXGroup\", subrole=nil)", "AXFocusedWindow": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXFrame": null, "AXFrontmost": 1, "AXFunctionRowTopLevelElements": [], "AXMainWindow": "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")", "AXMenuBar": "<AXUIElement 0x6000018ce040> {pid=45633}", "AXPosition": null, "AXRole": "AXApplication", "AXSize": null, "AXTitle": "qute<PERSON><PERSON>er", "AXWindows": ["AXUIElement(AxWindowId=14637, title=\"\", role=\"AXWindow\", subrole=\"AXDialog\")", "AXUIElement(AxWindowId=14631, title=\"qutebrowser quickstart | qutebrowser - qutebrowser\", role=\"AXWindow\", subrole=\"AXStandardWindow\")"], "Aero.AxIgnored": "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"}, "Aero.App.appBundleId": "org.qutebrowser.qutebrowser", "Aero.App.nsApp.activationPolicy": "regular", "Aero.App.nsApp.execPath": "file:///Applications/qutebrowser.app/Contents/MacOS/qutebrowser", "Aero.App.version": "3.5.0", "Aero.App.versionShort": "3.5.0", "Aero.AxIgnored": "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription", "Aero.axWindowId": 14637, "Aero.isDialogHeuristic": true, "Aero.isWindowHeuristic": false, "Aero.on-window-detected": [], "Aero.treeNodeParent": "AppBundle.MacosPopupWindowsContainer", "Aero.workspace": "nil"}