{
  "AXActivationPoint" : "<AXValue 0x6000019329d0> {value = x:-1.000000 y:1170.000000 type = kAXValueCGPointType}",
  "AXCancelButton" : null,
  "AXCloseButton" : null,
  "AXDefaultButton" : null,
  "AXDocument" : null,
  "AXFocused" : 1,
  "AXFrame" : "<AXValue 0x6000002e1d00> {value = x:0.000000 y:0.000000 w:1800.000000 h:1169.000000 type = kAXValueCGRectType}",
  "AXFullScreen" : 0,
  "AXFullScreenButton" : null,
  "AXGrowArea" : null,
  "AXMain" : 1,
  "AXMinimizeButton" : null,
  "AXMinimized" : 0,
  "AXModal" : 0,
  "AXParent" : "<AXUIElement Application 0x6000018cff60> {pid=38857}",
  "AXPosition" : "<AXValue 0x600001995c80> {value = x:0.000000 y:0.000000 type = kAXValueCGPointType}",
  "AXProxy" : null,
  "AXRole" : "AXWindow",
  "AXSections" : [

  ],
  "AXSize" : "<AXValue 0x6000019943f0> {value = w:1800.000000 h:1169.000000 type = kAXValueCGSizeType}",
  "AXSubrole" : "AXUnknown",
  "AXTitle" : "The past, present, and future of local-first - Martin Kleppmann (Local-First Conf) [NMq0vncHJvU].mkv - mpv",
  "AXTitleUIElement" : null,
  "AXToolbarButton" : null,
  "AXZoomButton" : null,
  "Aero.AXApp" : {
    "AXExtrasMenuBar" : null,
    "AXFocusedUIElement" : "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - Martin Kleppmann (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXUnknown\")",
    "AXFocusedWindow" : "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - Martin Kleppmann (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXUnknown\")",
    "AXFrame" : null,
    "AXFrontmost" : 1,
    "AXFunctionRowTopLevelElements" : [

    ],
    "AXMainWindow" : "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - Martin Kleppmann (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXUnknown\")",
    "AXMenuBar" : "<AXUIElement 0x600001932640> {pid=38857}",
    "AXPosition" : null,
    "AXRole" : "AXApplication",
    "AXSize" : null,
    "AXTitle" : "mpv",
    "AXWindows" : [
      "AXUIElement(AxWindowId=14286, title=\"The past, present, and future of local-first - Martin Kleppmann (Local-First Conf) [NMq0vncHJvU].mkv - mpv\", role=\"AXWindow\", subrole=\"AXUnknown\")"
    ],
    "Aero.AxIgnored" : "AXChildren, AXChildrenInNavigationOrder, AXEnhancedUserInterface, AXPreferredLanguage, AXRoleDescription, AXHidden"
  },
  "Aero.App.appBundleId" : "NULL-APP-BUNDLE-ID",
  "Aero.App.nsApp.activationPolicy" : "regular",
  "Aero.App.nsApp.execPath" : "file:///opt/homebrew/bin/mpv",
  "Aero.App.version" : null,
  "Aero.App.versionShort" : null,
  "Aero.AxIgnored" : "AXChildrenInNavigationOrder, AXChildren, AXRoleDescription",
  "Aero.axWindowId" : 14286,
  "Aero.isDialogHeuristic" : true,
  "Aero.isWindowHeuristic" : false, // todo fix?
  "Aero.on-window-detected" : [

  ],
  "Aero.treeNodeParent" : "AppBundle.MacosPopupWindowsContainer",
  "Aero.workspace" : "nil"
}
