# LICENSE

The AeroSpace itself is licensed under MIT. See [LICENSE](./LICENSE.txt) for the full license text.

## Bundled dependencies and materials

AeroSpace bundles the following dependencies and uses the following materials:

**BlueSocket**.
[BlueSocket GitHub link](https://github.com/Kitura/BlueSocket).
[BlueSocket Apache 2.0 license](./third-party-license/LICENSE-BlueSocket.txt).
BlueSocket is used as a more convenient Swift wrapper around UNIX C socket API.

**HotKey**.
[HotKey GitHub link](https://github.com/soffes/HotKey).
[HotKey MIT license](./third-party-license/LICENSE-HotKey.txt).
HotKey is used as a more convenient wrapper around macOS Carbon API to listen for global shortcuts.

**TOMLKIT**.
[TOMLKIT GitHub link](https://github.com/LebJe/TOMLKit).
[TOMLKIT MIT license](./third-party-license/LICENSE-TOMLKIT.txt).
TOMLKIT is used as a more convenient Swift wrapper around tomlplusplus C++ API.

**tomlplusplus**.
[tomlplusplus GitHub link](https://github.com/marzer/tomlplusplus).
[tomlplusplus MIT license](./third-party-license/LICENSE-tomlplusplus.txt).
tomlplusplus is used as TOML parser. tomlplusplus is used indirectly through TOMLKIT Swift API.

**ANTLR v4**.
[ANTLR v4 GitHub link](https://github.com/antlr/antlr4).
[ANTLR BSD-3 license](./third-party-license/LICENSE-antlr.txt).
ANTLR is used to parse AeroSpace built-in shell like language.

**swift-collections**.
[swift-collections GitHub link](https://github.com/apple/swift-collections).
[swift-collections Apache 2.0 license](./third-party-license/LICENSE-swift-collections.txt).
swift-collections is used for more advanced Swift collections.

**ISSoundAdditions**
[ISSoundAdditions GitHub link](https://github.com/InerziaSoft/ISSoundAdditions).
[ISSoundAdditions MIT license](./third-party-license/LICENSE-ISSoundAdditions.txt).
ISSoundAdditions is used as a convenient API to change system volume.
