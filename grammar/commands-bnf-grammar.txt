# EBNF-like grammar of aerospace CLI args to generate shell completion. Managed by https://github.com/adaszko/complgen
# Run ./build-shell-completion.sh to generate shell completion
aerospace <subcommand>;
aerospace -v;
aerospace --version;
aerospace --help;
aerospace -h;

<subcommand> ::=

    balance-sizes [--workspace <workspace>]

    | close [--quit-if-last-window|--window-id <window_id>]...

    | close-all-windows-but-current [--quit-if-last-window]

    | enable toggle
    | enable [--fail-if-noop] on [--fail-if-noop]
    | enable [--fail-if-noop] off [--fail-if-noop]

    | flatten-workspace-tree [--workspace <workspace>]

    | focus [<focus_flag>]... (left|down|up|right) [<focus_flag>]...
    | focus --window-id <window_id>
    | focus --dfs-index <number>

    | focus-back-and-forth

    | focus-monitor [--wrap-around] (left|down|up|right) [--wrap-around]
    | focus-monitor [--wrap-around] (next|prev) [--wrap-around]
    | focus-monitor <monitor_pattern>...

    | fullscreen [--no-outer-gaps|--window-id <window_id>]...
    | fullscreen [--no-outer-gaps|--fail-if-noop|--window-id <window_id>]... on [--no-outer-gaps|--fail-if-noop|--window-id <window_id>]...
    | fullscreen [--fail-if-noop|--window-id <window_id>] off [--fail-if-noop|--window-id <window_id>]

    | join-with [--window-id <window_id>] (left|down|up|right)

    | layout [--window-id <window_id>] (h_tiles|v_tiles|h_accordion|v_accordion|tiles|accordion|horizontal|vertical|tiling|floating)...

    | macos-native-fullscreen [--fail-if-noop|--window-id <window_id>]... [on|off] [--fail-if-noop|--window-id <window_id>]...

    | macos-native-minimize [--window-id <window_id>]

    | mode <mode_id>

    | move [--window-id <window_id>] (left|down|up|right) [--window-id <window_id>]

    | move-mouse [--fail-if-noop] (monitor-lazy-center|monitor-force-center|window-lazy-center|window-force-center) [--fail-if-noop]

    | move-node-to-monitor [<move_node_to_monitor1_flag>]... (left|down|up|right|next|prev) [<move_node_to_monitor1_flag>]...
    | move-node-to-monitor [<move_node_to_monitor2_flag>]... <monitor_pattern>... [<move_node_to_monitor2_flag>]...

    | move-node-to-workspace [--wrap-around|--focus-follows-window]... (next|prev) [--wrap-around|--focus-follows-window]...
    | move-node-to-workspace [--fail-if-noop|--window-id <window_id>|--focus-follows-window]... <workspace> [--fail-if-noop|--window-id <window_id>|--focus-follows-window]...

    | move-workspace-to-monitor [--wrap-around] (left|down|up|right|next|prev) [--wrap-around]
    | move-workspace-to-monitor [--wrap-around] <monitor_pattern>... [--wrap-around]

    | reload-config [--no-gui | --dry-run]...

    | resize [--window-id <window_id>] (smart|smart-opposite|width|height) [+|-]<number> [--window-id <window_id>]

    | split [--window-id <window_id>] (horizontal|vertical|opposite) [--window-id <window_id>]

    | summon-workspace [--fail-if-noop] <workspace> [--fail-if-noop]

    | trigger-binding <binding> --mode <mode_id>
    | trigger-binding --mode <mode_id> <binding>

    | volume (up|down)
    | volume (mute-toggle|mute-on|mute-off)
    | volume set <number>

    | workspace [--auto-back-and-forth|--fail-if-noop]... <workspace> [--auto-back-and-forth|--fail-if-noop]...
    | workspace [--wrap-around] (next|prev) [--wrap-around]

    | workspace-back-and-forth

    | config [--json | --keys]... --get {{{ aerospace config --major-keys }}} [--json | --keys]...
    | config --major-keys
    | config --all-keys
    | config --config-path

    | debug-windows [--window-id <window_id>]

    | list-apps [--macos-native-hidden [no] | --format <output_format> | --count | --json]...

    | list-exec-env-vars

    | list-modes [--current]

    | list-monitors [--focused [no] | --mouse [no] | --format <output_format> | --count | --json]...

    | list-windows [<list_windows_filter_flag> | --format <output_format> | --count | --json]...
    | list-windows [--format <output_format>|--count|--json] (--all|--focused) [--format <output_format>|--count|--json]

    | list-workspaces [<list_workspaces1_flag>]... --monitor <monitor_id>... [<list_workspaces1_flag>]...
    | list-workspaces [--format <output_format>|--count|--json] (--all|--focused) [--format <output_format>|--count|--json]
    ;

<window_id> ::= {{{ aerospace list-windows --all --format '%{window-id}%{tab}%{app-name} - %{window-title}' }}};
<binding> ::= {{{ aerospace config --get mode --keys | xargs -I{} aerospace config --get mode.{}.binding --keys }}};
<mode_id> ::= {{{ aerospace config --get mode --keys }}};
<workspace> ::= {{{ aerospace list-workspaces --monitor all --empty no }}};
<number> ::= {{{ true }}};
<monitor_pattern> ::= {{{ true }}};

<list_workspaces1_flag> ::= --visible [no] | --empty [no] | --format <output_format> | --format <output_format> | --count | --json;

<move_node_to_monitor1_flag> ::= --window-id <window_id>|--focus-follows-window|--fail-if-noop|--wrap-around;
<move_node_to_monitor2_flag> ::= --window-id <window_id>|--focus-follows-window|--fail-if-noop;

<focus_flag> ::= --boundaries <boundary>|--boundaries-action <boundaries_action>|--ignore-floating;
<boundaries_action> ::= stop|fail|wrap-around-the-workspace|wrap-around-all-monitors;
<boundary> ::= workspace|all-monitors-outer-frame;

<list_windows_filter_flag> ::= --workspace (visible | focused | <workspace>)...
    | --monitor <monitor_id>...
    | --pid <pid>
    | --app-bundle-id <app_bundle_id>
    ;
<output_format> ::= {{{ true }}};

<app_bundle_id> ::= {{{ aerospace list-apps --format '%{app-bundle-id}%{tab}%{app-name}' }}};
<pid> ::= {{{ aerospace list-apps --format '%{app-pid}%{tab}%{app-name}' }}};
<monitor_id> ::= all | mouse | focused | {{{ aerospace list-monitors --format '%{monitor-id}%{tab}%{monitor-name}' }}};
