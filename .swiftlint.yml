only_rules:
  - accessibility_trait_for_button
  - array_init
  - blanket_disable_command
  - block_based_kvo
  - class_delegate_protocol
  - closing_brace
  - closure_end_indentation
  - closure_parameter_position
  - closure_spacing
  - collection_alignment
  - colon
  - comma
  - comma_inheritance
  - compiler_protocol_init
  - computed_accessors_order
  - conditional_returns_on_newline
  - contains_over_filter_count
  - contains_over_filter_is_empty
  - contains_over_first_not_nil
  - contains_over_range_nil_comparison
  - control_statement
  - custom_rules
  - deployment_target
  - direct_return
  - discarded_notification_center_observer
  - discouraged_assert
  - discouraged_direct_init
  - discouraged_none_name
  - discouraged_object_literal
  - discouraged_optional_boolean
  - discouraged_optional_collection
  - duplicate_conditions
  - duplicate_enum_cases
  - duplicate_imports
  - duplicated_key_in_dictionary_literal
  - dynamic_inline
  - empty_collection_literal
  - empty_count
  - empty_enum_arguments
  - empty_parameters
  - empty_parentheses_with_trailing_closure
  - empty_string
  - empty_xctest_method
  - enum_case_associated_values_count
  - explicit_init
  - fallthrough
  - fatal_error_message
  - final_test_case
  - first_where
  - flatmap_over_map_reduce
  - for_where
  - generic_type_name
  - ibinspectable_in_extension
  - identical_operands
  - identifier_name
  - implicit_getter
  - implicit_return
  - inclusive_language
  - invalid_swiftlint_command
  - is_disjoint
  - joined_default_parameter
  - last_where
  - leading_whitespace
  - legacy_cggeometry_functions
  - legacy_constant
  - legacy_constructor
  - legacy_hashing
  - legacy_multiple
  - legacy_nsgeometry_functions
  - legacy_random
  - literal_expression_end_indentation
  - lower_acl_than_parent
  - mark
  - modifier_order
  - multiline_arguments
  - multiline_arguments_brackets
  - multiline_function_chains
  - multiline_literal_brackets
  - multiline_parameters
  - multiline_parameters_brackets
  - nimble_operator
  - no_extension_access_modifier
  - no_fallthrough_only
  - no_space_in_method_call
  - non_optional_string_data_conversion
  - non_overridable_class_declaration
  - notification_center_detachment
  - ns_number_init_as_function_reference
  - nsobject_prefer_isequal
  - number_separator
  - operator_usage_whitespace
  - operator_whitespace
  - overridden_super_call
  - prefer_self_in_static_references
  - prefer_self_type_over_type_of_self
  - prefer_zero_over_explicit_init
  - private_action
  - private_outlet
  - private_subject
  - private_swiftui_state
  - private_unit_test
  - prohibited_super_call
  - protocol_property_accessors_order
  - reduce_boolean
  - reduce_into
  - redundant_discardable_let
  - redundant_nil_coalescing
  - redundant_objc_attribute
  - redundant_optional_initialization
  - redundant_set_access_control
  - redundant_string_enum_value
  - redundant_type_annotation
  - redundant_void_return
  - required_enum_case
  - return_arrow_whitespace
  - return_value_from_void_function
  - self_binding
  - self_in_property_initialization
  - shorthand_operator
  - shorthand_optional_binding
  - sorted_first_last
  - statement_position
  - static_operator
  - static_over_final_class
  - strong_iboutlet
  - superfluous_disable_command
  - superfluous_else
  - switch_case_alignment
  - switch_case_on_newline
  - syntactic_sugar
  - test_case_accessibility
  - toggle_bool
  - trailing_closure
  - trailing_comma
  - trailing_newline
  - trailing_semicolon
  - trailing_whitespace
  - unavailable_condition
  - unavailable_function
  - unneeded_break_in_switch
  - unneeded_override
  - unneeded_parentheses_in_closure_argument
  - unowned_variable_capture
  - untyped_error_in_catch
  - unused_closure_parameter
  - unused_control_flow_label
  - unused_enumerated
  - unused_optional_binding
  - unused_setter_value
  - valid_ibinspectable
  - vertical_parameter_alignment
  - vertical_parameter_alignment_on_call
  - vertical_whitespace_closing_braces
  - vertical_whitespace_opening_braces
  - void_function_in_ternary
  - void_return
  - xct_specific_matcher
  - xctfail_message
  - yoda_condition
analyzer_rules:
  - capture_variable
  - typesafe_array_init
  - unneeded_synthesized_initializer
  - unused_declaration
  - unused_import
for_where:
  allow_for_as_filter: true
number_separator:
  minimum_length: 5
identifier_name:
  max_length:
    warning: 100
    error: 100
  min_length:
    warning: 2
    error: 2
  allowed_symbols:
    - '_'
  excluded:
    - 'x'
    - 'y'
    - 'z'
    - 'a'
    - 'b'
    - 'x1'
    - 'x2'
    - 'y1'
    - 'y2'
    - 'z2'
redundant_type_annotation:
  consider_default_literal_types_redundant: true
unneeded_override:
  affect_initializers: true
deployment_target:
  macOS_deployment_target: '10.11'
custom_rules:
  no_nsrect:
    regex: '\bNSRect\b'
    match_kinds: typeidentifier
    message: 'Use CGRect instead of NSRect'
  no_nssize:
    regex: '\bNSSize\b'
    match_kinds: typeidentifier
    message: 'Use CGSize instead of NSSize'
  no_nspoint:
    regex: '\bNSPoint\b'
    match_kinds: typeidentifier
    message: 'Use CGPoint instead of NSPoint'
  no_cgfloat:
    regex: '\bCGFloat\b'
    match_kinds: typeidentifier
    message: 'Use Double instead of CGFloat'
  no_cgfloat2:
    regex: '\bCGFloat\('
    message: 'Use Double instead of CGFloat'
  swiftui_state_private:
    regex: '@(ObservedObject|EnvironmentObject)\s+var'
    message: 'SwiftUI @ObservedObject and @EnvironmentObject properties should be private'
  swiftui_environment_private:
    regex: '@Environment\(\\\.\w+\)\s+var'
    message: 'SwiftUI @Environment properties should be private'
  final_class:
    regex: '^class [a-zA-Z\d]+[^{]+\{'
    message: 'Classes should be marked as final whenever possible. If you actually need it to be subclassable, just add `// swiftlint:disable:next final_class`.'
  no_alignment_center:
    regex: '\b\(alignment: .center\b'
    message: 'This alignment is the default.'
