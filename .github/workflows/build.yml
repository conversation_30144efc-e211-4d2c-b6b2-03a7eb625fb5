name: Build

on: [push, pull_request]

permissions: read-all

env:
  XCODE_PROJECT: "Rectangle.xcodeproj"
  XCODE_SCHEME: "Rectangle"
  CODE_SIGN_IDENTITY: "-"
  BUILD_DIR: "build"
  XCODE_ARCHIVE: "Rectangle.xcarchive"
  APP_NAME: "Rectangle.app"
  EXPORT_OPTIONS_PLIST: "exportOptions.plist"
  DMG_NAME: "Rectangle"
  DMG_FILE_NAME: "Rectangle.dmg"

jobs:
  build:
    runs-on: macos-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Build
      run: xcodebuild -project "$XCODE_PROJECT" -scheme "$XCODE_SCHEME" archive CODE_SIGN_IDENTITY="$CODE_SIGN_IDENTITY" -archivePath "$BUILD_DIR/$XCODE_ARCHIVE"
    - name: Export
      run: |
        plutil -convert xml1 - -o "$EXPORT_OPTIONS_PLIST" << EOF
          {
            "destination": "export",
            "method": "mac-application"
          }
        EOF

        xcodebuild -exportArchive -archivePath "$BUILD_DIR/$XCODE_ARCHIVE" -exportPath "$BUILD_DIR" -exportOptionsPlist "$EXPORT_OPTIONS_PLIST"
    - name: Resign App
      run: codesign --force --deep -s "$CODE_SIGN_IDENTITY" "$BUILD_DIR/$APP_NAME"
    - name: Make DMG
      run: hdiutil create -srcdir "$BUILD_DIR" -volname "$DMG_NAME" "$DMG_FILE_NAME"
    - name: Upload
      uses: actions/upload-artifact@v4
      with:
        name: Build
        path: ${{ env.DMG_FILE_NAME }}
