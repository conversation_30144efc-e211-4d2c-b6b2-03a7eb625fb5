// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		12D896200936C0DE9FF41FB4 /* (null) in Frameworks */ = {isa = PBXBuildFile; };
		30166BD024F27D6A00A38608 /* SpecifiedCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 30166BCF24F27D6A00A38608 /* SpecifiedCalculation.swift */; };
		6490B39127BF907A0056C220 /* BottomLeftEighthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6490B39027BF907A0056C220 /* BottomLeftEighthCalculation.swift */; };
		6490B39327BF90F90056C220 /* EighthsRepeated.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6490B39227BF90F90056C220 /* EighthsRepeated.swift */; };
		6490B39527BF96880056C220 /* TopLeftEighthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6490B39427BF96880056C220 /* TopLeftEighthCalculation.swift */; };
		6490B39727BF96EA0056C220 /* TopCenterLeftEighthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6490B39627BF96EA0056C220 /* TopCenterLeftEighthCalculation.swift */; };
		6490B39927BF97BB0056C220 /* TopCenterRightEighthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6490B39827BF97BB0056C220 /* TopCenterRightEighthCalculation.swift */; };
		6490B39B27BF980F0056C220 /* TopRightEighthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6490B39A27BF980F0056C220 /* TopRightEighthCalculation.swift */; };
		6490B39D27BF984D0056C220 /* BottomCenterLeftEighthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6490B39C27BF984D0056C220 /* BottomCenterLeftEighthCalculation.swift */; };
		6490B39F27BF98840056C220 /* BottomCenterRightEighthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6490B39E27BF98840056C220 /* BottomCenterRightEighthCalculation.swift */; };
		6490B3A127BF98C70056C220 /* BottomRightEighthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6490B3A027BF98C70056C220 /* BottomRightEighthCalculation.swift */; };
		729E0A982AFF76B1006E2F48 /* CenterProminentlyCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 729E0A972AFF76B1006E2F48 /* CenterProminentlyCalculation.swift */; };
		7BE578EF2C5BF4EE0083DAE3 /* CycleSize.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7BE578EE2C5BF4ED0083DAE3 /* CycleSize.swift */; };
		866661F2257D248A00A9CD2D /* RepeatedExecutionsInThirdsCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 866661F1257D248A00A9CD2D /* RepeatedExecutionsInThirdsCalculation.swift */; };
		944F25CD2CE5A144004B2FD2 /* PrefsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 944F25CC2CE5A144004B2FD2 /* PrefsViewController.swift */; };
		94E9B08E2C3B8D97004C7F41 /* MacTilingDefaults.swift in Sources */ = {isa = PBXBuildFile; fileRef = 94E9B08D2C3B8D97004C7F41 /* MacTilingDefaults.swift */; };
		94E9B0902C3E4578004C7F41 /* StringExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 94E9B08F2C3E4578004C7F41 /* StringExtension.swift */; };
		9818E00D28B59205004AA524 /* CompoundSnapArea.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9818E00C28B59205004AA524 /* CompoundSnapArea.swift */; };
		9818E01028B59396004AA524 /* HalvesCompoundCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9818E00F28B59396004AA524 /* HalvesCompoundCalculation.swift */; };
		9818E01228B59B64004AA524 /* ThirdsCompoundCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9818E01128B59B64004AA524 /* ThirdsCompoundCalculation.swift */; };
		9818E01428B5A4FD004AA524 /* SixthsCompoundCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9818E01328B5A4FD004AA524 /* SixthsCompoundCalculation.swift */; };
		9818E01828B63C48004AA524 /* FourthsCompoundCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9818E01728B63C48004AA524 /* FourthsCompoundCalculation.swift */; };
		98192DDA270F606C00015E66 /* Debounce.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98192DD9270F606C00015E66 /* Debounce.swift */; };
		98192DDE2717201100015E66 /* ReverseAllManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98192DDD2717201000015E66 /* ReverseAllManager.swift */; };
		981F27D12340E3E1006CD263 /* InternetAccessPolicy.plist in Resources */ = {isa = PBXBuildFile; fileRef = 981F27D02340E3E1006CD263 /* InternetAccessPolicy.plist */; };
		9821402122B3884600ABFB3F /* BottomHalfCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9821402022B3884600ABFB3F /* BottomHalfCalculation.swift */; };
		9821402322B3886100ABFB3F /* CenterCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9821402222B3886100ABFB3F /* CenterCalculation.swift */; };
		9821402522B3887200ABFB3F /* MaximizeCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9821402422B3887200ABFB3F /* MaximizeCalculation.swift */; };
		9821402722B3888100ABFB3F /* ChangeSizeCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9821402622B3888100ABFB3F /* ChangeSizeCalculation.swift */; };
		9821402922B3889100ABFB3F /* LowerLeftCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9821402822B3889100ABFB3F /* LowerLeftCalculation.swift */; };
		9821402B22B388A000ABFB3F /* LowerRightCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9821402A22B388A000ABFB3F /* LowerRightCalculation.swift */; };
		9821403122B38A0500ABFB3F /* TopHalfCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9821403022B38A0500ABFB3F /* TopHalfCalculation.swift */; };
		9821403322B38A1B00ABFB3F /* UpperLeftCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9821403222B38A1B00ABFB3F /* UpperLeftCalculation.swift */; };
		9821403522B38A2B00ABFB3F /* UpperRightCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9821403422B38A2B00ABFB3F /* UpperRightCalculation.swift */; };
		9821403722B3D16700ABFB3F /* MaximizeHeightCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9821403622B3D16700ABFB3F /* MaximizeHeightCalculation.swift */; };
		9821403F22B3EBD900ABFB3F /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9821403E22B3EBD900ABFB3F /* AppDelegate.swift */; };
		9821404322B3EBDA00ABFB3F /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 9821404222B3EBDA00ABFB3F /* Assets.xcassets */; };
		9821404622B3EBDA00ABFB3F /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 9821404422B3EBDA00ABFB3F /* Main.storyboard */; };
		9821405B22B3ECCA00ABFB3F /* RectangleLauncher.app in CopyFiles */ = {isa = PBXBuildFile; fileRef = 9821403C22B3EBD900ABFB3F /* RectangleLauncher.app */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		9821405E22B3ED3600ABFB3F /* ServiceManagement.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9821405D22B3ED3600ABFB3F /* ServiceManagement.framework */; };
		9821406022B3EFB200ABFB3F /* Defaults.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9821405F22B3EFB200ABFB3F /* Defaults.swift */; };
		9824700D22AF9B7D0037B409 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824700C22AF9B7D0037B409 /* AppDelegate.swift */; };
		9824701122AF9B7E0037B409 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 9824701022AF9B7E0037B409 /* Assets.xcassets */; };
		9824701422AF9B7E0037B409 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 9824701222AF9B7E0037B409 /* Main.storyboard */; };
		9824702022AF9B7E0037B409 /* RectangleTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824701F22AF9B7E0037B409 /* RectangleTests.swift */; };
		9824702C22AFA22E0037B409 /* AccessibilityWindowController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824702A22AFA22E0037B409 /* AccessibilityWindowController.swift */; };
		9824702F22AFA2E50037B409 /* AccessibilityAuthorization.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824702E22AFA2E50037B409 /* AccessibilityAuthorization.swift */; };
		9824703122AFA8470037B409 /* RectangleStatusItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824703022AFA8470037B409 /* RectangleStatusItem.swift */; };
		9824703722B0F3200037B409 /* WindowAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824703622B0F3200037B409 /* WindowAction.swift */; };
		9824703922B0F37C0037B409 /* ShortcutManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824703822B0F37C0037B409 /* ShortcutManager.swift */; };
		9824703B22B139780037B409 /* CUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824703A22B139780037B409 /* CUtil.swift */; };
		9824703D22B13C7E0037B409 /* AccessibilityElement.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824703C22B13C7E0037B409 /* AccessibilityElement.swift */; };
		9824703F22B13FBC0037B409 /* ScreenDetection.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824703E22B13FBC0037B409 /* ScreenDetection.swift */; };
		9824704122B186D00037B409 /* WindowManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824704022B186D00037B409 /* WindowManager.swift */; };
		9824704B22B189250037B409 /* StandardWindowMover.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824704622B189240037B409 /* StandardWindowMover.swift */; };
		9824704C22B189250037B409 /* WindowMover.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824704722B189240037B409 /* WindowMover.swift */; };
		9824704D22B189250037B409 /* WindowCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824704822B189250037B409 /* WindowCalculation.swift */; };
		9824704E22B189250037B409 /* QuantizedWindowMover.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824704922B189250037B409 /* QuantizedWindowMover.swift */; };
		9824704F22B189250037B409 /* BestEffortWindowMover.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824704A22B189250037B409 /* BestEffortWindowMover.swift */; };
		9824705122B28D7A0037B409 /* LeftRightHalfCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9824705022B28D7A0037B409 /* LeftRightHalfCalculation.swift */; };
		983BBD6F253B609D000D223E /* FootprintWindow.swift in Sources */ = {isa = PBXBuildFile; fileRef = 983BBD6E253B609D000D223E /* FootprintWindow.swift */; };
		983DD04028A844BE00BF1EEE /* SnapAreaViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 983DD03F28A844BE00BF1EEE /* SnapAreaViewController.swift */; };
		983DD04428B0639E00BF1EEE /* SnapAreaModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 983DD04328B0639E00BF1EEE /* SnapAreaModel.swift */; };
		984EDB0F29A42ED200D119D2 /* LaunchOnLogin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 984EDB0E29A42ED200D119D2 /* LaunchOnLogin.swift */; };
		9851A5C3251BEBA300ECF78C /* OrientationAware.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9851A5C2251BEBA300ECF78C /* OrientationAware.swift */; };
		985B9BF522B93EEC00A2E8F0 /* ApplicationToggle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 985B9BF422B93EEC00A2E8F0 /* ApplicationToggle.swift */; };
		985B9BF822BB6F5100A2E8F0 /* MessageView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 985B9BF622BB6F5000A2E8F0 /* MessageView.xib */; };
		985B9BF922BB6F5100A2E8F0 /* MessageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 985B9BF722BB6F5000A2E8F0 /* MessageView.swift */; };
		9877B63A29C8AC0E00F02D74 /* MASShortcut in Frameworks */ = {isa = PBXBuildFile; productRef = 9877B63929C8AC0E00F02D74 /* MASShortcut */; };
		9877B63D29C8AC3600F02D74 /* Sparkle in Frameworks */ = {isa = PBXBuildFile; productRef = 9877B63C29C8AC3600F02D74 /* Sparkle */; };
		988D066122EB4C7C004EABD7 /* FirstThirdCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 988D066022EB4C7C004EABD7 /* FirstThirdCalculation.swift */; };
		988D066322EB4CA5004EABD7 /* FirstTwoThirdsCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 988D066222EB4CA5004EABD7 /* FirstTwoThirdsCalculation.swift */; };
		988D066522EB4CB6004EABD7 /* CenterThirdCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 988D066422EB4CB5004EABD7 /* CenterThirdCalculation.swift */; };
		988D066722EB4CC0004EABD7 /* LastTwoThirdsCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 988D066622EB4CC0004EABD7 /* LastTwoThirdsCalculation.swift */; };
		988D066922EB4CCB004EABD7 /* LastThirdCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 988D066822EB4CCB004EABD7 /* LastThirdCalculation.swift */; };
		988D067D22EB4E17004EABD7 /* AlmostMaximizeCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 988D067C22EB4E17004EABD7 /* AlmostMaximizeCalculation.swift */; };
		988D067F22EB4EDE004EABD7 /* MoveLeftRightCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 988D067E22EB4EDE004EABD7 /* MoveLeftRightCalculation.swift */; };
		988D068322EB4EF3004EABD7 /* MoveUpDownCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 988D068222EB4EF3004EABD7 /* MoveUpDownCalculation.swift */; };
		98910B3E231130AF0066EC23 /* SettingsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98910B3D231130AF0066EC23 /* SettingsViewController.swift */; };
		98987AA42391890400BE72C4 /* LogViewer.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 98987AA22391890300BE72C4 /* LogViewer.storyboard */; };
		98987AA52391890400BE72C4 /* LogViewer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98987AA32391890400BE72C4 /* LogViewer.swift */; };
		989DA30E243FC0DF008C7AA4 /* WelcomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 989DA30D243FC0DF008C7AA4 /* WelcomeViewController.swift */; };
		98A009AB2512491300CFBF0C /* CenterHalfCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98A009AA2512491300CFBF0C /* CenterHalfCalculation.swift */; };
		98A009AD2512498000CFBF0C /* FirstFourthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98A009AC2512498000CFBF0C /* FirstFourthCalculation.swift */; };
		98A009AF2512517900CFBF0C /* SecondFourthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98A009AE2512517900CFBF0C /* SecondFourthCalculation.swift */; };
		98A009B1251252C900CFBF0C /* ThirdFourthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98A009B0251252C900CFBF0C /* ThirdFourthCalculation.swift */; };
		98A009B32512536900CFBF0C /* LastFourthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98A009B22512536800CFBF0C /* LastFourthCalculation.swift */; };
		98A009B52512537800CFBF0C /* TopLeftSixthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98A009B42512537800CFBF0C /* TopLeftSixthCalculation.swift */; };
		98A009B72512538200CFBF0C /* TopCenterSixthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98A009B62512538200CFBF0C /* TopCenterSixthCalculation.swift */; };
		98A009B92512538D00CFBF0C /* TopRightSixthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98A009B82512538D00CFBF0C /* TopRightSixthCalculation.swift */; };
		98A009BB2512539900CFBF0C /* BottomLeftSixthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98A009BA2512539900CFBF0C /* BottomLeftSixthCalculation.swift */; };
		98A009BD251253A000CFBF0C /* BottomCenterSixthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98A009BC251253A000CFBF0C /* BottomCenterSixthCalculation.swift */; };
		98A009BF251253AB00CFBF0C /* BottomRightSixthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98A009BE251253AB00CFBF0C /* BottomRightSixthCalculation.swift */; };
		98A6EDDD251F3F4A00F74B10 /* SixthsRepeated.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98A6EDDC251F3F4A00F74B10 /* SixthsRepeated.swift */; };
		98A6EDEC2528FFC100F74B10 /* WindowActionCategory.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98A6EDEB2528FFC100F74B10 /* WindowActionCategory.swift */; };
		98B3559823CE025700E410E0 /* CenteringFixedSizedWindowMover.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98B3559723CE025700E410E0 /* CenteringFixedSizedWindowMover.swift */; };
		98BEFA482620DEDD00D9D54F /* NSImageExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98BEFA472620DEDC00D9D54F /* NSImageExtension.swift */; };
		98C1008C2305F1FA006E5344 /* SubsequentExecutionMode.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98C1008B2305F1FA006E5344 /* SubsequentExecutionMode.swift */; };
		98C1008E230B9EF6006E5344 /* NextPrevDisplayCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98C1008D230B9EF6006E5344 /* NextPrevDisplayCalculation.swift */; };
		98C2755E231FF6A9009B9292 /* EventMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98C2755D231FF6A9009B9292 /* EventMonitor.swift */; };
		98C27561231FFA5F009B9292 /* SnappingManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98C27560231FFA5F009B9292 /* SnappingManager.swift */; };
		98C275672322E2DA009B9292 /* WindowHistory.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98C275662322E2DA009B9292 /* WindowHistory.swift */; };
		98C6DEF023CE191700CC0C1E /* GapCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98C6DEEF23CE191700CC0C1E /* GapCalculation.swift */; };
		98C97FFD25893B040061F01F /* Config.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98C97FFC25893B040061F01F /* Config.swift */; };
		98D1441324560B1E0090C603 /* AlertUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98D1441224560B1E0090C603 /* AlertUtil.swift */; };
		98D16A442592AD55005228CB /* MASShortcutMigration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98D16A432592AD55005228CB /* MASShortcutMigration.swift */; };
		98D16A492592B460005228CB /* NotificationExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98D16A482592B460005228CB /* NotificationExtension.swift */; };
		98D4B6C525B6256C009C7BF6 /* TodoManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98D4B6C425B6256C009C7BF6 /* TodoManager.swift */; };
		98FA9497235A2D7600F95C4F /* RepeatedExecutionsCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98FA9496235A2D7600F95C4F /* RepeatedExecutionsCalculation.swift */; };
		98FD7C5F2687BC14009E9DAF /* FirstThreeFourthsCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98FD7C5E2687BC14009E9DAF /* FirstThreeFourthsCalculation.swift */; };
		98FD7C612687BCB6009E9DAF /* LastThreeFourthsCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 98FD7C602687BCB6009E9DAF /* LastThreeFourthsCalculation.swift */; };
		AA040C67290B2640003181D5 /* RunLoopThread.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA040C66290B2640003181D5 /* RunLoopThread.swift */; };
		AA0AC000291C1B5E00D125D2 /* CGExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA0ABFFF291C1B5E00D125D2 /* CGExtension.swift */; };
		AA0AC002291C1B9100D125D2 /* AXExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA0AC001291C1B9100D125D2 /* AXExtension.swift */; };
		AA0AC004291C48DE00D125D2 /* SequenceExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA0AC003291C48DE00D125D2 /* SequenceExtension.swift */; };
		AA0ACC2F2864A86D0025E376 /* StageUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA0ACC2E2864A86D0025E376 /* StageUtil.swift */; };
		AA3A9E8B29230A82004EB8E5 /* CFExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA3A9E8A29230A82004EB8E5 /* CFExtension.swift */; };
		AA49DD1129B8C1B100690E13 /* TitleBarManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA49DD1029B8C1B100690E13 /* TitleBarManager.swift */; };
		AA4DA2FB28FDC94A00355CEB /* DispatchTimeExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA4DA2FA28FDC94A00355CEB /* DispatchTimeExtension.swift */; };
		AA536C2729005DD000579AC6 /* TimeoutCache.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA536C2629005DD000579AC6 /* TimeoutCache.swift */; };
		AA69F83C29909A95001A81AF /* RightTodoCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA69F83B29909A95001A81AF /* RightTodoCalculation.swift */; };
		AA69F8402992DCB1001A81AF /* LeftTodoCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = AA69F83F2992DCB1001A81AF /* LeftTodoCalculation.swift */; };
		AAADE1AF28CBAB0000036331 /* WindowUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = AAADE1AE28CBAB0000036331 /* WindowUtil.swift */; };
		B4521F932BD7CEFB00FD43CC /* ChangeWindowDimensionCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = B4521F922BD7CEFB00FD43CC /* ChangeWindowDimensionCalculation.swift */; };
		B4780A322BD4C75900732B9E /* HalfOrDoubleDimensionCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = B4780A312BD4C75900732B9E /* HalfOrDoubleDimensionCalculation.swift */; };
		D0423D8327A8D31D008A4894 /* HorizontalThirdsRepeated.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0423D8227A8D31D008A4894 /* HorizontalThirdsRepeated.swift */; };
		D04CE3002781794E00BD47B3 /* TopLeftNinthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = D04CE2FF2781794E00BD47B3 /* TopLeftNinthCalculation.swift */; };
		D04CE30227817A6100BD47B3 /* TopCenterNinthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = D04CE30127817A6100BD47B3 /* TopCenterNinthCalculation.swift */; };
		D04CE30427817A6F00BD47B3 /* TopRightNinthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = D04CE30327817A6F00BD47B3 /* TopRightNinthCalculation.swift */; };
		D04CE30627817A8400BD47B3 /* MiddleLeftNinthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = D04CE30527817A8400BD47B3 /* MiddleLeftNinthCalculation.swift */; };
		D04CE30827817A9200BD47B3 /* MiddleCenterNinthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = D04CE30727817A9200BD47B3 /* MiddleCenterNinthCalculation.swift */; };
		D04CE30A27817A9F00BD47B3 /* MiddleRightNinthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = D04CE30927817A9F00BD47B3 /* MiddleRightNinthCalculation.swift */; };
		D04CE30C27817AA900BD47B3 /* BottomLeftNinthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = D04CE30B27817AA900BD47B3 /* BottomLeftNinthCalculation.swift */; };
		D04CE30E27817AB500BD47B3 /* BottomCenterNinthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = D04CE30D27817AB500BD47B3 /* BottomCenterNinthCalculation.swift */; };
		D04CE31027817ABE00BD47B3 /* BottomRightNinthCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = D04CE30F27817ABE00BD47B3 /* BottomRightNinthCalculation.swift */; };
		D04CE31227817C9B00BD47B3 /* NinthsRepeated.swift in Sources */ = {isa = PBXBuildFile; fileRef = D04CE31127817C9B00BD47B3 /* NinthsRepeated.swift */; };
		D0CFE33127A8CAED004DA47B /* TopLeftThirdCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0CFE33027A8CAED004DA47B /* TopLeftThirdCalculation.swift */; };
		D0CFE33327A8CCB1004DA47B /* TopRightThirdCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0CFE33227A8CCB1004DA47B /* TopRightThirdCalculation.swift */; };
		D0CFE33527A8CD16004DA47B /* BottomLeftThirdCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0CFE33427A8CD16004DA47B /* BottomLeftThirdCalculation.swift */; };
		D0CFE33727A8CD51004DA47B /* BottomRightThirdCalculation.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0CFE33627A8CD51004DA47B /* BottomRightThirdCalculation.swift */; };
		FDE8FCE027C2950400EACCAA /* MultiWindowManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = FDE8FCDF27C2950400EACCAA /* MultiWindowManager.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		9824701C22AF9B7E0037B409 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9824700122AF9B7D0037B409 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9824700822AF9B7D0037B409;
			remoteInfo = Rectangle;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		9821405A22B3EC9900ABFB3F /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = Contents/Library/LoginItems;
			dstSubfolderSpec = 1;
			files = (
				9821405B22B3ECCA00ABFB3F /* RectangleLauncher.app in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		30166BCF24F27D6A00A38608 /* SpecifiedCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SpecifiedCalculation.swift; sourceTree = "<group>"; };
		423DC1992AE681F900C98564 /* mul */ = {isa = PBXFileReference; lastKnownFileType = text.json.xcstrings; name = mul; path = mul.lproj/Main.xcstrings; sourceTree = "<group>"; };
		42627A972ADA03D200D047C6 /* mul */ = {isa = PBXFileReference; lastKnownFileType = text.json.xcstrings; name = mul; path = mul.lproj/Main.xcstrings; sourceTree = "<group>"; };
		6490B39027BF907A0056C220 /* BottomLeftEighthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BottomLeftEighthCalculation.swift; sourceTree = "<group>"; };
		6490B39227BF90F90056C220 /* EighthsRepeated.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EighthsRepeated.swift; sourceTree = "<group>"; };
		6490B39427BF96880056C220 /* TopLeftEighthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopLeftEighthCalculation.swift; sourceTree = "<group>"; };
		6490B39627BF96EA0056C220 /* TopCenterLeftEighthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopCenterLeftEighthCalculation.swift; sourceTree = "<group>"; };
		6490B39827BF97BB0056C220 /* TopCenterRightEighthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopCenterRightEighthCalculation.swift; sourceTree = "<group>"; };
		6490B39A27BF980F0056C220 /* TopRightEighthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopRightEighthCalculation.swift; sourceTree = "<group>"; };
		6490B39C27BF984D0056C220 /* BottomCenterLeftEighthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BottomCenterLeftEighthCalculation.swift; sourceTree = "<group>"; };
		6490B39E27BF98840056C220 /* BottomCenterRightEighthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BottomCenterRightEighthCalculation.swift; sourceTree = "<group>"; };
		6490B3A027BF98C70056C220 /* BottomRightEighthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BottomRightEighthCalculation.swift; sourceTree = "<group>"; };
		729E0A972AFF76B1006E2F48 /* CenterProminentlyCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CenterProminentlyCalculation.swift; sourceTree = "<group>"; };
		7BE578EE2C5BF4ED0083DAE3 /* CycleSize.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CycleSize.swift; sourceTree = "<group>"; };
		866661F1257D248A00A9CD2D /* RepeatedExecutionsInThirdsCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepeatedExecutionsInThirdsCalculation.swift; sourceTree = "<group>"; };
		944F25CC2CE5A144004B2FD2 /* PrefsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PrefsViewController.swift; sourceTree = "<group>"; };
		94E9B08D2C3B8D97004C7F41 /* MacTilingDefaults.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MacTilingDefaults.swift; sourceTree = "<group>"; };
		94E9B08F2C3E4578004C7F41 /* StringExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StringExtension.swift; sourceTree = "<group>"; };
		9808018523D05C0B0077774A /* RectangleRelease.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RectangleRelease.entitlements; sourceTree = "<group>"; };
		9808018623D05C1F0077774A /* RectangleLauncherRelease.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RectangleLauncherRelease.entitlements; sourceTree = "<group>"; };
		9818E00C28B59205004AA524 /* CompoundSnapArea.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CompoundSnapArea.swift; sourceTree = "<group>"; };
		9818E00F28B59396004AA524 /* HalvesCompoundCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HalvesCompoundCalculation.swift; sourceTree = "<group>"; };
		9818E01128B59B64004AA524 /* ThirdsCompoundCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ThirdsCompoundCalculation.swift; sourceTree = "<group>"; };
		9818E01328B5A4FD004AA524 /* SixthsCompoundCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SixthsCompoundCalculation.swift; sourceTree = "<group>"; };
		9818E01728B63C48004AA524 /* FourthsCompoundCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FourthsCompoundCalculation.swift; sourceTree = "<group>"; };
		98192DD9270F606C00015E66 /* Debounce.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Debounce.swift; sourceTree = "<group>"; };
		98192DDD2717201000015E66 /* ReverseAllManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReverseAllManager.swift; sourceTree = "<group>"; };
		981F27D02340E3E1006CD263 /* InternetAccessPolicy.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = InternetAccessPolicy.plist; sourceTree = "<group>"; };
		9821402022B3884600ABFB3F /* BottomHalfCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BottomHalfCalculation.swift; sourceTree = "<group>"; };
		9821402222B3886100ABFB3F /* CenterCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CenterCalculation.swift; sourceTree = "<group>"; };
		9821402422B3887200ABFB3F /* MaximizeCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MaximizeCalculation.swift; sourceTree = "<group>"; };
		9821402622B3888100ABFB3F /* ChangeSizeCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChangeSizeCalculation.swift; sourceTree = "<group>"; };
		9821402822B3889100ABFB3F /* LowerLeftCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LowerLeftCalculation.swift; sourceTree = "<group>"; };
		9821402A22B388A000ABFB3F /* LowerRightCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LowerRightCalculation.swift; sourceTree = "<group>"; };
		9821403022B38A0500ABFB3F /* TopHalfCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopHalfCalculation.swift; sourceTree = "<group>"; };
		9821403222B38A1B00ABFB3F /* UpperLeftCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpperLeftCalculation.swift; sourceTree = "<group>"; };
		9821403422B38A2B00ABFB3F /* UpperRightCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpperRightCalculation.swift; sourceTree = "<group>"; };
		9821403622B3D16700ABFB3F /* MaximizeHeightCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MaximizeHeightCalculation.swift; sourceTree = "<group>"; };
		9821403C22B3EBD900ABFB3F /* RectangleLauncher.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = RectangleLauncher.app; sourceTree = BUILT_PRODUCTS_DIR; };
		9821403E22B3EBD900ABFB3F /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		9821404222B3EBDA00ABFB3F /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		9821404522B3EBDA00ABFB3F /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		9821404722B3EBDA00ABFB3F /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		9821404822B3EBDA00ABFB3F /* RectangleLauncher.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = RectangleLauncher.entitlements; sourceTree = "<group>"; };
		9821405D22B3ED3600ABFB3F /* ServiceManagement.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ServiceManagement.framework; path = System/Library/Frameworks/ServiceManagement.framework; sourceTree = SDKROOT; };
		9821405F22B3EFB200ABFB3F /* Defaults.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Defaults.swift; sourceTree = "<group>"; };
		9821552627BFFB13002523EC /* Rectangle-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Rectangle-Bridging-Header.h"; sourceTree = "<group>"; };
		9824700922AF9B7D0037B409 /* Rectangle.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Rectangle.app; sourceTree = BUILT_PRODUCTS_DIR; };
		9824700C22AF9B7D0037B409 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		9824701022AF9B7E0037B409 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		9824701322AF9B7E0037B409 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		9824701522AF9B7E0037B409 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		9824701622AF9B7E0037B409 /* Rectangle.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Rectangle.entitlements; sourceTree = "<group>"; };
		9824701B22AF9B7E0037B409 /* RectangleTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RectangleTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		9824701F22AF9B7E0037B409 /* RectangleTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RectangleTests.swift; sourceTree = "<group>"; };
		9824702122AF9B7E0037B409 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		9824702A22AFA22E0037B409 /* AccessibilityWindowController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AccessibilityWindowController.swift; sourceTree = "<group>"; };
		9824702E22AFA2E50037B409 /* AccessibilityAuthorization.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AccessibilityAuthorization.swift; sourceTree = "<group>"; };
		9824703022AFA8470037B409 /* RectangleStatusItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RectangleStatusItem.swift; sourceTree = "<group>"; };
		9824703622B0F3200037B409 /* WindowAction.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WindowAction.swift; sourceTree = "<group>"; };
		9824703822B0F37C0037B409 /* ShortcutManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShortcutManager.swift; sourceTree = "<group>"; };
		9824703A22B139780037B409 /* CUtil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CUtil.swift; sourceTree = "<group>"; };
		9824703C22B13C7E0037B409 /* AccessibilityElement.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AccessibilityElement.swift; sourceTree = "<group>"; };
		9824703E22B13FBC0037B409 /* ScreenDetection.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ScreenDetection.swift; sourceTree = "<group>"; };
		9824704022B186D00037B409 /* WindowManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WindowManager.swift; sourceTree = "<group>"; };
		9824704622B189240037B409 /* StandardWindowMover.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = StandardWindowMover.swift; sourceTree = "<group>"; };
		9824704722B189240037B409 /* WindowMover.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WindowMover.swift; sourceTree = "<group>"; };
		9824704822B189250037B409 /* WindowCalculation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = WindowCalculation.swift; sourceTree = "<group>"; };
		9824704922B189250037B409 /* QuantizedWindowMover.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = QuantizedWindowMover.swift; sourceTree = "<group>"; };
		9824704A22B189250037B409 /* BestEffortWindowMover.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BestEffortWindowMover.swift; sourceTree = "<group>"; };
		9824705022B28D7A0037B409 /* LeftRightHalfCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LeftRightHalfCalculation.swift; sourceTree = "<group>"; };
		983BBD6E253B609D000D223E /* FootprintWindow.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FootprintWindow.swift; sourceTree = "<group>"; };
		983DD03F28A844BE00BF1EEE /* SnapAreaViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SnapAreaViewController.swift; sourceTree = "<group>"; };
		983DD04328B0639E00BF1EEE /* SnapAreaModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SnapAreaModel.swift; sourceTree = "<group>"; };
		984EDB0E29A42ED200D119D2 /* LaunchOnLogin.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LaunchOnLogin.swift; sourceTree = "<group>"; };
		9851A5C2251BEBA300ECF78C /* OrientationAware.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OrientationAware.swift; sourceTree = "<group>"; };
		985B9BF422B93EEC00A2E8F0 /* ApplicationToggle.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ApplicationToggle.swift; sourceTree = "<group>"; };
		985B9BF622BB6F5000A2E8F0 /* MessageView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = MessageView.xib; sourceTree = "<group>"; };
		985B9BF722BB6F5000A2E8F0 /* MessageView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MessageView.swift; sourceTree = "<group>"; };
		988D066022EB4C7C004EABD7 /* FirstThirdCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FirstThirdCalculation.swift; sourceTree = "<group>"; };
		988D066222EB4CA5004EABD7 /* FirstTwoThirdsCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FirstTwoThirdsCalculation.swift; sourceTree = "<group>"; };
		988D066422EB4CB5004EABD7 /* CenterThirdCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CenterThirdCalculation.swift; sourceTree = "<group>"; };
		988D066622EB4CC0004EABD7 /* LastTwoThirdsCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LastTwoThirdsCalculation.swift; sourceTree = "<group>"; };
		988D066822EB4CCB004EABD7 /* LastThirdCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LastThirdCalculation.swift; sourceTree = "<group>"; };
		988D067C22EB4E17004EABD7 /* AlmostMaximizeCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AlmostMaximizeCalculation.swift; sourceTree = "<group>"; };
		988D067E22EB4EDE004EABD7 /* MoveLeftRightCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MoveLeftRightCalculation.swift; sourceTree = "<group>"; };
		988D068222EB4EF3004EABD7 /* MoveUpDownCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MoveUpDownCalculation.swift; sourceTree = "<group>"; };
		98910B3D231130AF0066EC23 /* SettingsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsViewController.swift; sourceTree = "<group>"; };
		98987AA22391890300BE72C4 /* LogViewer.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = LogViewer.storyboard; sourceTree = "<group>"; };
		98987AA32391890400BE72C4 /* LogViewer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LogViewer.swift; sourceTree = "<group>"; };
		989DA30D243FC0DF008C7AA4 /* WelcomeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WelcomeViewController.swift; sourceTree = "<group>"; };
		98A009AA2512491300CFBF0C /* CenterHalfCalculation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CenterHalfCalculation.swift; sourceTree = "<group>"; };
		98A009AC2512498000CFBF0C /* FirstFourthCalculation.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FirstFourthCalculation.swift; sourceTree = "<group>"; };
		98A009AE2512517900CFBF0C /* SecondFourthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SecondFourthCalculation.swift; sourceTree = "<group>"; };
		98A009B0251252C900CFBF0C /* ThirdFourthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ThirdFourthCalculation.swift; sourceTree = "<group>"; };
		98A009B22512536800CFBF0C /* LastFourthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LastFourthCalculation.swift; sourceTree = "<group>"; };
		98A009B42512537800CFBF0C /* TopLeftSixthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopLeftSixthCalculation.swift; sourceTree = "<group>"; };
		98A009B62512538200CFBF0C /* TopCenterSixthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopCenterSixthCalculation.swift; sourceTree = "<group>"; };
		98A009B82512538D00CFBF0C /* TopRightSixthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopRightSixthCalculation.swift; sourceTree = "<group>"; };
		98A009BA2512539900CFBF0C /* BottomLeftSixthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BottomLeftSixthCalculation.swift; sourceTree = "<group>"; };
		98A009BC251253A000CFBF0C /* BottomCenterSixthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BottomCenterSixthCalculation.swift; sourceTree = "<group>"; };
		98A009BE251253AB00CFBF0C /* BottomRightSixthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BottomRightSixthCalculation.swift; sourceTree = "<group>"; };
		98A6EDDC251F3F4A00F74B10 /* SixthsRepeated.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SixthsRepeated.swift; sourceTree = "<group>"; };
		98A6EDEB2528FFC100F74B10 /* WindowActionCategory.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WindowActionCategory.swift; sourceTree = "<group>"; };
		98B3559723CE025700E410E0 /* CenteringFixedSizedWindowMover.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CenteringFixedSizedWindowMover.swift; sourceTree = "<group>"; };
		98BEFA472620DEDC00D9D54F /* NSImageExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NSImageExtension.swift; sourceTree = "<group>"; };
		98C1008B2305F1FA006E5344 /* SubsequentExecutionMode.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SubsequentExecutionMode.swift; sourceTree = "<group>"; };
		98C1008D230B9EF6006E5344 /* NextPrevDisplayCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NextPrevDisplayCalculation.swift; sourceTree = "<group>"; };
		98C2755D231FF6A9009B9292 /* EventMonitor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EventMonitor.swift; sourceTree = "<group>"; };
		98C27560231FFA5F009B9292 /* SnappingManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SnappingManager.swift; sourceTree = "<group>"; };
		98C275662322E2DA009B9292 /* WindowHistory.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WindowHistory.swift; sourceTree = "<group>"; };
		98C6DEEF23CE191700CC0C1E /* GapCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GapCalculation.swift; sourceTree = "<group>"; };
		98C97FFC25893B040061F01F /* Config.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Config.swift; sourceTree = "<group>"; };
		98D1441224560B1E0090C603 /* AlertUtil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AlertUtil.swift; sourceTree = "<group>"; };
		98D16A432592AD55005228CB /* MASShortcutMigration.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MASShortcutMigration.swift; sourceTree = "<group>"; };
		98D16A482592B460005228CB /* NotificationExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationExtension.swift; sourceTree = "<group>"; };
		98D4B6C425B6256C009C7BF6 /* TodoManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TodoManager.swift; sourceTree = "<group>"; };
		98FA9496235A2D7600F95C4F /* RepeatedExecutionsCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RepeatedExecutionsCalculation.swift; sourceTree = "<group>"; };
		98FD7C5E2687BC14009E9DAF /* FirstThreeFourthsCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FirstThreeFourthsCalculation.swift; sourceTree = "<group>"; };
		98FD7C602687BCB6009E9DAF /* LastThreeFourthsCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LastThreeFourthsCalculation.swift; sourceTree = "<group>"; };
		AA040C66290B2640003181D5 /* RunLoopThread.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RunLoopThread.swift; sourceTree = "<group>"; };
		AA0ABFFF291C1B5E00D125D2 /* CGExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CGExtension.swift; sourceTree = "<group>"; };
		AA0AC001291C1B9100D125D2 /* AXExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AXExtension.swift; sourceTree = "<group>"; };
		AA0AC003291C48DE00D125D2 /* SequenceExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SequenceExtension.swift; sourceTree = "<group>"; };
		AA0ACC2E2864A86D0025E376 /* StageUtil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StageUtil.swift; sourceTree = "<group>"; };
		AA3A9E8A29230A82004EB8E5 /* CFExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CFExtension.swift; sourceTree = "<group>"; };
		AA49DD1029B8C1B100690E13 /* TitleBarManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TitleBarManager.swift; sourceTree = "<group>"; };
		AA4DA2FA28FDC94A00355CEB /* DispatchTimeExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DispatchTimeExtension.swift; sourceTree = "<group>"; };
		AA536C2629005DD000579AC6 /* TimeoutCache.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TimeoutCache.swift; sourceTree = "<group>"; };
		AA69F83B29909A95001A81AF /* RightTodoCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RightTodoCalculation.swift; sourceTree = "<group>"; };
		AA69F83F2992DCB1001A81AF /* LeftTodoCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LeftTodoCalculation.swift; sourceTree = "<group>"; };
		AAADE1AE28CBAB0000036331 /* WindowUtil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WindowUtil.swift; sourceTree = "<group>"; };
		B4521F922BD7CEFB00FD43CC /* ChangeWindowDimensionCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChangeWindowDimensionCalculation.swift; sourceTree = "<group>"; };
		B4780A312BD4C75900732B9E /* HalfOrDoubleDimensionCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HalfOrDoubleDimensionCalculation.swift; sourceTree = "<group>"; };
		D0423D8227A8D31D008A4894 /* HorizontalThirdsRepeated.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HorizontalThirdsRepeated.swift; sourceTree = "<group>"; };
		D04CE2FF2781794E00BD47B3 /* TopLeftNinthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopLeftNinthCalculation.swift; sourceTree = "<group>"; };
		D04CE30127817A6100BD47B3 /* TopCenterNinthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopCenterNinthCalculation.swift; sourceTree = "<group>"; };
		D04CE30327817A6F00BD47B3 /* TopRightNinthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopRightNinthCalculation.swift; sourceTree = "<group>"; };
		D04CE30527817A8400BD47B3 /* MiddleLeftNinthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MiddleLeftNinthCalculation.swift; sourceTree = "<group>"; };
		D04CE30727817A9200BD47B3 /* MiddleCenterNinthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MiddleCenterNinthCalculation.swift; sourceTree = "<group>"; };
		D04CE30927817A9F00BD47B3 /* MiddleRightNinthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MiddleRightNinthCalculation.swift; sourceTree = "<group>"; };
		D04CE30B27817AA900BD47B3 /* BottomLeftNinthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BottomLeftNinthCalculation.swift; sourceTree = "<group>"; };
		D04CE30D27817AB500BD47B3 /* BottomCenterNinthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BottomCenterNinthCalculation.swift; sourceTree = "<group>"; };
		D04CE30F27817ABE00BD47B3 /* BottomRightNinthCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BottomRightNinthCalculation.swift; sourceTree = "<group>"; };
		D04CE31127817C9B00BD47B3 /* NinthsRepeated.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NinthsRepeated.swift; sourceTree = "<group>"; };
		D0CFE33027A8CAED004DA47B /* TopLeftThirdCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopLeftThirdCalculation.swift; sourceTree = "<group>"; };
		D0CFE33227A8CCB1004DA47B /* TopRightThirdCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopRightThirdCalculation.swift; sourceTree = "<group>"; };
		D0CFE33427A8CD16004DA47B /* BottomLeftThirdCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BottomLeftThirdCalculation.swift; sourceTree = "<group>"; };
		D0CFE33627A8CD51004DA47B /* BottomRightThirdCalculation.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BottomRightThirdCalculation.swift; sourceTree = "<group>"; };
		FDE8FCDF27C2950400EACCAA /* MultiWindowManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MultiWindowManager.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		9821403922B3EBD900ABFB3F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9824700622AF9B7D0037B409 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9877B63A29C8AC0E00F02D74 /* MASShortcut in Frameworks */,
				9877B63D29C8AC3600F02D74 /* Sparkle in Frameworks */,
				9821405E22B3ED3600ABFB3F /* ServiceManagement.framework in Frameworks */,
				12D896200936C0DE9FF41FB4 /* (null) in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9824701822AF9B7E0037B409 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		9818E00E28B5922A004AA524 /* CompoundSnapArea */ = {
			isa = PBXGroup;
			children = (
				9818E00C28B59205004AA524 /* CompoundSnapArea.swift */,
				9818E00F28B59396004AA524 /* HalvesCompoundCalculation.swift */,
				9818E01128B59B64004AA524 /* ThirdsCompoundCalculation.swift */,
				9818E01728B63C48004AA524 /* FourthsCompoundCalculation.swift */,
				9818E01328B5A4FD004AA524 /* SixthsCompoundCalculation.swift */,
			);
			path = CompoundSnapArea;
			sourceTree = "<group>";
		};
		98192DDF2717201E00015E66 /* MultiWindow */ = {
			isa = PBXGroup;
			children = (
				98192DDD2717201000015E66 /* ReverseAllManager.swift */,
				FDE8FCDF27C2950400EACCAA /* MultiWindowManager.swift */,
			);
			path = MultiWindow;
			sourceTree = "<group>";
		};
		9821403D22B3EBD900ABFB3F /* RectangleLauncher */ = {
			isa = PBXGroup;
			children = (
				9808018623D05C1F0077774A /* RectangleLauncherRelease.entitlements */,
				9821403E22B3EBD900ABFB3F /* AppDelegate.swift */,
				9821404222B3EBDA00ABFB3F /* Assets.xcassets */,
				9821404422B3EBDA00ABFB3F /* Main.storyboard */,
				9821404722B3EBDA00ABFB3F /* Info.plist */,
				9821404822B3EBDA00ABFB3F /* RectangleLauncher.entitlements */,
			);
			path = RectangleLauncher;
			sourceTree = "<group>";
		};
		9821405C22B3ED3600ABFB3F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				9821405D22B3ED3600ABFB3F /* ServiceManagement.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		982140E922B7DA3100ABFB3F /* WindowCalculation */ = {
			isa = PBXGroup;
			children = (
				98A009AA2512491300CFBF0C /* CenterHalfCalculation.swift */,
				98A009AC2512498000CFBF0C /* FirstFourthCalculation.swift */,
				98FD7C5E2687BC14009E9DAF /* FirstThreeFourthsCalculation.swift */,
				98FD7C602687BCB6009E9DAF /* LastThreeFourthsCalculation.swift */,
				98A009AE2512517900CFBF0C /* SecondFourthCalculation.swift */,
				98A009B0251252C900CFBF0C /* ThirdFourthCalculation.swift */,
				98A009B22512536800CFBF0C /* LastFourthCalculation.swift */,
				98A009B42512537800CFBF0C /* TopLeftSixthCalculation.swift */,
				98A009B62512538200CFBF0C /* TopCenterSixthCalculation.swift */,
				98A009B82512538D00CFBF0C /* TopRightSixthCalculation.swift */,
				98A009BA2512539900CFBF0C /* BottomLeftSixthCalculation.swift */,
				98A009BC251253A000CFBF0C /* BottomCenterSixthCalculation.swift */,
				98A009BE251253AB00CFBF0C /* BottomRightSixthCalculation.swift */,
				9824704822B189250037B409 /* WindowCalculation.swift */,
				9824705022B28D7A0037B409 /* LeftRightHalfCalculation.swift */,
				9821402022B3884600ABFB3F /* BottomHalfCalculation.swift */,
				9821403022B38A0500ABFB3F /* TopHalfCalculation.swift */,
				9821402222B3886100ABFB3F /* CenterCalculation.swift */,
				729E0A972AFF76B1006E2F48 /* CenterProminentlyCalculation.swift */,
				9821402422B3887200ABFB3F /* MaximizeCalculation.swift */,
				9821402622B3888100ABFB3F /* ChangeSizeCalculation.swift */,
				B4780A312BD4C75900732B9E /* HalfOrDoubleDimensionCalculation.swift */,
				9821402822B3889100ABFB3F /* LowerLeftCalculation.swift */,
				9821402A22B388A000ABFB3F /* LowerRightCalculation.swift */,
				9821403222B38A1B00ABFB3F /* UpperLeftCalculation.swift */,
				9821403422B38A2B00ABFB3F /* UpperRightCalculation.swift */,
				9821403622B3D16700ABFB3F /* MaximizeHeightCalculation.swift */,
				988D066022EB4C7C004EABD7 /* FirstThirdCalculation.swift */,
				988D066222EB4CA5004EABD7 /* FirstTwoThirdsCalculation.swift */,
				988D066422EB4CB5004EABD7 /* CenterThirdCalculation.swift */,
				988D066622EB4CC0004EABD7 /* LastTwoThirdsCalculation.swift */,
				988D066822EB4CCB004EABD7 /* LastThirdCalculation.swift */,
				988D067E22EB4EDE004EABD7 /* MoveLeftRightCalculation.swift */,
				988D068222EB4EF3004EABD7 /* MoveUpDownCalculation.swift */,
				988D067C22EB4E17004EABD7 /* AlmostMaximizeCalculation.swift */,
				98C1008D230B9EF6006E5344 /* NextPrevDisplayCalculation.swift */,
				98FA9496235A2D7600F95C4F /* RepeatedExecutionsCalculation.swift */,
				98C6DEEF23CE191700CC0C1E /* GapCalculation.swift */,
				9851A5C2251BEBA300ECF78C /* OrientationAware.swift */,
				98A6EDDC251F3F4A00F74B10 /* SixthsRepeated.swift */,
				866661F1257D248A00A9CD2D /* RepeatedExecutionsInThirdsCalculation.swift */,
				30166BCF24F27D6A00A38608 /* SpecifiedCalculation.swift */,
				D04CE31127817C9B00BD47B3 /* NinthsRepeated.swift */,
				D04CE2FF2781794E00BD47B3 /* TopLeftNinthCalculation.swift */,
				D04CE30127817A6100BD47B3 /* TopCenterNinthCalculation.swift */,
				D04CE30327817A6F00BD47B3 /* TopRightNinthCalculation.swift */,
				D04CE30527817A8400BD47B3 /* MiddleLeftNinthCalculation.swift */,
				D04CE30727817A9200BD47B3 /* MiddleCenterNinthCalculation.swift */,
				D04CE30927817A9F00BD47B3 /* MiddleRightNinthCalculation.swift */,
				D04CE30B27817AA900BD47B3 /* BottomLeftNinthCalculation.swift */,
				D04CE30D27817AB500BD47B3 /* BottomCenterNinthCalculation.swift */,
				D04CE30F27817ABE00BD47B3 /* BottomRightNinthCalculation.swift */,
				D0CFE33027A8CAED004DA47B /* TopLeftThirdCalculation.swift */,
				D0CFE33227A8CCB1004DA47B /* TopRightThirdCalculation.swift */,
				D0CFE33427A8CD16004DA47B /* BottomLeftThirdCalculation.swift */,
				D0CFE33627A8CD51004DA47B /* BottomRightThirdCalculation.swift */,
				D0423D8227A8D31D008A4894 /* HorizontalThirdsRepeated.swift */,
				6490B39227BF90F90056C220 /* EighthsRepeated.swift */,
				6490B39427BF96880056C220 /* TopLeftEighthCalculation.swift */,
				6490B39627BF96EA0056C220 /* TopCenterLeftEighthCalculation.swift */,
				6490B39827BF97BB0056C220 /* TopCenterRightEighthCalculation.swift */,
				6490B39A27BF980F0056C220 /* TopRightEighthCalculation.swift */,
				6490B39027BF907A0056C220 /* BottomLeftEighthCalculation.swift */,
				6490B39C27BF984D0056C220 /* BottomCenterLeftEighthCalculation.swift */,
				6490B39E27BF98840056C220 /* BottomCenterRightEighthCalculation.swift */,
				6490B3A027BF98C70056C220 /* BottomRightEighthCalculation.swift */,
				AA69F83B29909A95001A81AF /* RightTodoCalculation.swift */,
				AA69F83F2992DCB1001A81AF /* LeftTodoCalculation.swift */,
				B4521F922BD7CEFB00FD43CC /* ChangeWindowDimensionCalculation.swift */,
			);
			path = WindowCalculation;
			sourceTree = "<group>";
		};
		982140EA22B7DB0400ABFB3F /* WindowMover */ = {
			isa = PBXGroup;
			children = (
				9824704A22B189250037B409 /* BestEffortWindowMover.swift */,
				9824704922B189250037B409 /* QuantizedWindowMover.swift */,
				9824704622B189240037B409 /* StandardWindowMover.swift */,
				9824704722B189240037B409 /* WindowMover.swift */,
				98B3559723CE025700E410E0 /* CenteringFixedSizedWindowMover.swift */,
			);
			path = WindowMover;
			sourceTree = "<group>";
		};
		982140EB22B7DB1A00ABFB3F /* AccessibilityAuthorization */ = {
			isa = PBXGroup;
			children = (
				9824702E22AFA2E50037B409 /* AccessibilityAuthorization.swift */,
				9824702A22AFA22E0037B409 /* AccessibilityWindowController.swift */,
			);
			path = AccessibilityAuthorization;
			sourceTree = "<group>";
		};
		982140EC22B7DB5700ABFB3F /* Utilities */ = {
			isa = PBXGroup;
			children = (
				9824703A22B139780037B409 /* CUtil.swift */,
				98D1441224560B1E0090C603 /* AlertUtil.swift */,
				98D16A432592AD55005228CB /* MASShortcutMigration.swift */,
				98D16A482592B460005228CB /* NotificationExtension.swift */,
				98BEFA472620DEDC00D9D54F /* NSImageExtension.swift */,
				98192DD9270F606C00015E66 /* Debounce.swift */,
				AA0ACC2E2864A86D0025E376 /* StageUtil.swift */,
				94E9B08D2C3B8D97004C7F41 /* MacTilingDefaults.swift */,
				94E9B08F2C3E4578004C7F41 /* StringExtension.swift */,
				AAADE1AE28CBAB0000036331 /* WindowUtil.swift */,
				AA4DA2FA28FDC94A00355CEB /* DispatchTimeExtension.swift */,
				AA536C2629005DD000579AC6 /* TimeoutCache.swift */,
				AA040C66290B2640003181D5 /* RunLoopThread.swift */,
				AA0AC001291C1B9100D125D2 /* AXExtension.swift */,
				AA0ABFFF291C1B5E00D125D2 /* CGExtension.swift */,
				AA0AC003291C48DE00D125D2 /* SequenceExtension.swift */,
				AA3A9E8A29230A82004EB8E5 /* CFExtension.swift */,
				98C2755D231FF6A9009B9292 /* EventMonitor.swift */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
		9824700022AF9B7D0037B409 = {
			isa = PBXGroup;
			children = (
				9824700B22AF9B7D0037B409 /* Rectangle */,
				9824701E22AF9B7E0037B409 /* RectangleTests */,
				9821403D22B3EBD900ABFB3F /* RectangleLauncher */,
				9824700A22AF9B7D0037B409 /* Products */,
				9821405C22B3ED3600ABFB3F /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		9824700A22AF9B7D0037B409 /* Products */ = {
			isa = PBXGroup;
			children = (
				9824700922AF9B7D0037B409 /* Rectangle.app */,
				9824701B22AF9B7E0037B409 /* RectangleTests.xctest */,
				9821403C22B3EBD900ABFB3F /* RectangleLauncher.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		9824700B22AF9B7D0037B409 /* Rectangle */ = {
			isa = PBXGroup;
			children = (
				9824700C22AF9B7D0037B409 /* AppDelegate.swift */,
				9824701022AF9B7E0037B409 /* Assets.xcassets */,
				9824701222AF9B7E0037B409 /* Main.storyboard */,
				9824701522AF9B7E0037B409 /* Info.plist */,
				981F27D02340E3E1006CD263 /* InternetAccessPolicy.plist */,
				9821552627BFFB13002523EC /* Rectangle-Bridging-Header.h */,
				9808018523D05C0B0077774A /* RectangleRelease.entitlements */,
				9824701622AF9B7E0037B409 /* Rectangle.entitlements */,
				9821405F22B3EFB200ABFB3F /* Defaults.swift */,
				984EDB0E29A42ED200D119D2 /* LaunchOnLogin.swift */,
				98C1008B2305F1FA006E5344 /* SubsequentExecutionMode.swift */,
				7BE578EE2C5BF4ED0083DAE3 /* CycleSize.swift */,
				985B9BF422B93EEC00A2E8F0 /* ApplicationToggle.swift */,
				9824703022AFA8470037B409 /* RectangleStatusItem.swift */,
				9824703622B0F3200037B409 /* WindowAction.swift */,
				98A6EDEB2528FFC100F74B10 /* WindowActionCategory.swift */,
				9824703822B0F37C0037B409 /* ShortcutManager.swift */,
				9824703C22B13C7E0037B409 /* AccessibilityElement.swift */,
				9824703E22B13FBC0037B409 /* ScreenDetection.swift */,
				98C275662322E2DA009B9292 /* WindowHistory.swift */,
				9824704022B186D00037B409 /* WindowManager.swift */,
				AA49DD1029B8C1B100690E13 /* TitleBarManager.swift */,
				98987AA62391890C00BE72C4 /* Logging */,
				98910B3C2311304D0066EC23 /* PrefsWindow */,
				989DA30C243FC0C3008C7AA4 /* WelcomeWindow */,
				98C2755F231FF6AE009B9292 /* Snapping */,
				98D4B6C925B625B6009C7BF6 /* TodoMode */,
				985B9BFA22BE218C00A2E8F0 /* Popover */,
				982140EA22B7DB0400ABFB3F /* WindowMover */,
				982140E922B7DA3100ABFB3F /* WindowCalculation */,
				98192DDF2717201E00015E66 /* MultiWindow */,
				982140EB22B7DB1A00ABFB3F /* AccessibilityAuthorization */,
				982140EC22B7DB5700ABFB3F /* Utilities */,
			);
			path = Rectangle;
			sourceTree = "<group>";
		};
		9824701E22AF9B7E0037B409 /* RectangleTests */ = {
			isa = PBXGroup;
			children = (
				9824701F22AF9B7E0037B409 /* RectangleTests.swift */,
				9824702122AF9B7E0037B409 /* Info.plist */,
			);
			path = RectangleTests;
			sourceTree = "<group>";
		};
		985B9BFA22BE218C00A2E8F0 /* Popover */ = {
			isa = PBXGroup;
			children = (
				985B9BF722BB6F5000A2E8F0 /* MessageView.swift */,
				985B9BF622BB6F5000A2E8F0 /* MessageView.xib */,
			);
			path = Popover;
			sourceTree = "<group>";
		};
		98910B3C2311304D0066EC23 /* PrefsWindow */ = {
			isa = PBXGroup;
			children = (
				944F25CC2CE5A144004B2FD2 /* PrefsViewController.swift */,
				98910B3D231130AF0066EC23 /* SettingsViewController.swift */,
				983DD03F28A844BE00BF1EEE /* SnapAreaViewController.swift */,
				98C97FFC25893B040061F01F /* Config.swift */,
			);
			path = PrefsWindow;
			sourceTree = "<group>";
		};
		98987AA62391890C00BE72C4 /* Logging */ = {
			isa = PBXGroup;
			children = (
				98987AA22391890300BE72C4 /* LogViewer.storyboard */,
				98987AA32391890400BE72C4 /* LogViewer.swift */,
			);
			path = Logging;
			sourceTree = "<group>";
		};
		989DA30C243FC0C3008C7AA4 /* WelcomeWindow */ = {
			isa = PBXGroup;
			children = (
				989DA30D243FC0DF008C7AA4 /* WelcomeViewController.swift */,
			);
			path = WelcomeWindow;
			sourceTree = "<group>";
		};
		98C2755F231FF6AE009B9292 /* Snapping */ = {
			isa = PBXGroup;
			children = (
				98C27560231FFA5F009B9292 /* SnappingManager.swift */,
				983BBD6E253B609D000D223E /* FootprintWindow.swift */,
				983DD04328B0639E00BF1EEE /* SnapAreaModel.swift */,
				9818E00E28B5922A004AA524 /* CompoundSnapArea */,
			);
			path = Snapping;
			sourceTree = "<group>";
		};
		98D4B6C925B625B6009C7BF6 /* TodoMode */ = {
			isa = PBXGroup;
			children = (
				98D4B6C425B6256C009C7BF6 /* TodoManager.swift */,
			);
			path = TodoMode;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		9821403B22B3EBD900ABFB3F /* RectangleLauncher */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9821405422B3EBDA00ABFB3F /* Build configuration list for PBXNativeTarget "RectangleLauncher" */;
			buildPhases = (
				9821403822B3EBD900ABFB3F /* Sources */,
				9821403922B3EBD900ABFB3F /* Frameworks */,
				9821403A22B3EBD900ABFB3F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RectangleLauncher;
			productName = RectangleLauncher;
			productReference = 9821403C22B3EBD900ABFB3F /* RectangleLauncher.app */;
			productType = "com.apple.product-type.application";
		};
		9824700822AF9B7D0037B409 /* Rectangle */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9824702422AF9B7E0037B409 /* Build configuration list for PBXNativeTarget "Rectangle" */;
			buildPhases = (
				9824700522AF9B7D0037B409 /* Sources */,
				9824700622AF9B7D0037B409 /* Frameworks */,
				9824700722AF9B7D0037B409 /* Resources */,
				9821405A22B3EC9900ABFB3F /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Rectangle;
			packageProductDependencies = (
				9877B63929C8AC0E00F02D74 /* MASShortcut */,
				9877B63C29C8AC3600F02D74 /* Sparkle */,
			);
			productName = Rectangle;
			productReference = 9824700922AF9B7D0037B409 /* Rectangle.app */;
			productType = "com.apple.product-type.application";
		};
		9824701A22AF9B7E0037B409 /* RectangleTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9824702722AF9B7E0037B409 /* Build configuration list for PBXNativeTarget "RectangleTests" */;
			buildPhases = (
				9824701722AF9B7E0037B409 /* Sources */,
				9824701822AF9B7E0037B409 /* Frameworks */,
				9824701922AF9B7E0037B409 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9824701D22AF9B7E0037B409 /* PBXTargetDependency */,
			);
			name = RectangleTests;
			productName = RectangleTests;
			productReference = 9824701B22AF9B7E0037B409 /* RectangleTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		9824700122AF9B7D0037B409 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastSwiftUpdateCheck = 1020;
				LastUpgradeCheck = 1500;
				ORGANIZATIONNAME = "Ryan Hanson";
				TargetAttributes = {
					9821403B22B3EBD900ABFB3F = {
						CreatedOnToolsVersion = 10.2.1;
						SystemCapabilities = {
							com.apple.HardenedRuntime = {
								enabled = 0;
							};
							com.apple.Sandbox = {
								enabled = 1;
							};
						};
					};
					9824700822AF9B7D0037B409 = {
						CreatedOnToolsVersion = 10.2.1;
						LastSwiftMigration = 1320;
						SystemCapabilities = {
							com.apple.HardenedRuntime = {
								enabled = 0;
							};
							com.apple.Sandbox = {
								enabled = 0;
							};
						};
					};
					9824701A22AF9B7E0037B409 = {
						CreatedOnToolsVersion = 10.2.1;
						TestTargetID = 9824700822AF9B7D0037B409;
					};
				};
			};
			buildConfigurationList = 9824700422AF9B7D0037B409 /* Build configuration list for PBXProject "Rectangle" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				es,
				fr,
				de,
				ru,
				"pt-BR",
				"pt-PT",
				it,
				pl,
				nl,
				"es-419",
				ja,
				"zh-Hans",
				"zh-Hant",
				ko,
				"zh-Hant-HK",
				"sv-SE",
				ro,
				id,
				cs,
				sk,
				ca,
				tr,
				"ca-ES",
				uk,
				vi,
				lt,
				ar,
				nb,
				nn,
				"en-GB",
			);
			mainGroup = 9824700022AF9B7D0037B409;
			packageReferences = (
				9877B63829C8AC0E00F02D74 /* XCRemoteSwiftPackageReference "MASShortcut" */,
				9877B63B29C8AC3600F02D74 /* XCRemoteSwiftPackageReference "Sparkle" */,
			);
			productRefGroup = 9824700A22AF9B7D0037B409 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				9824700822AF9B7D0037B409 /* Rectangle */,
				9824701A22AF9B7E0037B409 /* RectangleTests */,
				9821403B22B3EBD900ABFB3F /* RectangleLauncher */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		9821403A22B3EBD900ABFB3F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9821404322B3EBDA00ABFB3F /* Assets.xcassets in Resources */,
				9821404622B3EBDA00ABFB3F /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9824700722AF9B7D0037B409 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				985B9BF822BB6F5100A2E8F0 /* MessageView.xib in Resources */,
				9824701122AF9B7E0037B409 /* Assets.xcassets in Resources */,
				9824701422AF9B7E0037B409 /* Main.storyboard in Resources */,
				981F27D12340E3E1006CD263 /* InternetAccessPolicy.plist in Resources */,
				98987AA42391890400BE72C4 /* LogViewer.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9824701922AF9B7E0037B409 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		9821403822B3EBD900ABFB3F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9821403F22B3EBD900ABFB3F /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9824700522AF9B7D0037B409 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				98A009B32512536900CFBF0C /* LastFourthCalculation.swift in Sources */,
				98A009BB2512539900CFBF0C /* BottomLeftSixthCalculation.swift in Sources */,
				9821402322B3886100ABFB3F /* CenterCalculation.swift in Sources */,
				98A009AB2512491300CFBF0C /* CenterHalfCalculation.swift in Sources */,
				9818E01828B63C48004AA524 /* FourthsCompoundCalculation.swift in Sources */,
				D0CFE33327A8CCB1004DA47B /* TopRightThirdCalculation.swift in Sources */,
				94E9B08E2C3B8D97004C7F41 /* MacTilingDefaults.swift in Sources */,
				9821403522B38A2B00ABFB3F /* UpperRightCalculation.swift in Sources */,
				984EDB0F29A42ED200D119D2 /* LaunchOnLogin.swift in Sources */,
				9824702F22AFA2E50037B409 /* AccessibilityAuthorization.swift in Sources */,
				988D066322EB4CA5004EABD7 /* FirstTwoThirdsCalculation.swift in Sources */,
				D04CE30A27817A9F00BD47B3 /* MiddleRightNinthCalculation.swift in Sources */,
				98BEFA482620DEDD00D9D54F /* NSImageExtension.swift in Sources */,
				98FA9497235A2D7600F95C4F /* RepeatedExecutionsCalculation.swift in Sources */,
				D0CFE33727A8CD51004DA47B /* BottomRightThirdCalculation.swift in Sources */,
				9824705122B28D7A0037B409 /* LeftRightHalfCalculation.swift in Sources */,
				9824703D22B13C7E0037B409 /* AccessibilityElement.swift in Sources */,
				98D1441324560B1E0090C603 /* AlertUtil.swift in Sources */,
				AA0AC004291C48DE00D125D2 /* SequenceExtension.swift in Sources */,
				985B9BF522B93EEC00A2E8F0 /* ApplicationToggle.swift in Sources */,
				AA0AC002291C1B9100D125D2 /* AXExtension.swift in Sources */,
				98D16A442592AD55005228CB /* MASShortcutMigration.swift in Sources */,
				988D066522EB4CB6004EABD7 /* CenterThirdCalculation.swift in Sources */,
				D04CE30827817A9200BD47B3 /* MiddleCenterNinthCalculation.swift in Sources */,
				98A009BD251253A000CFBF0C /* BottomCenterSixthCalculation.swift in Sources */,
				6490B39F27BF98840056C220 /* BottomCenterRightEighthCalculation.swift in Sources */,
				98C2755E231FF6A9009B9292 /* EventMonitor.swift in Sources */,
				98A009AF2512517900CFBF0C /* SecondFourthCalculation.swift in Sources */,
				6490B39127BF907A0056C220 /* BottomLeftEighthCalculation.swift in Sources */,
				D04CE30C27817AA900BD47B3 /* BottomLeftNinthCalculation.swift in Sources */,
				6490B39327BF90F90056C220 /* EighthsRepeated.swift in Sources */,
				9821406022B3EFB200ABFB3F /* Defaults.swift in Sources */,
				D0CFE33527A8CD16004DA47B /* BottomLeftThirdCalculation.swift in Sources */,
				D0CFE33127A8CAED004DA47B /* TopLeftThirdCalculation.swift in Sources */,
				FDE8FCE027C2950400EACCAA /* MultiWindowManager.swift in Sources */,
				98192DDA270F606C00015E66 /* Debounce.swift in Sources */,
				D04CE3002781794E00BD47B3 /* TopLeftNinthCalculation.swift in Sources */,
				9821403122B38A0500ABFB3F /* TopHalfCalculation.swift in Sources */,
				9818E01428B5A4FD004AA524 /* SixthsCompoundCalculation.swift in Sources */,
				9824704B22B189250037B409 /* StandardWindowMover.swift in Sources */,
				9821402722B3888100ABFB3F /* ChangeSizeCalculation.swift in Sources */,
				6490B39927BF97BB0056C220 /* TopCenterRightEighthCalculation.swift in Sources */,
				98B3559823CE025700E410E0 /* CenteringFixedSizedWindowMover.swift in Sources */,
				729E0A982AFF76B1006E2F48 /* CenterProminentlyCalculation.swift in Sources */,
				9824704E22B189250037B409 /* QuantizedWindowMover.swift in Sources */,
				AA536C2729005DD000579AC6 /* TimeoutCache.swift in Sources */,
				D04CE30627817A8400BD47B3 /* MiddleLeftNinthCalculation.swift in Sources */,
				9824703B22B139780037B409 /* CUtil.swift in Sources */,
				988D067D22EB4E17004EABD7 /* AlmostMaximizeCalculation.swift in Sources */,
				98C1008C2305F1FA006E5344 /* SubsequentExecutionMode.swift in Sources */,
				94E9B0902C3E4578004C7F41 /* StringExtension.swift in Sources */,
				6490B3A127BF98C70056C220 /* BottomRightEighthCalculation.swift in Sources */,
				985B9BF922BB6F5100A2E8F0 /* MessageView.swift in Sources */,
				988D066722EB4CC0004EABD7 /* LastTwoThirdsCalculation.swift in Sources */,
				98192DDE2717201100015E66 /* ReverseAllManager.swift in Sources */,
				AA0ACC2F2864A86D0025E376 /* StageUtil.swift in Sources */,
				98A009B1251252C900CFBF0C /* ThirdFourthCalculation.swift in Sources */,
				D04CE31227817C9B00BD47B3 /* NinthsRepeated.swift in Sources */,
				9824703F22B13FBC0037B409 /* ScreenDetection.swift in Sources */,
				983DD04428B0639E00BF1EEE /* SnapAreaModel.swift in Sources */,
				AA040C67290B2640003181D5 /* RunLoopThread.swift in Sources */,
				9821402B22B388A000ABFB3F /* LowerRightCalculation.swift in Sources */,
				D04CE30427817A6F00BD47B3 /* TopRightNinthCalculation.swift in Sources */,
				98FD7C612687BCB6009E9DAF /* LastThreeFourthsCalculation.swift in Sources */,
				9821402522B3887200ABFB3F /* MaximizeCalculation.swift in Sources */,
				866661F2257D248A00A9CD2D /* RepeatedExecutionsInThirdsCalculation.swift in Sources */,
				9824704D22B189250037B409 /* WindowCalculation.swift in Sources */,
				98C1008E230B9EF6006E5344 /* NextPrevDisplayCalculation.swift in Sources */,
				9824703122AFA8470037B409 /* RectangleStatusItem.swift in Sources */,
				D04CE31027817ABE00BD47B3 /* BottomRightNinthCalculation.swift in Sources */,
				98A009B92512538D00CFBF0C /* TopRightSixthCalculation.swift in Sources */,
				98C275672322E2DA009B9292 /* WindowHistory.swift in Sources */,
				9821403722B3D16700ABFB3F /* MaximizeHeightCalculation.swift in Sources */,
				9824704122B186D00037B409 /* WindowManager.swift in Sources */,
				D0423D8327A8D31D008A4894 /* HorizontalThirdsRepeated.swift in Sources */,
				98C97FFD25893B040061F01F /* Config.swift in Sources */,
				989DA30E243FC0DF008C7AA4 /* WelcomeViewController.swift in Sources */,
				9818E01228B59B64004AA524 /* ThirdsCompoundCalculation.swift in Sources */,
				98C27561231FFA5F009B9292 /* SnappingManager.swift in Sources */,
				9824703722B0F3200037B409 /* WindowAction.swift in Sources */,
				B4521F932BD7CEFB00FD43CC /* ChangeWindowDimensionCalculation.swift in Sources */,
				9821402922B3889100ABFB3F /* LowerLeftCalculation.swift in Sources */,
				7BE578EF2C5BF4EE0083DAE3 /* CycleSize.swift in Sources */,
				9821402122B3884600ABFB3F /* BottomHalfCalculation.swift in Sources */,
				9851A5C3251BEBA300ECF78C /* OrientationAware.swift in Sources */,
				98FD7C5F2687BC14009E9DAF /* FirstThreeFourthsCalculation.swift in Sources */,
				983DD04028A844BE00BF1EEE /* SnapAreaViewController.swift in Sources */,
				9824702C22AFA22E0037B409 /* AccessibilityWindowController.swift in Sources */,
				988D066122EB4C7C004EABD7 /* FirstThirdCalculation.swift in Sources */,
				983BBD6F253B609D000D223E /* FootprintWindow.swift in Sources */,
				988D067F22EB4EDE004EABD7 /* MoveLeftRightCalculation.swift in Sources */,
				AA3A9E8B29230A82004EB8E5 /* CFExtension.swift in Sources */,
				98C6DEF023CE191700CC0C1E /* GapCalculation.swift in Sources */,
				98910B3E231130AF0066EC23 /* SettingsViewController.swift in Sources */,
				98A009AD2512498000CFBF0C /* FirstFourthCalculation.swift in Sources */,
				AA4DA2FB28FDC94A00355CEB /* DispatchTimeExtension.swift in Sources */,
				98D4B6C525B6256C009C7BF6 /* TodoManager.swift in Sources */,
				B4780A322BD4C75900732B9E /* HalfOrDoubleDimensionCalculation.swift in Sources */,
				AA69F83C29909A95001A81AF /* RightTodoCalculation.swift in Sources */,
				AA49DD1129B8C1B100690E13 /* TitleBarManager.swift in Sources */,
				9824700D22AF9B7D0037B409 /* AppDelegate.swift in Sources */,
				98A009BF251253AB00CFBF0C /* BottomRightSixthCalculation.swift in Sources */,
				6490B39527BF96880056C220 /* TopLeftEighthCalculation.swift in Sources */,
				D04CE30227817A6100BD47B3 /* TopCenterNinthCalculation.swift in Sources */,
				98A009B52512537800CFBF0C /* TopLeftSixthCalculation.swift in Sources */,
				9824704F22B189250037B409 /* BestEffortWindowMover.swift in Sources */,
				30166BD024F27D6A00A38608 /* SpecifiedCalculation.swift in Sources */,
				9824703922B0F37C0037B409 /* ShortcutManager.swift in Sources */,
				98D16A492592B460005228CB /* NotificationExtension.swift in Sources */,
				AA0AC000291C1B5E00D125D2 /* CGExtension.swift in Sources */,
				6490B39D27BF984D0056C220 /* BottomCenterLeftEighthCalculation.swift in Sources */,
				98A6EDDD251F3F4A00F74B10 /* SixthsRepeated.swift in Sources */,
				AAADE1AF28CBAB0000036331 /* WindowUtil.swift in Sources */,
				98A009B72512538200CFBF0C /* TopCenterSixthCalculation.swift in Sources */,
				AA69F8402992DCB1001A81AF /* LeftTodoCalculation.swift in Sources */,
				9821403322B38A1B00ABFB3F /* UpperLeftCalculation.swift in Sources */,
				98987AA52391890400BE72C4 /* LogViewer.swift in Sources */,
				9824704C22B189250037B409 /* WindowMover.swift in Sources */,
				6490B39B27BF980F0056C220 /* TopRightEighthCalculation.swift in Sources */,
				944F25CD2CE5A144004B2FD2 /* PrefsViewController.swift in Sources */,
				9818E01028B59396004AA524 /* HalvesCompoundCalculation.swift in Sources */,
				988D066922EB4CCB004EABD7 /* LastThirdCalculation.swift in Sources */,
				98A6EDEC2528FFC100F74B10 /* WindowActionCategory.swift in Sources */,
				988D068322EB4EF3004EABD7 /* MoveUpDownCalculation.swift in Sources */,
				D04CE30E27817AB500BD47B3 /* BottomCenterNinthCalculation.swift in Sources */,
				6490B39727BF96EA0056C220 /* TopCenterLeftEighthCalculation.swift in Sources */,
				9818E00D28B59205004AA524 /* CompoundSnapArea.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9824701722AF9B7E0037B409 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9824702022AF9B7E0037B409 /* RectangleTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		9824701D22AF9B7E0037B409 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9824700822AF9B7D0037B409 /* Rectangle */;
			targetProxy = 9824701C22AF9B7E0037B409 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		9821404422B3EBDA00ABFB3F /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				9821404522B3EBDA00ABFB3F /* Base */,
				42627A972ADA03D200D047C6 /* mul */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		9824701222AF9B7E0037B409 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				9824701322AF9B7E0037B409 /* Base */,
				423DC1992AE681F900C98564 /* mul */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		9821405522B3EBDA00ABFB3F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = RectangleLauncher/RectangleLauncher.entitlements;
				CODE_SIGN_IDENTITY = "-";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_HARDENED_RUNTIME = NO;
				INFOPLIST_FILE = RectangleLauncher/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.knollsoft.RectangleLauncher;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		9821405622B3EBDA00ABFB3F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = RectangleLauncher/RectangleLauncherRelease.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = XSYZ3E4B7D;
				ENABLE_HARDENED_RUNTIME = YES;
				INFOPLIST_FILE = RectangleLauncher/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.knollsoft.RectangleLauncher;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		9824702222AF9B7E0037B409 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "Mac Developer";
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		9824702322AF9B7E0037B409 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "Mac Developer";
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Release;
		};
		9824702522AF9B7E0037B409 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Rectangle/Rectangle.entitlements;
				CODE_SIGN_IDENTITY = "-";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 93;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = "";
				ENABLE_HARDENED_RUNTIME = NO;
				INFOPLIST_FILE = Rectangle/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MARKETING_VERSION = 0.87;
				PRODUCT_BUNDLE_IDENTIFIER = com.knollsoft.Rectangle;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Rectangle/Rectangle-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		9824702622AF9B7E0037B409 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Rectangle/RectangleRelease.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 93;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = XSYZ3E4B7D;
				ENABLE_HARDENED_RUNTIME = YES;
				INFOPLIST_FILE = Rectangle/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MARKETING_VERSION = 0.87;
				PRODUCT_BUNDLE_IDENTIFIER = com.knollsoft.Rectangle;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Rectangle/Rectangle-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		9824702822AF9B7E0037B409 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_IDENTITY = "-";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = RectangleTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				PRODUCT_BUNDLE_IDENTIFIER = dev.ryanhanson.RectangleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Rectangle.app/Contents/MacOS/Rectangle";
			};
			name = Debug;
		};
		9824702922AF9B7E0037B409 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = XSYZ3E4B7D;
				INFOPLIST_FILE = RectangleTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				PRODUCT_BUNDLE_IDENTIFIER = dev.ryanhanson.RectangleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Rectangle.app/Contents/MacOS/Rectangle";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		9821405422B3EBDA00ABFB3F /* Build configuration list for PBXNativeTarget "RectangleLauncher" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9821405522B3EBDA00ABFB3F /* Debug */,
				9821405622B3EBDA00ABFB3F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9824700422AF9B7D0037B409 /* Build configuration list for PBXProject "Rectangle" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9824702222AF9B7E0037B409 /* Debug */,
				9824702322AF9B7E0037B409 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9824702422AF9B7E0037B409 /* Build configuration list for PBXNativeTarget "Rectangle" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9824702522AF9B7E0037B409 /* Debug */,
				9824702622AF9B7E0037B409 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9824702722AF9B7E0037B409 /* Build configuration list for PBXNativeTarget "RectangleTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9824702822AF9B7E0037B409 /* Debug */,
				9824702922AF9B7E0037B409 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		9877B63829C8AC0E00F02D74 /* XCRemoteSwiftPackageReference "MASShortcut" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/rxhanson/MASShortcut";
			requirement = {
				branch = master;
				kind = branch;
			};
		};
		9877B63B29C8AC3600F02D74 /* XCRemoteSwiftPackageReference "Sparkle" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/sparkle-project/Sparkle";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		9877B63929C8AC0E00F02D74 /* MASShortcut */ = {
			isa = XCSwiftPackageProductDependency;
			package = 9877B63829C8AC0E00F02D74 /* XCRemoteSwiftPackageReference "MASShortcut" */;
			productName = MASShortcut;
		};
		9877B63C29C8AC3600F02D74 /* Sparkle */ = {
			isa = XCSwiftPackageProductDependency;
			package = 9877B63B29C8AC3600F02D74 /* XCRemoteSwiftPackageReference "Sparkle" */;
			productName = Sparkle;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 9824700122AF9B7D0037B409 /* Project object */;
}
