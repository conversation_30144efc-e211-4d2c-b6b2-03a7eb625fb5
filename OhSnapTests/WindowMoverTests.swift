import XCTest

@testable import OhSnap

final class WindowMoverTests: XCTestCase {
    var windowMover: WindowMover!
    var mockAccessibilityElement: TestMockAccessibilityElement!
    var mockCalculationService: TestMockCalculationService!
    var mockScreenDetection: TestMockScreenDetection!

    override func setUp() {
        super.setUp()
        mockAccessibilityElement = TestMockAccessibilityElement()
        mockCalculationService = TestMockCalculationService()
        mockScreenDetection = TestMockScreenDetection()

        windowMover = WindowMover(
            accessibilityElement: mockAccessibilityElement,
            calculationService: mockCalculationService,
            screenDetection: mockScreenDetection
        )
    }

    override func tearDown() {
        windowMover = nil
        mockAccessibilityElement = nil
        mockCalculationService = nil
        mockScreenDetection = nil
        super.tearDown()
    }

    // MARK: - Test Methods

    func testMoveWindowBasicCase() async throws {
        // Skip this test for now as it requires more complex mocking
        // We'll need to revisit this test later
    }

    func testMoveWindowHandlesWindowInfoError() async {
        // Given
        let window = AXUIElementCreateSystemWide()
        let direction = WindowDirection.leftHalf
        let screen = TestMockNSScreen()

        // Set up mock to throw error when getting window info
        mockAccessibilityElement.shouldThrowError = true

        // When/Then
        do {
            try await windowMover.moveWindow(window, to: direction, on: screen)
            XCTFail("Should have thrown an error")
        } catch let error as WindowMoveError {
            XCTAssertEqual(error, WindowMoveError.failedToGetWindowInfo)
        } catch {
            XCTFail("Unexpected error type: \(error)")
        }
    }

    func testMoveWindowHandlesSetFrameError() async {
        // Given
        let window = AXUIElementCreateSystemWide()
        let direction = WindowDirection.leftHalf
        let screen = TestMockNSScreen()
        screen.mockFrame = CGRect(x: 0, y: 0, width: 1000, height: 800)

        // Set up mock window info
        let windowInfo = WindowInfo(
            frame: CGRect(x: 100, y: 100, width: 500, height: 400),
            monitorID: nil,
            appBundleIdentifier: nil,
            isFullscreen: false
        )
        mockAccessibilityElement.mockWindowInfo = windowInfo

        // Set up mock calculation result
        let targetFrame = CGRect(x: 0, y: 0, width: 500, height: 800)
        mockCalculationService.calculatedRect = targetFrame

        // Set up mock screen detection
        mockScreenDetection.targetScreen = screen

        // Set up mock to throw error when setting frame
        mockAccessibilityElement.shouldThrowErrorOnSetFrame = true

        // When/Then
        do {
            try await windowMover.moveWindow(window, to: direction, on: screen)
            XCTFail("Should have thrown an error")
        } catch {
            // Just verify that an error was thrown
            XCTAssertTrue(true)
        }
    }

    func testMoveWindowWithRetry() async throws {
        // Skip this test for now as it requires more complex mocking
        // We'll need to revisit this test later
    }
}
