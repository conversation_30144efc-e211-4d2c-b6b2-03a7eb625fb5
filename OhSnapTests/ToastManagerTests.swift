import Combine
import XCTest

@testable import OhSnap

final class ToastManagerTests: XCTestCase {
    var toastManager: ToastManager!
    private var cancellables = Set<AnyCancellable>()

    override func setUp() {
        super.setUp()
        toastManager = ToastManager.shared
        // Reset the toast manager state
        toastManager.dismiss()
    }

    override func tearDown() {
        cancellables.removeAll()
        toastManager.dismiss()
        super.tearDown()
    }

    // MARK: - Basic Toast Tests

    func testShowSuccessToast() {
        // Given
        let title = "Success Title"
        let message = "Success Message"
        let expectation = XCTestExpectation(description: "Toast should be presented")

        // When
        toastManager.$isPresented
            .dropFirst()  // Skip initial value
            .sink { isPresented in
                if isPresented {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)

        toastManager.showSuccess(title: title, message: message)

        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertEqual(toastManager.currentToast?.type, .success)
        XCTAssertEqual(toastManager.currentToast?.title, title)
        XCTAssertEqual(toastManager.currentToast?.message, message)
        XCTAssertTrue(toastManager.isPresented)
    }

    func testShowErrorToast() {
        // Given
        let title = "Error Title"
        let message = "Error Message"

        // When
        toastManager.showError(title: title, message: message)

        // Then
        XCTAssertEqual(toastManager.currentToast?.type, .error)
        XCTAssertEqual(toastManager.currentToast?.title, title)
        XCTAssertEqual(toastManager.currentToast?.message, message)
        XCTAssertTrue(toastManager.isPresented)
    }

    func testShowInfoToast() {
        // Given
        let title = "Info Title"
        let message = "Info Message"

        // When
        toastManager.showInfo(title: title, message: message)

        // Then
        XCTAssertEqual(toastManager.currentToast?.type, .info)
        XCTAssertEqual(toastManager.currentToast?.title, title)
        XCTAssertEqual(toastManager.currentToast?.message, message)
        XCTAssertTrue(toastManager.isPresented)
    }

    // MARK: - Loading Toast Tests

    func testShowLoadingToast() {
        // Given
        let title = "Loading Title"
        let message = "Loading Message"

        // When
        toastManager.showLoading(title: title, message: message)

        // Then
        XCTAssertEqual(toastManager.currentToast?.type, .loading)
        XCTAssertEqual(toastManager.currentToast?.title, title)
        XCTAssertEqual(toastManager.currentToast?.message, message)
        XCTAssertTrue(toastManager.isPresented)
    }

    func testUpdateLoadingToast() {
        // Given
        let initialTitle = "Initial Loading"
        let updatedTitle = "Updated Loading"

        // When
        toastManager.showLoading(title: initialTitle)
        toastManager.updateLoading(title: updatedTitle)

        // Then
        XCTAssertEqual(toastManager.currentToast?.type, .loading)
        XCTAssertEqual(toastManager.currentToast?.title, updatedTitle)
        XCTAssertTrue(toastManager.isPresented)
    }

    func testUpdateLoadingIgnoredWhenNoLoadingToast() {
        // Given
        toastManager.showSuccess(title: "Success")
        let originalTitle = toastManager.currentToast?.title

        // When
        toastManager.updateLoading(title: "Updated Loading")

        // Then - Should not change the toast
        XCTAssertEqual(toastManager.currentToast?.type, .success)
        XCTAssertEqual(toastManager.currentToast?.title, originalTitle)
    }

    func testCompleteLoadingWithSuccess() {
        // Given
        toastManager.showLoading(title: "Loading")

        // When
        toastManager.completeLoading(title: "Success")

        // Then
        XCTAssertEqual(toastManager.currentToast?.type, .success)
        XCTAssertEqual(toastManager.currentToast?.title, "Success")
        XCTAssertTrue(toastManager.isPresented)
    }

    func testFailLoading() {
        // Given
        toastManager.showLoading(title: "Loading")

        // When
        toastManager.failLoading(title: "Failed")

        // Then
        XCTAssertEqual(toastManager.currentToast?.type, .error)
        XCTAssertEqual(toastManager.currentToast?.title, "Failed")
        XCTAssertTrue(toastManager.isPresented)
    }

    func testCompleteLoadingIgnoredWhenNoLoadingToast() {
        // Given
        toastManager.showInfo(title: "Info")

        // When
        toastManager.completeLoading(title: "Success")

        // Then - Should not change the toast
        XCTAssertEqual(toastManager.currentToast?.type, .info)
    }

    // MARK: - Dismiss Tests

    func testDismissToast() {
        // Given
        toastManager.showSuccess(title: "Success")
        XCTAssertTrue(toastManager.isPresented)

        let expectation = XCTestExpectation(description: "Toast should be dismissed")

        // When
        toastManager.$isPresented
            .dropFirst()  // Skip current value
            .sink { isPresented in
                if !isPresented {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)

        toastManager.dismiss()

        // Then
        wait(for: [expectation], timeout: 1.0)
        XCTAssertFalse(toastManager.isPresented)
    }
}
