import XCTest

@testable import OhSnap

final class LoggingServiceTests: XCTestCase {
    var loggingService: LoggingService!

    override func setUp() {
        super.setUp()
        loggingService = LoggingService.shared

        // Save original settings
        saveOriginalSettings()
    }

    override func tearDown() {
        // Restore original settings
        restoreOriginalSettings()
        super.tearDown()
    }

    // Original settings to restore after tests
    private var originalMinimumLogLevel: LogLevel!
    private var originalIsLoggingEnabled: Bool!
    private var originalEnabledCategories: [LogCategory] = []

    private func saveOriginalSettings() {
        originalMinimumLogLevel = loggingService.minimumLogLevel
        originalIsLoggingEnabled = loggingService.isLoggingEnabled
        originalEnabledCategories = LogCategory.allCases.filter {
            loggingService.isCategoryEnabled($0)
        }
    }

    private func restoreOriginalSettings() {
        loggingService.minimumLogLevel = originalMinimumLogLevel
        loggingService.isLoggingEnabled = originalIsLoggingEnabled

        // First disable all categories
        loggingService.disableAllCategories()

        // Then enable only the original ones
        originalEnabledCategories.forEach { loggingService.enableCategory($0) }
    }

    // MARK: - Log Level Tests

    func testLogLevelSetting() {
        // Given
        loggingService.minimumLogLevel = .warning

        // When/Then
        XCTAssertEqual(
            loggingService.minimumLogLevel, .warning, "Minimum log level should be warning")

        // Change it
        loggingService.minimumLogLevel = .error

        // Verify change
        XCTAssertEqual(loggingService.minimumLogLevel, .error, "Minimum log level should be error")
    }

    func testLogLevelHierarchy() {
        // Test the hierarchy of log levels
        // The order might be different in the actual implementation
        // Let's just verify that all levels are different
        XCTAssertNotEqual(
            LogLevel.error.rawValue, LogLevel.warning.rawValue,
            "Error should be different from warning")
        XCTAssertNotEqual(
            LogLevel.warning.rawValue, LogLevel.info.rawValue,
            "Warning should be different from info"
        )
        XCTAssertNotEqual(
            LogLevel.info.rawValue, LogLevel.debug.rawValue, "Info should be different from debug")
    }

    // MARK: - Category Tests

    func testCategoryEnableDisable() {
        // First disable all categories
        loggingService.disableAllCategories()

        // Enable specific categories
        loggingService.enableCategory(.general)
        loggingService.enableCategory(.windowSnapping)

        // Verify
        XCTAssertTrue(
            loggingService.isCategoryEnabled(.general), "General category should be enabled")
        XCTAssertTrue(
            loggingService.isCategoryEnabled(.windowSnapping),
            "WindowSnapping category should be enabled")
        XCTAssertFalse(
            loggingService.isCategoryEnabled(.screenDetection),
            "ScreenDetection category should be disabled")

        // Disable a category
        loggingService.disableCategory(.windowSnapping)

        // Verify
        XCTAssertTrue(
            loggingService.isCategoryEnabled(.general), "General category should still be enabled")
        XCTAssertFalse(
            loggingService.isCategoryEnabled(.windowSnapping),
            "WindowSnapping category should now be disabled")
    }

    func testEnableAllCategories() {
        // Given
        loggingService.disableAllCategories()

        // When
        loggingService.enableAllCategories()

        // Then
        for category in LogCategory.allCases {
            XCTAssertTrue(
                loggingService.isCategoryEnabled(category), "\(category) should be enabled")
        }
    }

    func testDisableAllCategories() {
        // Given
        loggingService.enableAllCategories()

        // When
        loggingService.disableAllCategories()

        // Then
        for category in LogCategory.allCases {
            XCTAssertFalse(
                loggingService.isCategoryEnabled(category), "\(category) should be disabled")
        }
    }

    // MARK: - Logging Enabled Tests

    func testLoggingEnabledFlag() {
        // Given
        loggingService.isLoggingEnabled = true

        // When
        XCTAssertTrue(loggingService.isLoggingEnabled, "Logging should be enabled")

        // Change it
        loggingService.isLoggingEnabled = false

        // Then
        XCTAssertFalse(loggingService.isLoggingEnabled, "Logging should be disabled")
    }

    // MARK: - Service Name Category Mapping

    func testServiceNameCategoryMapping() {
        // Test a few key mappings
        XCTAssertEqual(LogCategory.categoryForService("WindowSnappingService"), .windowSnapping)
        XCTAssertEqual(LogCategory.categoryForService("ScreenDetectionService"), .screenDetection)
        XCTAssertEqual(LogCategory.categoryForService("WorkspaceService"), .workspaces)
        XCTAssertEqual(
            LogCategory.categoryForService("UnknownService"), .general,
            "Unknown services should map to general")
    }

    // MARK: - Logging Methods

    func testLoggingMethods() {
        // This is a simple test to ensure the logging methods don't crash
        // We can't easily test the output since it goes to stdout

        // Enable logging
        loggingService.isLoggingEnabled = true
        loggingService.minimumLogLevel = .debug
        loggingService.enableAllCategories()

        // Call all logging methods
        loggingService.debug("Debug message", service: "TestService")
        loggingService.info("Info message", service: "TestService")
        loggingService.warning("Warning message", service: "TestService")
        loggingService.error("Error message", service: "TestService")

        // No assertions needed - we're just making sure they don't crash
        XCTAssertTrue(true)
    }
}
