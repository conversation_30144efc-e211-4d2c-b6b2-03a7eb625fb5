import Combine
import XCTest

@testable import OhSnap

final class PermissionManagerTests: XCTestCase {
    var permissionManager: PermissionManager!
    private var cancellables = Set<AnyCancellable>()
    
    override func setUp() {
        super.setUp()
        permissionManager = PermissionManager.shared
    }
    
    override func tearDown() {
        cancellables.removeAll()
        super.tearDown()
    }
    
    // MARK: - Permission Status Tests
    
    func testCheckAccessibilityPermission() {
        // When
        let result = permissionManager.checkAccessibilityPermission()
        
        // Then - Just verify the method runs without crashing
        // We can't reliably test the actual result since it depends on system permissions
        XCTAssertNotNil(result)
    }
    
    // MARK: - Notification Tests
    
    func testPermissionStatusPublisher() {
        // This test just verifies that the published property exists and can be subscribed to
        let expectation = XCTestExpectation(description: "Should be able to subscribe to permission status")
        
        // When
        let cancellable = permissionManager.$isAccessibilityPermissionGranted
            .sink { _ in
                expectation.fulfill()
            }
        
        // Then
        wait(for: [expectation], timeout: 1.0)
        cancellable.cancel()
    }
}