import SwiftUI
import XCTest

@testable import OhSnap

final class WorkspaceDetailViewTests: XCTestCase {

    // MARK: - Properties

    private var workspaceService: WorkspaceService!
    private var snappingService: WindowSnappingService!

    // MARK: - Setup & Teardown

    override func setUp() {
        super.setUp()
        snappingService = WindowSnappingService()
        workspaceService = WorkspaceService(snappingService: snappingService)
    }

    override func tearDown() {
        workspaceService = nil
        snappingService = nil
        super.tearDown()
    }

    // MARK: - Basic Initialization Tests

    func testInitializationWithBasicParameters() {
        // Given
        let workspace = createSampleWorkspace()

        // When
        let view = WorkspaceDetailView(workspace: workspace)
            .environmentObject(workspaceService)

        // Then
        XCTAssertNotNil(view)
    }

    // MARK: - Display Compatibility Tests

    func testDisplayCompatibilityCheck() {
        // Given
        let workspace = createSampleWorkspace()

        // When - Create the view
        let view = WorkspaceDetailView(workspace: workspace)
            .environmentObject(workspaceService)

        // Then - Just verify the view initializes without crashing
        // We can't directly test the private checkDisplayCompatibility method
        XCTAssertNotNil(view)
    }

    // MARK: - App Grouping Tests

    func testAppGroupsDisplay() {
        // Given
        let workspace = createSampleWorkspace()

        // When - Create the view
        let view = WorkspaceDetailView(workspace: workspace)
            .environmentObject(workspaceService)

        // Then - Just verify the view initializes without crashing
        // We can't directly test the private getAppGroups method
        XCTAssertNotNil(view)
    }

    func testAppNameDisplay() {
        // Given
        let workspace = createSampleWorkspace()

        // When - Create the view
        let view = WorkspaceDetailView(workspace: workspace)
            .environmentObject(workspaceService)

        // Then - Just verify the view initializes without crashing
        // We can't directly test the private getAppName method
        XCTAssertNotNil(view)
    }

    func testWindowsForAppDisplay() {
        // Given
        let workspace = createSampleWorkspace()

        // When - Create the view
        let view = WorkspaceDetailView(workspace: workspace)
            .environmentObject(workspaceService)

        // Then - Just verify the view initializes without crashing
        // We can't directly test the private getWindowsForApp method
        XCTAssertNotNil(view)
    }

    // MARK: - Preview Scale Tests

    func testPreviewScaleCalculation() {
        // Given
        let workspace = createSampleWorkspace()

        // When - Create the view
        let view = WorkspaceDetailView(workspace: workspace)
            .environmentObject(workspaceService)

        // Then - Just verify the view initializes without crashing
        // We can't directly test the private previewScale property
        XCTAssertNotNil(view)
    }

    // MARK: - Helper Methods

    private func createSampleWorkspace() -> Workspace {
        return Workspace(
            id: UUID(),
            name: "Sample Workspace",
            windowInfos: [
                WindowInfo(
                    frame: CGRect(x: 100, y: 100, width: 800, height: 600),
                    monitorID: NSScreen.main?.deviceDescription[
                        NSDeviceDescriptionKey("NSScreenNumber")
                    ].map {
                        UUID(uuidString: String(($0 as! NSNumber).intValue))!
                    },
                    appBundleIdentifier: "com.apple.Safari",
                    isFullscreen: false,
                    zOrder: 0
                ),
                WindowInfo(
                    frame: CGRect(x: 200, y: 300, width: 600, height: 400),
                    monitorID: NSScreen.main?.deviceDescription[
                        NSDeviceDescriptionKey("NSScreenNumber")
                    ].map {
                        UUID(uuidString: String(($0 as! NSNumber).intValue))!
                    },
                    appBundleIdentifier: "com.apple.finder",
                    isFullscreen: false,
                    zOrder: 1
                ),
            ],
            shortcutKeyCode: nil,
            shortcutModifiers: nil
        )
    }
}
