# Contributing

## Feature Requests

Rectang<PERSON> is not accepting any new feature requests at this time, sorry. You can file a feature request for a feature that you plan to implement and submit a pull request for, so that the feature can be reviewed and you will know ahead of time if the feature will be rejected.  

## Bugs

Please search through the existing issues, open and closed, before filing a new bug.
Add the version of Rectangle, the version of the OS, and screenshots or videos as necessary.

## Coding Style

Please match the existing coding style as much as possible.

## License

By contributing to Rectangle you agree that your contributions will be licensed under its MIT license.

## Incentives

Logic from Rectangle is used in the [Multitouch](https://multitouch.app) app. The [Rectangle Pro](https://rectangleapp.com/pro) app is entirely built on top of Rectangle. If you contribute significant code or localizations that get merged into Rectangle, you get a free license of Multitouch or Rectangle Pro. Contributors to Sparkle, MASShortcut, or Spectacle can also receive free Multitouch or Rectangle Pro licenses (just send me a direct message on [Gitter](https://gitter.im)).
