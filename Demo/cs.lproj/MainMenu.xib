<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="8173.3" systemVersion="14E46" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="8173.3"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="NSApplication">
            <connections>
                <outlet property="delegate" destination="494" id="495"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <menu title="AMainMenu" systemMenu="main" id="29">
            <items>
                <menuItem title="Demo" id="56">
                    <menu key="submenu" title="Demo" systemMenu="apple" id="57">
                        <items>
                            <menuItem title="About Demo" id="58">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="orderFrontStandardAboutPanel:" target="-2" id="142"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="236">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Preferences…" keyEquivalent="," id="129"/>
                            <menuItem isSeparatorItem="YES" id="143">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Services" id="131">
                                <menu key="submenu" title="Services" systemMenu="services" id="130"/>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="144">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Hide Demo" keyEquivalent="h" id="134">
                                <connections>
                                    <action selector="hide:" target="-1" id="367"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Hide Others" keyEquivalent="h" id="145">
                                <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                <connections>
                                    <action selector="hideOtherApplications:" target="-1" id="368"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Show All" id="150">
                                <connections>
                                    <action selector="unhideAllApplications:" target="-1" id="370"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="149">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Quit Demo" keyEquivalent="q" id="136">
                                <connections>
                                    <action selector="terminate:" target="-3" id="449"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="File" id="83">
                    <menu key="submenu" title="File" id="81">
                        <items>
                            <menuItem title="New" keyEquivalent="n" id="82">
                                <connections>
                                    <action selector="newDocument:" target="-1" id="373"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Open…" keyEquivalent="o" id="72">
                                <connections>
                                    <action selector="openDocument:" target="-1" id="374"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Open Recent" id="124">
                                <menu key="submenu" title="Open Recent" systemMenu="recentDocuments" id="125">
                                    <items>
                                        <menuItem title="Clear Menu" id="126">
                                            <connections>
                                                <action selector="clearRecentDocuments:" target="-1" id="127"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="79">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Close" keyEquivalent="w" id="73">
                                <connections>
                                    <action selector="performClose:" target="-1" id="193"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Save…" keyEquivalent="s" id="75">
                                <connections>
                                    <action selector="saveDocument:" target="-1" id="362"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Revert to Saved" id="112">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="revertDocumentToSaved:" target="-1" id="364"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="74">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Page Setup..." keyEquivalent="P" id="77">
                                <modifierMask key="keyEquivalentModifierMask" shift="YES" command="YES"/>
                                <connections>
                                    <action selector="runPageLayout:" target="-1" id="87"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Print…" keyEquivalent="p" id="78">
                                <connections>
                                    <action selector="print:" target="-1" id="86"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="Edit" id="217">
                    <menu key="submenu" title="Edit" id="205">
                        <items>
                            <menuItem title="Undo" keyEquivalent="z" id="207">
                                <connections>
                                    <action selector="undo:" target="-1" id="223"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Redo" keyEquivalent="Z" id="215">
                                <modifierMask key="keyEquivalentModifierMask" shift="YES" command="YES"/>
                                <connections>
                                    <action selector="redo:" target="-1" id="231"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="206">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Cut" keyEquivalent="x" id="199">
                                <connections>
                                    <action selector="cut:" target="-1" id="228"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Copy" keyEquivalent="c" id="197">
                                <connections>
                                    <action selector="copy:" target="-1" id="224"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Paste" keyEquivalent="v" id="203">
                                <connections>
                                    <action selector="paste:" target="-1" id="226"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Paste and Match Style" keyEquivalent="V" id="485">
                                <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                <connections>
                                    <action selector="pasteAsPlainText:" target="-1" id="486"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Delete" id="202">
                                <connections>
                                    <action selector="delete:" target="-1" id="235"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Select All" keyEquivalent="a" id="198">
                                <connections>
                                    <action selector="selectAll:" target="-1" id="232"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="214">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Find" id="218">
                                <menu key="submenu" title="Find" id="220">
                                    <items>
                                        <menuItem title="Find…" tag="1" keyEquivalent="f" id="209">
                                            <connections>
                                                <action selector="performFindPanelAction:" target="-1" id="241"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Find and Replace…" tag="12" keyEquivalent="f" id="534">
                                            <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                            <connections>
                                                <action selector="performFindPanelAction:" target="-1" id="535"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Find Next" tag="2" keyEquivalent="g" id="208">
                                            <connections>
                                                <action selector="performFindPanelAction:" target="-1" id="487"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Find Previous" tag="3" keyEquivalent="G" id="213">
                                            <modifierMask key="keyEquivalentModifierMask" shift="YES" command="YES"/>
                                            <connections>
                                                <action selector="performFindPanelAction:" target="-1" id="488"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Use Selection for Find" tag="7" keyEquivalent="e" id="221">
                                            <connections>
                                                <action selector="performFindPanelAction:" target="-1" id="489"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Jump to Selection" keyEquivalent="j" id="210">
                                            <connections>
                                                <action selector="centerSelectionInVisibleArea:" target="-1" id="245"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="Spelling and Grammar" id="216">
                                <menu key="submenu" title="Spelling and Grammar" id="200">
                                    <items>
                                        <menuItem title="Show Spelling and Grammar" keyEquivalent=":" id="204">
                                            <connections>
                                                <action selector="showGuessPanel:" target="-1" id="230"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Check Document Now" keyEquivalent=";" id="201">
                                            <connections>
                                                <action selector="checkSpelling:" target="-1" id="225"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="453"/>
                                        <menuItem title="Check Spelling While Typing" id="219">
                                            <connections>
                                                <action selector="toggleContinuousSpellChecking:" target="-1" id="222"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Check Grammar With Spelling" id="346">
                                            <connections>
                                                <action selector="toggleGrammarChecking:" target="-1" id="347"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Correct Spelling Automatically" id="454">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="toggleAutomaticSpellingCorrection:" target="-1" id="456"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="Substitutions" id="348">
                                <menu key="submenu" title="Substitutions" id="349">
                                    <items>
                                        <menuItem title="Show Substitutions" id="457">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="orderFrontSubstitutionsPanel:" target="-1" id="458"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="459"/>
                                        <menuItem title="Smart Copy/Paste" tag="1" keyEquivalent="f" id="350">
                                            <connections>
                                                <action selector="toggleSmartInsertDelete:" target="-1" id="355"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Smart Quotes" tag="2" keyEquivalent="g" id="351">
                                            <connections>
                                                <action selector="toggleAutomaticQuoteSubstitution:" target="-1" id="356"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Smart Dashes" id="460">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="toggleAutomaticDashSubstitution:" target="-1" id="461"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Smart Links" tag="3" keyEquivalent="G" id="354">
                                            <modifierMask key="keyEquivalentModifierMask" shift="YES" command="YES"/>
                                            <connections>
                                                <action selector="toggleAutomaticLinkDetection:" target="-1" id="357"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Text Replacement" id="462">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="toggleAutomaticTextReplacement:" target="-1" id="463"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="Transformations" id="450">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Transformations" id="451">
                                    <items>
                                        <menuItem title="Make Upper Case" id="452">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="uppercaseWord:" target="-1" id="464"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Make Lower Case" id="465">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="lowercaseWord:" target="-1" id="468"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Capitalize" id="466">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="capitalizeWord:" target="-1" id="467"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="Speech" id="211">
                                <menu key="submenu" title="Speech" id="212">
                                    <items>
                                        <menuItem title="Start Speaking" id="196">
                                            <connections>
                                                <action selector="startSpeaking:" target="-1" id="233"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Stop Speaking" id="195">
                                            <connections>
                                                <action selector="stopSpeaking:" target="-1" id="227"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="Format" id="375">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <menu key="submenu" title="Format" id="376">
                        <items>
                            <menuItem title="Font" id="377">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Font" systemMenu="font" id="388">
                                    <items>
                                        <menuItem title="Show Fonts" keyEquivalent="t" id="389">
                                            <connections>
                                                <action selector="orderFrontFontPanel:" target="420" id="424"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Bold" tag="2" keyEquivalent="b" id="390">
                                            <connections>
                                                <action selector="addFontTrait:" target="420" id="421"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Italic" tag="1" keyEquivalent="i" id="391">
                                            <connections>
                                                <action selector="addFontTrait:" target="420" id="422"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Underline" keyEquivalent="u" id="392">
                                            <connections>
                                                <action selector="underline:" target="-1" id="432"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="393"/>
                                        <menuItem title="Bigger" tag="3" keyEquivalent="+" id="394">
                                            <connections>
                                                <action selector="modifyFont:" target="420" id="425"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Smaller" tag="4" keyEquivalent="-" id="395">
                                            <connections>
                                                <action selector="modifyFont:" target="420" id="423"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="396"/>
                                        <menuItem title="Kern" id="397">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <menu key="submenu" title="Kern" id="415">
                                                <items>
                                                    <menuItem title="Use Default" id="416">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="useStandardKerning:" target="-1" id="438"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Use None" id="417">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="turnOffKerning:" target="-1" id="441"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Tighten" id="418">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="tightenKerning:" target="-1" id="431"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Loosen" id="419">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="loosenKerning:" target="-1" id="435"/>
                                                        </connections>
                                                    </menuItem>
                                                </items>
                                            </menu>
                                        </menuItem>
                                        <menuItem title="Ligatures" id="398">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <menu key="submenu" title="Ligatures" id="411">
                                                <items>
                                                    <menuItem title="Use Default" id="412">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="useStandardLigatures:" target="-1" id="439"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Use None" id="413">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="turnOffLigatures:" target="-1" id="440"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Use All" id="414">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="useAllLigatures:" target="-1" id="434"/>
                                                        </connections>
                                                    </menuItem>
                                                </items>
                                            </menu>
                                        </menuItem>
                                        <menuItem title="Baseline" id="399">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <menu key="submenu" title="Baseline" id="405">
                                                <items>
                                                    <menuItem title="Use Default" id="406">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="unscript:" target="-1" id="437"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Superscript" id="407">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="superscript:" target="-1" id="430"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Subscript" id="408">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="subscript:" target="-1" id="429"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Raise" id="409">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="raiseBaseline:" target="-1" id="426"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Lower" id="410">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="lowerBaseline:" target="-1" id="427"/>
                                                        </connections>
                                                    </menuItem>
                                                </items>
                                            </menu>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="400"/>
                                        <menuItem title="Show Colors" keyEquivalent="C" id="401">
                                            <connections>
                                                <action selector="orderFrontColorPanel:" target="-1" id="433"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="402"/>
                                        <menuItem title="Copy Style" keyEquivalent="c" id="403">
                                            <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                            <connections>
                                                <action selector="copyFont:" target="-1" id="428"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Paste Style" keyEquivalent="v" id="404">
                                            <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                            <connections>
                                                <action selector="pasteFont:" target="-1" id="436"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="Text" id="496">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Text" id="497">
                                    <items>
                                        <menuItem title="Align Left" keyEquivalent="{" id="498">
                                            <connections>
                                                <action selector="alignLeft:" target="-1" id="524"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Center" keyEquivalent="|" id="499">
                                            <connections>
                                                <action selector="alignCenter:" target="-1" id="518"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Justify" id="500">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="alignJustified:" target="-1" id="523"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Align Right" keyEquivalent="}" id="501">
                                            <connections>
                                                <action selector="alignRight:" target="-1" id="521"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="502"/>
                                        <menuItem title="Writing Direction" id="503">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <menu key="submenu" title="Writing Direction" id="508">
                                                <items>
                                                    <menuItem title="Paragraph" enabled="NO" id="509">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                    </menuItem>
                                                    <menuItem id="510">
                                                        <string key="title">	Default</string>
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="makeBaseWritingDirectionNatural:" target="-1" id="525"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem id="511">
                                                        <string key="title">	Left to Right</string>
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="makeBaseWritingDirectionLeftToRight:" target="-1" id="526"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem id="512">
                                                        <string key="title">	Right to Left</string>
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="makeBaseWritingDirectionRightToLeft:" target="-1" id="527"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem isSeparatorItem="YES" id="513"/>
                                                    <menuItem title="Selection" enabled="NO" id="514">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                    </menuItem>
                                                    <menuItem id="515">
                                                        <string key="title">	Default</string>
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="makeTextWritingDirectionNatural:" target="-1" id="528"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem id="516">
                                                        <string key="title">	Left to Right</string>
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="makeTextWritingDirectionLeftToRight:" target="-1" id="529"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem id="517">
                                                        <string key="title">	Right to Left</string>
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="makeTextWritingDirectionRightToLeft:" target="-1" id="530"/>
                                                        </connections>
                                                    </menuItem>
                                                </items>
                                            </menu>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="504"/>
                                        <menuItem title="Show Ruler" id="505">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="toggleRuler:" target="-1" id="520"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Copy Ruler" keyEquivalent="c" id="506">
                                            <modifierMask key="keyEquivalentModifierMask" control="YES" command="YES"/>
                                            <connections>
                                                <action selector="copyRuler:" target="-1" id="522"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Paste Ruler" keyEquivalent="v" id="507">
                                            <modifierMask key="keyEquivalentModifierMask" control="YES" command="YES"/>
                                            <connections>
                                                <action selector="pasteRuler:" target="-1" id="519"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="View" id="295">
                    <menu key="submenu" title="View" id="296">
                        <items>
                            <menuItem title="Show Toolbar" keyEquivalent="t" id="297">
                                <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                <connections>
                                    <action selector="toggleToolbarShown:" target="-1" id="366"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Customize Toolbar…" id="298">
                                <connections>
                                    <action selector="runToolbarCustomizationPalette:" target="-1" id="365"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="Window" id="19">
                    <menu key="submenu" title="Window" systemMenu="window" id="24">
                        <items>
                            <menuItem title="Minimize" keyEquivalent="m" id="23">
                                <connections>
                                    <action selector="performMiniaturize:" target="-1" id="37"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Zoom" id="239">
                                <connections>
                                    <action selector="performZoom:" target="-1" id="240"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="92">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Bring All to Front" id="5">
                                <connections>
                                    <action selector="arrangeInFront:" target="-1" id="39"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="Help" id="490">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <menu key="submenu" title="Help" systemMenu="help" id="491">
                        <items>
                            <menuItem title="Demo Help" keyEquivalent="?" id="492">
                                <connections>
                                    <action selector="showHelp:" target="-1" id="493"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
            </items>
        </menu>
        <window title="Demo" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" frameAutosaveName="DemoWindow" animationBehavior="default" id="371">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES"/>
            <rect key="contentRect" x="335" y="390" width="393" height="129"/>
            <rect key="screenRect" x="0.0" y="0.0" width="1440" height="877"/>
            <view key="contentView" id="372">
                <rect key="frame" x="0.0" y="0.0" width="393" height="129"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <customView id="536" customClass="MASShortcutView">
                        <rect key="frame" x="142" y="90" width="158" height="19"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    </customView>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" id="PG0-jh-Onh">
                        <rect key="frame" x="9" y="92" width="120" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Klávesová zkratka:" id="85u-1A-n7H">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button id="zCi-ki-Uuh">
                        <rect key="frame" x="140" y="63" width="196" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Zapnout klávesovou zkratku" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="Y47-p3-sDa">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <binding destination="rCO-Ve-DT5" name="value" keyPath="values.customShortcutEnabled" id="VjS-3V-VdA"/>
                        </connections>
                    </button>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" id="KnS-ut-phz">
                        <rect key="frame" x="18" y="65" width="111" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Předvolby:" id="cUE-gA-heG">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button id="F4r-KM-wn9">
                        <rect key="frame" x="140" y="43" width="237" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Zapnout vestavěnou zkratku (⌘F2)" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="7gv-jN-44g">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <binding destination="rCO-Ve-DT5" name="value" keyPath="values.hardcodedShortcutEnabled" id="dlZ-si-HeN"/>
                        </connections>
                    </button>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" id="9fB-XS-8pK">
                        <rect key="frame" x="18" y="20" width="111" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Zkratka v akci:" id="Zbz-mV-NDc">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" id="Aso-dH-W8I">
                        <rect key="frame" x="140" y="20" width="211" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="left" placeholderString="stiskněte klávesovou zkratku" id="Ckx-v7-e6x">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" red="0.37647062540054321" green="0.85098046064376831" blue="0.17647059261798859" alpha="1" colorSpace="deviceRGB"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                </subviews>
            </view>
            <point key="canvasLocation" x="6.5" y="248.5"/>
        </window>
        <customObject id="494" customClass="AppDelegate">
            <connections>
                <outlet property="customShortcutView" destination="536" id="aO6-hh-1vm"/>
                <outlet property="feedbackTextField" destination="Aso-dH-W8I" id="hk8-xL-ieC"/>
                <outlet property="window" destination="371" id="532"/>
            </connections>
        </customObject>
        <customObject id="420" customClass="NSFontManager"/>
        <userDefaultsController representsSharedInstance="YES" id="rCO-Ve-DT5"/>
    </objects>
</document>
