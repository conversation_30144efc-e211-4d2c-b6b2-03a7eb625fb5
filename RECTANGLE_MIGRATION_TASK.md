# Rectangle Window Management Migration Task

## Objective

Replace OhSnap's current window management implementation with <PERSON><PERSON><PERSON><PERSON>'s exact approach to ensure identical behavior and compatibility.

## Progress Tracking

### Phase 1: Analysis and Cleanup ✅

- [x] Analyze current OhSnap window management files
- [x] Identify files to delete to avoid conflicts
- [x] Analyze Rectangle's window management structure
- [x] Create migration plan

### Phase 2: File Removal ✅

- [x] Delete conflicting window management files from OhSnap
- [x] Remove any custom window positioning logic
- [x] Clean up imports and references

### Phase 3: Rectangle Integration ✅

- [x] Copy Rectangle's core window management files
- [x] Copy Rectangle's window calculation files
- [x] Copy Rectangle's utility files
- [x] Copy Rectangle's snapping files
- [x] Copy Rectangle's window mover files
- [x] Adapt file structure to OhSnap's organization

### Phase 4: Integration and Adaptation ⏳

- [x] Rename conflicting OhSnap AccessibilityElement to avoid conflicts
- [x] Create Rectangle's Logger adapter for OhSnap's LoggingService
- [x] Create Rectangle's AppDelegate adapter for windowHistory
- [x] Remove remaining conflicting OhSnap files
- [x] Create RectangleWindowSnappingBridge to connect systems
- [x] Update AppDelegate to use Rectangle's WindowManager
- [x] Update WorkspaceService to use RectangleWindowSnappingBridge
- [x] Update ShortcutService to use RectangleWindowSnappingBridge
- [x] Fix AppColorUtility calls in workspace files
- [ ] Remove remaining old OhSnap files causing conflicts
- [ ] Create missing Rectangle types and adapters
- [ ] Resolve remaining compilation errors
- [ ] Update any OhSnap-specific configurations

### Phase 5: Testing and Validation ✅

- [ ] Test basic window positioning
- [ ] Test multi-display support
- [ ] Verify logging works correctly
- [ ] Test edge cases and error handling

## Files to Analyze

### Rectangle Source Files (to copy):

- Rectangle/AccessibilityElement.swift
- Rectangle/WindowAction.swift
- Rectangle/WindowHistory.swift
- Rectangle/WindowManager.swift
- Rectangle/Utilities/CGExtension.swift
- Rectangle/Utilities/CFExtension.swift
- Rectangle/Utilities/WindowUtil.swift
- Rectangle/WindowCalculation/\*.swift (all calculation files)

### OhSnap Files (to remove to avoid conflicts):

#### Core Window Management Files:

- OhSnap/Features/WindowManagement/WindowManager.swift (conflicts with Rectangle's WindowManager)
- OhSnap/Features/WindowManagement/WindowCalculationService.swift
- OhSnap/Features/WindowManagement/WindowSnappingService.swift
- OhSnap/Features/WindowManagement/WindowPositioningService.swift
- OhSnap/Features/WindowManagement/WindowMover.swift
- OhSnap/Features/WindowManagement/SnappingManager.swift
- OhSnap/Features/WindowManagement/ScreenDetectionService.swift

#### Window Calculation Files:

- OhSnap/Features/WindowManagement/WindowCalculation/WindowCalculation.swift
- OhSnap/Features/WindowManagement/WindowCalculation/AlmostMaximizeCalculation.swift
- OhSnap/Features/WindowManagement/WindowCalculation/Calculation.swift
- OhSnap/Features/WindowManagement/WindowCalculation/ChangeSizeCalculation.swift
- OhSnap/Features/WindowManagement/WindowCalculation/LeftRightHalfCalculation.swift
- OhSnap/Features/WindowManagement/WindowCalculation/SpecifiedCalculation.swift
- OhSnap/Features/WindowManagement/WindowCalculation/StandardPositionCalculation.swift
- OhSnap/Features/WindowManagement/WindowCalculation/TopBottomHalfCalculation.swift

#### Supporting Files:

- OhSnap/Features/WindowManagement/AdjacentScreens.swift
- OhSnap/Features/WindowManagement/CGRectExtensions.swift
- OhSnap/Features/WindowManagement/DisplayScaleManager.swift
- OhSnap/Features/WindowManagement/SnapArea.swift
- OhSnap/Features/WindowManagement/SnapAreaModel.swift
- OhSnap/Features/WindowManagement/SubsequentExecutionMode.swift

#### Files to Keep (NOT window management):

- OhSnap/Features/Core/AccessibilityElement.swift (keep - this is core accessibility, not window management)

## Notes

- Keep workspace functionality completely separate
- Use Rectangle's exact coordinate system and calculations
- Maintain OhSnap's logging system integration
- Preserve existing service architecture where possible
