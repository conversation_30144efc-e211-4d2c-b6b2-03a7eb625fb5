// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXBuildFile section */
		0D2CAB131B8332E5005431FC /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 0D2CAB151B8332E5005431FC /* Localizable.strings */; };
		0D2CAB191B8339F4005431FC /* MASLocalization.h in Headers */ = {isa = PBXBuildFile; fileRef = 0D2CAB171B8339F4005431FC /* MASLocalization.h */; };
		0D2CAB1A1B8339F4005431FC /* MASLocalization.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D2CAB181B8339F4005431FC /* MASLocalization.m */; };
		0D2CAB1B1B83409C005431FC /* MainMenu.xib in Resources */ = {isa = PBXBuildFile; fileRef = 0D2CAB1D1B83409C005431FC /* MainMenu.xib */; };
		0D2CAB211B834464005431FC /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 0D2CAB231B834464005431FC /* Localizable.strings */; };
		0D39DCA21A668A4400639145 /* MASHotKeyTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D39DCA11A668A4400639145 /* MASHotKeyTests.m */; };
		0D39DCA41A668E5500639145 /* MASShortcutMonitorTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D39DCA31A668E5500639145 /* MASShortcutMonitorTests.m */; };
		0D827CD71990D4420010B8EF /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0D827CD61990D4420010B8EF /* Cocoa.framework */; };
		0D827D251990D55E0010B8EF /* MASShortcut.h in Headers */ = {isa = PBXBuildFile; fileRef = 0D827D1B1990D55E0010B8EF /* MASShortcut.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0D827D261990D55E0010B8EF /* MASShortcut.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D827D1C1990D55E0010B8EF /* MASShortcut.m */; };
		0D827D2B1990D55E0010B8EF /* MASShortcutView.h in Headers */ = {isa = PBXBuildFile; fileRef = 0D827D211990D55E0010B8EF /* MASShortcutView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0D827D2C1990D55E0010B8EF /* MASShortcutView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D827D221990D55E0010B8EF /* MASShortcutView.m */; };
		0D827D381990D5E70010B8EF /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0D827CD61990D4420010B8EF /* Cocoa.framework */; };
		0D827D6F1990D6110010B8EF /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D827D6A1990D6110010B8EF /* AppDelegate.m */; };
		0D827D711990D6110010B8EF /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D827D6D1990D6110010B8EF /* main.m */; };
		0D827D731990D6590010B8EF /* MASShortcut.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0D827CD31990D4420010B8EF /* MASShortcut.framework */; };
		0D827D751990D6A60010B8EF /* MASShortcut.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = 0D827CD31990D4420010B8EF /* MASShortcut.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, ); }; };
		0D827D771990F81E0010B8EF /* Shortcut.h in Headers */ = {isa = PBXBuildFile; fileRef = 0D827D761990F81E0010B8EF /* Shortcut.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0D827D9419910B740010B8EF /* MASShortcutTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D827D9319910B740010B8EF /* MASShortcutTests.m */; };
		0D827D9519910C1E0010B8EF /* MASShortcut.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0D827CD31990D4420010B8EF /* MASShortcut.framework */; };
		0D827D9719910FF70010B8EF /* MASKeyCodes.h in Headers */ = {isa = PBXBuildFile; fileRef = 0D827D9619910FF70010B8EF /* MASKeyCodes.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0D827D99199110F60010B8EF /* Prefix.pch in Headers */ = {isa = PBXBuildFile; fileRef = 0D827D98199110F60010B8EF /* Prefix.pch */; };
		0D827D9E19911A190010B8EF /* MASShortcutValidator.h in Headers */ = {isa = PBXBuildFile; fileRef = 0D827D9C19911A190010B8EF /* MASShortcutValidator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0D827D9F19911A190010B8EF /* MASShortcutValidator.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D827D9D19911A190010B8EF /* MASShortcutValidator.m */; };
		0D827DA519912D240010B8EF /* MASShortcutMonitor.h in Headers */ = {isa = PBXBuildFile; fileRef = 0D827DA319912D240010B8EF /* MASShortcutMonitor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0D827DAD199132840010B8EF /* MASShortcutBinder.h in Headers */ = {isa = PBXBuildFile; fileRef = 0D827DAB199132840010B8EF /* MASShortcutBinder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0DC2F17619922798003A0131 /* MASHotKey.h in Headers */ = {isa = PBXBuildFile; fileRef = 0DC2F17419922798003A0131 /* MASHotKey.h */; };
		0DC2F17719922798003A0131 /* MASHotKey.m in Sources */ = {isa = PBXBuildFile; fileRef = 0DC2F17519922798003A0131 /* MASHotKey.m */; };
		0DC2F17C199232EA003A0131 /* MASShortcutMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D827DA419912D240010B8EF /* MASShortcutMonitor.m */; };
		0DC2F17D199232F7003A0131 /* MASShortcutBinder.m in Sources */ = {isa = PBXBuildFile; fileRef = 0D827DAC199132840010B8EF /* MASShortcutBinder.m */; };
		0DC2F18919925F8F003A0131 /* MASShortcutBinderTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 0DC2F18819925F8F003A0131 /* MASShortcutBinderTests.m */; };
		0DC2F18D1993708A003A0131 /* MASDictionaryTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = 0DC2F18B1993708A003A0131 /* MASDictionaryTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0DC2F18E1993708A003A0131 /* MASDictionaryTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = 0DC2F18C1993708A003A0131 /* MASDictionaryTransformer.m */; };
		0DC2F190199372B4003A0131 /* MASDictionaryTransformerTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 0DC2F18F199372B4003A0131 /* MASDictionaryTransformerTests.m */; };
		0DC2F19819938EFA003A0131 /* MASShortcutView+Bindings.h in Headers */ = {isa = PBXBuildFile; fileRef = 0DC2F19619938EFA003A0131 /* MASShortcutView+Bindings.h */; settings = {ATTRIBUTES = (Public, ); }; };
		0DC2F19919938EFA003A0131 /* MASShortcutView+Bindings.m in Sources */ = {isa = PBXBuildFile; fileRef = 0DC2F19719938EFA003A0131 /* MASShortcutView+Bindings.m */; };
		50C888F126F8E2FE0086EB9A /* MASShortcutViewButtonCell.h in Headers */ = {isa = PBXBuildFile; fileRef = 50C888EF26F8E2FE0086EB9A /* MASShortcutViewButtonCell.h */; settings = {ATTRIBUTES = (Public, ); }; };
		50C888F226F8E2FE0086EB9A /* MASShortcutViewButtonCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 50C888F026F8E2FE0086EB9A /* MASShortcutViewButtonCell.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		0D827D8E19910AFF0010B8EF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0D827CCA1990D4420010B8EF /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0D827CD21990D4420010B8EF;
			remoteInfo = MASShortcut;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		0D827D741990D6980010B8EF /* Copy Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				0D827D751990D6A60010B8EF /* MASShortcut.framework in Copy Frameworks */,
			);
			name = "Copy Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0D2CAB141B8332E5005431FC /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		0D2CAB161B8332EE005431FC /* cs */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = cs; path = cs.lproj/Localizable.strings; sourceTree = "<group>"; };
		0D2CAB171B8339F4005431FC /* MASLocalization.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASLocalization.h; sourceTree = "<group>"; };
		0D2CAB181B8339F4005431FC /* MASLocalization.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASLocalization.m; sourceTree = "<group>"; };
		0D2CAB1E1B8340A4005431FC /* cs */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = cs; path = cs.lproj/MainMenu.xib; sourceTree = "<group>"; };
		0D2CAB221B834464005431FC /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		0D2CAB241B834467005431FC /* cs */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = cs; path = cs.lproj/Localizable.strings; sourceTree = "<group>"; };
		0D39DCA11A668A4400639145 /* MASHotKeyTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASHotKeyTests.m; sourceTree = "<group>"; };
		0D39DCA31A668E5500639145 /* MASShortcutMonitorTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASShortcutMonitorTests.m; sourceTree = "<group>"; };
		0D58DE521BA165FC0023BFBE /* de */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = de; path = de.lproj/Localizable.strings; sourceTree = "<group>"; };
		0D58DE531BA166170023BFBE /* es */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = es; path = es.lproj/Localizable.strings; sourceTree = "<group>"; };
		0D58DE541BA166270023BFBE /* it */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = it; path = it.lproj/Localizable.strings; sourceTree = "<group>"; };
		0D58DE551BA166390023BFBE /* fr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fr; path = fr.lproj/Localizable.strings; sourceTree = "<group>"; };
		0D58DE561BA166420023BFBE /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja.lproj/Localizable.strings; sourceTree = "<group>"; };
		0D827CD31990D4420010B8EF /* MASShortcut.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = MASShortcut.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		0D827CD61990D4420010B8EF /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = System/Library/Frameworks/Cocoa.framework; sourceTree = SDKROOT; };
		0D827CD91990D4420010B8EF /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		0D827CDA1990D4420010B8EF /* CoreData.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreData.framework; path = System/Library/Frameworks/CoreData.framework; sourceTree = SDKROOT; };
		0D827CDB1990D4420010B8EF /* AppKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppKit.framework; path = System/Library/Frameworks/AppKit.framework; sourceTree = SDKROOT; };
		0D827D1B1990D55E0010B8EF /* MASShortcut.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASShortcut.h; sourceTree = "<group>"; };
		0D827D1C1990D55E0010B8EF /* MASShortcut.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASShortcut.m; sourceTree = "<group>"; };
		0D827D211990D55E0010B8EF /* MASShortcutView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASShortcutView.h; sourceTree = "<group>"; };
		0D827D221990D55E0010B8EF /* MASShortcutView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASShortcutView.m; sourceTree = "<group>"; };
		0D827D2F1990D5640010B8EF /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		0D827D371990D5E70010B8EF /* Demo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Demo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		0D827D691990D6110010B8EF /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		0D827D6A1990D6110010B8EF /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		0D827D6B1990D6110010B8EF /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		0D827D6C1990D6110010B8EF /* Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Prefix.pch; sourceTree = "<group>"; };
		0D827D6D1990D6110010B8EF /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		0D827D761990F81E0010B8EF /* Shortcut.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Shortcut.h; sourceTree = "<group>"; };
		0D827D8319910AFF0010B8EF /* MASShortcutTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MASShortcutTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		0D827D8719910AFF0010B8EF /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		0D827D8D19910AFF0010B8EF /* Prefix.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Prefix.pch; sourceTree = "<group>"; };
		0D827D9319910B740010B8EF /* MASShortcutTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASShortcutTests.m; sourceTree = "<group>"; };
		0D827D9619910FF70010B8EF /* MASKeyCodes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASKeyCodes.h; sourceTree = "<group>"; };
		0D827D98199110F60010B8EF /* Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Prefix.pch; sourceTree = "<group>"; };
		0D827D9C19911A190010B8EF /* MASShortcutValidator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASShortcutValidator.h; sourceTree = "<group>"; };
		0D827D9D19911A190010B8EF /* MASShortcutValidator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASShortcutValidator.m; sourceTree = "<group>"; };
		0D827DA319912D240010B8EF /* MASShortcutMonitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASShortcutMonitor.h; sourceTree = "<group>"; };
		0D827DA419912D240010B8EF /* MASShortcutMonitor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASShortcutMonitor.m; sourceTree = "<group>"; };
		0D827DAB199132840010B8EF /* MASShortcutBinder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASShortcutBinder.h; sourceTree = "<group>"; };
		0D827DAC199132840010B8EF /* MASShortcutBinder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASShortcutBinder.m; sourceTree = "<group>"; };
		0DC2F17419922798003A0131 /* MASHotKey.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASHotKey.h; sourceTree = "<group>"; };
		0DC2F17519922798003A0131 /* MASHotKey.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASHotKey.m; sourceTree = "<group>"; };
		0DC2F18819925F8F003A0131 /* MASShortcutBinderTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASShortcutBinderTests.m; sourceTree = "<group>"; };
		0DC2F18B1993708A003A0131 /* MASDictionaryTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASDictionaryTransformer.h; sourceTree = "<group>"; };
		0DC2F18C1993708A003A0131 /* MASDictionaryTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASDictionaryTransformer.m; sourceTree = "<group>"; };
		0DC2F18F199372B4003A0131 /* MASDictionaryTransformerTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASDictionaryTransformerTests.m; sourceTree = "<group>"; };
		0DC2F19619938EFA003A0131 /* MASShortcutView+Bindings.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "MASShortcutView+Bindings.h"; sourceTree = "<group>"; };
		0DC2F19719938EFA003A0131 /* MASShortcutView+Bindings.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "MASShortcutView+Bindings.m"; sourceTree = "<group>"; };
		0DEDAA021C6BB479001605F5 /* de */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = de; path = de.lproj/Localizable.strings; sourceTree = "<group>"; };
		50C888EF26F8E2FE0086EB9A /* MASShortcutViewButtonCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASShortcutViewButtonCell.h; sourceTree = "<group>"; };
		50C888F026F8E2FE0086EB9A /* MASShortcutViewButtonCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASShortcutViewButtonCell.m; sourceTree = "<group>"; };
		57B25C2D1E78E06D0061A9EC /* pt */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = pt; path = pt.lproj/Localizable.strings; sourceTree = "<group>"; };
		57B25C2E1E78E06D0061A9EC /* pt */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = pt; path = pt.lproj/Localizable.strings; sourceTree = "<group>"; };
		6EA6034E1CBF822000A3ED9C /* pl */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = pl; path = pl.lproj/Localizable.strings; sourceTree = "<group>"; };
		6EA6034F1CBF822800A3ED9C /* ko */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ko; path = ko.lproj/Localizable.strings; sourceTree = "<group>"; };
		6EA603501CBF822D00A3ED9C /* ru */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ru; path = ru.lproj/Localizable.strings; sourceTree = "<group>"; };
		6EA603511CBF823600A3ED9C /* nl */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = nl; path = nl.lproj/Localizable.strings; sourceTree = "<group>"; };
		76A0597D1C51DC940014B271 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		76A0597E1C51DC9F0014B271 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/Localizable.strings"; sourceTree = "<group>"; };
		EAFFDC811AACFF3300F38834 /* MASShortcut.modulemap */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = "sourcecode.module-map"; path = MASShortcut.modulemap; sourceTree = "<group>"; };
		ED840EAE25E66B37003F76F7 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/MainMenu.xib; sourceTree = "<group>"; };
		ED8737791BCE459800BB1716 /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja.lproj/Localizable.strings; sourceTree = "<group>"; };
		FDFF016F20CB2FB400CC88F3 /* sv */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = sv; path = sv.lproj/Localizable.strings; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0D827CCF1990D4420010B8EF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0D827CD71990D4420010B8EF /* Cocoa.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0D827D341990D5E70010B8EF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0D827D381990D5E70010B8EF /* Cocoa.framework in Frameworks */,
				0D827D731990D6590010B8EF /* MASShortcut.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0D827D8019910AFF0010B8EF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0D827D9519910C1E0010B8EF /* MASShortcut.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0D827CC91990D4420010B8EF = {
			isa = PBXGroup;
			children = (
				0DBA0B9E22A4FAC5008685CD /* Framework */,
				0D827D8519910AFF0010B8EF /* Test Support */,
				0DBA0B9C22A4F88C008685CD /* Resources */,
				0D827D681990D6110010B8EF /* Demo */,
				0D827CD51990D4420010B8EF /* Frameworks */,
				0D827CD41990D4420010B8EF /* Products */,
			);
			sourceTree = "<group>";
		};
		0D827CD41990D4420010B8EF /* Products */ = {
			isa = PBXGroup;
			children = (
				0D827CD31990D4420010B8EF /* MASShortcut.framework */,
				0D827D371990D5E70010B8EF /* Demo.app */,
				0D827D8319910AFF0010B8EF /* MASShortcutTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		0D827CD51990D4420010B8EF /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				0D827CD61990D4420010B8EF /* Cocoa.framework */,
				0D827CD91990D4420010B8EF /* Foundation.framework */,
				0D827CDA1990D4420010B8EF /* CoreData.framework */,
				0D827CDB1990D4420010B8EF /* AppKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		0D827D681990D6110010B8EF /* Demo */ = {
			isa = PBXGroup;
			children = (
				0D2CAB231B834464005431FC /* Localizable.strings */,
				0D827D691990D6110010B8EF /* AppDelegate.h */,
				0D827D6A1990D6110010B8EF /* AppDelegate.m */,
				0D2CAB1D1B83409C005431FC /* MainMenu.xib */,
				0D827D6B1990D6110010B8EF /* Info.plist */,
				0D827D6C1990D6110010B8EF /* Prefix.pch */,
				0D827D6D1990D6110010B8EF /* main.m */,
			);
			path = Demo;
			sourceTree = "<group>";
		};
		0D827D8519910AFF0010B8EF /* Test Support */ = {
			isa = PBXGroup;
			children = (
				0D827D8719910AFF0010B8EF /* Info.plist */,
				0D827D8D19910AFF0010B8EF /* Prefix.pch */,
			);
			name = "Test Support";
			path = Tests;
			sourceTree = "<group>";
		};
		0DBA0B9C22A4F88C008685CD /* Resources */ = {
			isa = PBXGroup;
			children = (
				0D2CAB151B8332E5005431FC /* Localizable.strings */,
			);
			name = Resources;
			path = Framework/Resources;
			sourceTree = "<group>";
		};
		0DBA0B9E22A4FAC5008685CD /* Framework */ = {
			isa = PBXGroup;
			children = (
				0DBA0B9F22A4FACE008685CD /* Model */,
				0DBA0BA022A4FAE0008685CD /* Monitoring */,
				0DBA0BA122A4FAF6008685CD /* User Defaults Storage */,
				0DBA0BA222A4FB15008685CD /* UI */,
				0D827D2F1990D5640010B8EF /* Info.plist */,
				EAFFDC811AACFF3300F38834 /* MASShortcut.modulemap */,
				0D827D98199110F60010B8EF /* Prefix.pch */,
				0D827D761990F81E0010B8EF /* Shortcut.h */,
			);
			path = Framework;
			sourceTree = "<group>";
		};
		0DBA0B9F22A4FACE008685CD /* Model */ = {
			isa = PBXGroup;
			children = (
				0D827D9619910FF70010B8EF /* MASKeyCodes.h */,
				0D827D1B1990D55E0010B8EF /* MASShortcut.h */,
				0D827D1C1990D55E0010B8EF /* MASShortcut.m */,
				0D827D9319910B740010B8EF /* MASShortcutTests.m */,
				0D827D9C19911A190010B8EF /* MASShortcutValidator.h */,
				0D827D9D19911A190010B8EF /* MASShortcutValidator.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		0DBA0BA022A4FAE0008685CD /* Monitoring */ = {
			isa = PBXGroup;
			children = (
				0DC2F17419922798003A0131 /* MASHotKey.h */,
				0DC2F17519922798003A0131 /* MASHotKey.m */,
				0D39DCA11A668A4400639145 /* MASHotKeyTests.m */,
				0D827DA319912D240010B8EF /* MASShortcutMonitor.h */,
				0D827DA419912D240010B8EF /* MASShortcutMonitor.m */,
				0D39DCA31A668E5500639145 /* MASShortcutMonitorTests.m */,
			);
			path = Monitoring;
			sourceTree = "<group>";
		};
		0DBA0BA122A4FAF6008685CD /* User Defaults Storage */ = {
			isa = PBXGroup;
			children = (
				0DC2F18B1993708A003A0131 /* MASDictionaryTransformer.h */,
				0DC2F18C1993708A003A0131 /* MASDictionaryTransformer.m */,
				0DC2F18F199372B4003A0131 /* MASDictionaryTransformerTests.m */,
				0D827DAB199132840010B8EF /* MASShortcutBinder.h */,
				0D827DAC199132840010B8EF /* MASShortcutBinder.m */,
				0DC2F18819925F8F003A0131 /* MASShortcutBinderTests.m */,
			);
			path = "User Defaults Storage";
			sourceTree = "<group>";
		};
		0DBA0BA222A4FB15008685CD /* UI */ = {
			isa = PBXGroup;
			children = (
				0D2CAB171B8339F4005431FC /* MASLocalization.h */,
				0D2CAB181B8339F4005431FC /* MASLocalization.m */,
				0D827D211990D55E0010B8EF /* MASShortcutView.h */,
				0D827D221990D55E0010B8EF /* MASShortcutView.m */,
				50C888EF26F8E2FE0086EB9A /* MASShortcutViewButtonCell.h */,
				50C888F026F8E2FE0086EB9A /* MASShortcutViewButtonCell.m */,
				0DC2F19619938EFA003A0131 /* MASShortcutView+Bindings.h */,
				0DC2F19719938EFA003A0131 /* MASShortcutView+Bindings.m */,
			);
			path = UI;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		0D827CD01990D4420010B8EF /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0D827D9719910FF70010B8EF /* MASKeyCodes.h in Headers */,
				0D827D2B1990D55E0010B8EF /* MASShortcutView.h in Headers */,
				0D827D99199110F60010B8EF /* Prefix.pch in Headers */,
				0D827D251990D55E0010B8EF /* MASShortcut.h in Headers */,
				0DC2F19819938EFA003A0131 /* MASShortcutView+Bindings.h in Headers */,
				0D827D9E19911A190010B8EF /* MASShortcutValidator.h in Headers */,
				0DC2F18D1993708A003A0131 /* MASDictionaryTransformer.h in Headers */,
				0D827DA519912D240010B8EF /* MASShortcutMonitor.h in Headers */,
				0D827DAD199132840010B8EF /* MASShortcutBinder.h in Headers */,
				0D2CAB191B8339F4005431FC /* MASLocalization.h in Headers */,
				50C888F126F8E2FE0086EB9A /* MASShortcutViewButtonCell.h in Headers */,
				0DC2F17619922798003A0131 /* MASHotKey.h in Headers */,
				0D827D771990F81E0010B8EF /* Shortcut.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		0D827CD21990D4420010B8EF /* MASShortcut */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0D827CFB1990D4420010B8EF /* Build configuration list for PBXNativeTarget "MASShortcut" */;
			buildPhases = (
				0D827CCE1990D4420010B8EF /* Sources */,
				0D827CCF1990D4420010B8EF /* Frameworks */,
				0D827CD01990D4420010B8EF /* Headers */,
				0D827CD11990D4420010B8EF /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MASShortcut;
			productName = MASShortcut;
			productReference = 0D827CD31990D4420010B8EF /* MASShortcut.framework */;
			productType = "com.apple.product-type.framework";
		};
		0D827D361990D5E70010B8EF /* Demo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0D827D661990D5E70010B8EF /* Build configuration list for PBXNativeTarget "Demo" */;
			buildPhases = (
				0D827D331990D5E70010B8EF /* Sources */,
				0D827D341990D5E70010B8EF /* Frameworks */,
				0D827D351990D5E70010B8EF /* Resources */,
				0D827D741990D6980010B8EF /* Copy Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Demo;
			productName = Demo;
			productReference = 0D827D371990D5E70010B8EF /* Demo.app */;
			productType = "com.apple.product-type.application";
		};
		0D827D8219910AFF0010B8EF /* MASShortcutTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0D827D9219910AFF0010B8EF /* Build configuration list for PBXNativeTarget "MASShortcutTests" */;
			buildPhases = (
				0D827D7F19910AFF0010B8EF /* Sources */,
				0D827D8019910AFF0010B8EF /* Frameworks */,
				0D827D8119910AFF0010B8EF /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				0D827D8F19910AFF0010B8EF /* PBXTargetDependency */,
			);
			name = MASShortcutTests;
			productName = Tests;
			productReference = 0D827D8319910AFF0010B8EF /* MASShortcutTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0D827CCA1990D4420010B8EF /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1250;
				ORGANIZATIONNAME = "Vadim Shpakovski";
				TargetAttributes = {
					0D827D8219910AFF0010B8EF = {
						TestTargetID = 0D827CD21990D4420010B8EF;
					};
				};
			};
			buildConfigurationList = 0D827CCD1990D4420010B8EF /* Build configuration list for PBXProject "MASShortcut" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				cs,
				de,
				es,
				it,
				fr,
				ja,
				"zh-Hans",
				"zh-Hant",
				pl,
				ko,
				ru,
				nl,
				pt,
				sv,
				Base,
			);
			mainGroup = 0D827CC91990D4420010B8EF;
			productRefGroup = 0D827CD41990D4420010B8EF /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0D827CD21990D4420010B8EF /* MASShortcut */,
				0D827D8219910AFF0010B8EF /* MASShortcutTests */,
				0D827D361990D5E70010B8EF /* Demo */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0D827CD11990D4420010B8EF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0D2CAB131B8332E5005431FC /* Localizable.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0D827D351990D5E70010B8EF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0D2CAB1B1B83409C005431FC /* MainMenu.xib in Resources */,
				0D2CAB211B834464005431FC /* Localizable.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0D827D8119910AFF0010B8EF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0D827CCE1990D4420010B8EF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0DC2F17719922798003A0131 /* MASHotKey.m in Sources */,
				0D827D9F19911A190010B8EF /* MASShortcutValidator.m in Sources */,
				0DC2F17C199232EA003A0131 /* MASShortcutMonitor.m in Sources */,
				0D827D2C1990D55E0010B8EF /* MASShortcutView.m in Sources */,
				0D827D261990D55E0010B8EF /* MASShortcut.m in Sources */,
				0DC2F18E1993708A003A0131 /* MASDictionaryTransformer.m in Sources */,
				0D2CAB1A1B8339F4005431FC /* MASLocalization.m in Sources */,
				0DC2F19919938EFA003A0131 /* MASShortcutView+Bindings.m in Sources */,
				50C888F226F8E2FE0086EB9A /* MASShortcutViewButtonCell.m in Sources */,
				0DC2F17D199232F7003A0131 /* MASShortcutBinder.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0D827D331990D5E70010B8EF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0D827D711990D6110010B8EF /* main.m in Sources */,
				0D827D6F1990D6110010B8EF /* AppDelegate.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0D827D7F19910AFF0010B8EF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0DC2F190199372B4003A0131 /* MASDictionaryTransformerTests.m in Sources */,
				0D827D9419910B740010B8EF /* MASShortcutTests.m in Sources */,
				0DC2F18919925F8F003A0131 /* MASShortcutBinderTests.m in Sources */,
				0D39DCA21A668A4400639145 /* MASHotKeyTests.m in Sources */,
				0D39DCA41A668E5500639145 /* MASShortcutMonitorTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0D827D8F19910AFF0010B8EF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0D827CD21990D4420010B8EF /* MASShortcut */;
			targetProxy = 0D827D8E19910AFF0010B8EF /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		0D2CAB151B8332E5005431FC /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				0D2CAB141B8332E5005431FC /* en */,
				0D2CAB161B8332EE005431FC /* cs */,
				0D58DE521BA165FC0023BFBE /* de */,
				0D58DE531BA166170023BFBE /* es */,
				0D58DE541BA166270023BFBE /* it */,
				0D58DE551BA166390023BFBE /* fr */,
				0D58DE561BA166420023BFBE /* ja */,
				76A0597D1C51DC940014B271 /* zh-Hans */,
				76A0597E1C51DC9F0014B271 /* zh-Hant */,
				6EA6034E1CBF822000A3ED9C /* pl */,
				6EA6034F1CBF822800A3ED9C /* ko */,
				6EA603501CBF822D00A3ED9C /* ru */,
				6EA603511CBF823600A3ED9C /* nl */,
				57B25C2D1E78E06D0061A9EC /* pt */,
				FDFF016F20CB2FB400CC88F3 /* sv */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		0D2CAB1D1B83409C005431FC /* MainMenu.xib */ = {
			isa = PBXVariantGroup;
			children = (
				0D2CAB1E1B8340A4005431FC /* cs */,
				ED840EAE25E66B37003F76F7 /* Base */,
			);
			name = MainMenu.xib;
			sourceTree = "<group>";
		};
		0D2CAB231B834464005431FC /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				0D2CAB221B834464005431FC /* en */,
				0D2CAB241B834467005431FC /* cs */,
				ED8737791BCE459800BB1716 /* ja */,
				0DEDAA021C6BB479001605F5 /* de */,
				57B25C2E1E78E06D0061A9EC /* pt */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		0D827CF91990D4420010B8EF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		0D827CFA1990D4420010B8EF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_TREAT_WARNINGS_AS_ERRORS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.10;
				SDKROOT = macosx;
			};
			name = Release;
		};
		0D827CFC1990D4420010B8EF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = YES;
				COMBINE_HIDPI_IMAGES = YES;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 2.4.0;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_VERSION = A;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Framework/Prefix.pch;
				INFOPLIST_FILE = Framework/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = Framework/MASShortcut.modulemap;
				PRODUCT_BUNDLE_IDENTIFIER = "com.github.shpakovski.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				USER_HEADER_SEARCH_PATHS = "\"$SRCROOT/Framework/include\"";
				USE_HEADERMAP = NO;
				WRAPPER_EXTENSION = framework;
			};
			name = Debug;
		};
		0D827CFD1990D4420010B8EF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = YES;
				COMBINE_HIDPI_IMAGES = YES;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 2.4.0;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_VERSION = A;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Framework/Prefix.pch;
				INFOPLIST_FILE = Framework/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = Framework/MASShortcut.modulemap;
				PRODUCT_BUNDLE_IDENTIFIER = "com.github.shpakovski.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				USER_HEADER_SEARCH_PATHS = "\"$SRCROOT/Framework/include\"";
				USE_HEADERMAP = NO;
				WRAPPER_EXTENSION = framework;
			};
			name = Release;
		};
		0D827D621990D5E70010B8EF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Demo/Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = Demo/Info.plist;
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				PRODUCT_BUNDLE_IDENTIFIER = "com.shpakovski.mac.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				WRAPPER_EXTENSION = app;
			};
			name = Debug;
		};
		0D827D631990D5E70010B8EF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Demo/Prefix.pch;
				INFOPLIST_FILE = Demo/Info.plist;
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				PRODUCT_BUNDLE_IDENTIFIER = "com.shpakovski.mac.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				WRAPPER_EXTENSION = app;
			};
			name = Release;
		};
		0D827D9019910AFF0010B8EF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(DEVELOPER_FRAMEWORKS_DIR)",
					"$(inherited)",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Tests/Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = Tests/Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = "com.github.shpakovski.MASShortcut.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				WRAPPER_EXTENSION = xctest;
			};
			name = Debug;
		};
		0D827D9119910AFF0010B8EF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(DEVELOPER_FRAMEWORKS_DIR)",
					"$(inherited)",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Tests/Prefix.pch;
				INFOPLIST_FILE = Tests/Info.plist;
				PRODUCT_BUNDLE_IDENTIFIER = "com.github.shpakovski.MASShortcut.${PRODUCT_NAME:rfc1034identifier}";
				PRODUCT_NAME = "$(TARGET_NAME)";
				WRAPPER_EXTENSION = xctest;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0D827CCD1990D4420010B8EF /* Build configuration list for PBXProject "MASShortcut" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0D827CF91990D4420010B8EF /* Debug */,
				0D827CFA1990D4420010B8EF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0D827CFB1990D4420010B8EF /* Build configuration list for PBXNativeTarget "MASShortcut" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0D827CFC1990D4420010B8EF /* Debug */,
				0D827CFD1990D4420010B8EF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0D827D661990D5E70010B8EF /* Build configuration list for PBXNativeTarget "Demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0D827D621990D5E70010B8EF /* Debug */,
				0D827D631990D5E70010B8EF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0D827D9219910AFF0010B8EF /* Build configuration list for PBXNativeTarget "MASShortcutTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0D827D9019910AFF0010B8EF /* Debug */,
				0D827D9119910AFF0010B8EF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0D827CCA1990D4420010B8EF /* Project object */;
}
