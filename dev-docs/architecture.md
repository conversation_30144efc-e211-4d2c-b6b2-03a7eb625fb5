# Architecture

## Definitions

**SPM.** Swift package manager and Swift build tool. In other words, `swift` CLI tool

## High level project infrastructure overview

- `../Sources`.
  The majority of AeroSpace source code. Managed by SPM `../Package.swift`
- `../Sources/AppBundle/`.
  AeroSpace.app server. Technically, it's a SPM library that is exposed to `xcode-app-bundle-launcher`
- `../Sources/Cli/`.
  CLI client. CLI client is built purely using SPM, no Xcode involved (phew!)
- `../Sources/Common/`.
  Shared code between server and client. Mainly command line args parsing and util functions are shared.
- `../xcode-app-bundle-launcher/`.
  Very small technical directory that defines entry point for Xcode project.
  Xcode projects are ugly and hard to manage without using the Xcode itself.
  And Swift LSP doesn't support Xcode projects, it only support SPM projects.
  Unfortunately, SPM doesn't allow building macOS apps (aka "App Bundle").
  SPM is only capable of defining libraries and building CLI apps.
  All code is pushed as much as possible to SPM "library" located in `../Sources/`.
  The Xcode project model is located in `../AeroSpace.xcodeproj/` and generated by `../project.yml` "skeleton"
- `../Sources/AppBundleTests/`.
  Tests
- `../docs/`.
  Documentation sources for site and man pages in Asciidoc format https://asciidoc.org/

## client/server interaction

`aerospace` CLI binary is client. `AeroSpace.app` is server. Client and server talk to each other via predefined UNIX file.

Each time you run a CLI command:
1. Args are parsed by the client, args parsing errors are reporeted if any. Help is shown if `-h`/`--help` is passed.
1. If args are parsed successufly, the args are send to the server
1. Server parses the args once again, and runs the command
1. Server returns stdout, stderr, and exit code to the client
1. Client shows stdout, stderr, and ends the process with the requested exit code

## Commands subsystem

todo

../Sources/AppBundle/command/
../Sources/Common/cmdArgs/

Command checklist:
- [ ] Documentation in `../docs/aerospace-*` and `../docs/commands.adoc`
  - [ ] Check that site looks alright `./.site/commands.html`
  - [ ] Check that man page looks alright `./.man`
- [ ] Do `--window-id` and/or `--workspace` flags make sense for the command?
- [ ] Shell completion `../grammar/commands-bnf-grammar.txt`

## TOML Config parse subsystem

todo

../Sources/AppBundle/config/

## Tree Model subsystem

todo

../Sources/AppBundle/tree/

## Layout subsystem

todo

../Sources/AppBundle/layout/
