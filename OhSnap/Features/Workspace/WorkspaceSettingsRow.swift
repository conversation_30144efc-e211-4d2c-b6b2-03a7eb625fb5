import AppKit
import Carbon.HIToolbox
import Foundation
import KeyboardShortcuts
import MA<PERSON>hor<PERSON>cut
import SwiftUI

struct WorkspaceSettingsRow: View {
    let workspace: Workspace
    let onSave: (String) -> Void
    let onDelete: () -> Void

    @EnvironmentObject var workspaceService: WorkspaceService
    @State private var isEditing = false
    @State private var editedName: String
    @FocusState private var isNameFieldFocused: Bool
    @State private var refreshTrigger = UUID()

    // Alert state
    @State private var showingDeleteAlert = false

    // State for conflict detection
    @State private var conflictingShortcut: String? = nil
    @State private var showingConflictAlert = false

    // Logger
    private let logger = LoggingService.shared
    private let serviceName = "WorkspaceSettingsRow"

    init(
        workspace: Workspace,
        onSave: @escaping (String) -> Void,
        onDelete: @escaping () -> Void
    ) {
        self.workspace = workspace
        self.onSave = onSave
        self.onDelete = onDelete
        _editedName = State(initialValue: workspace.name)
    }

    // Check for conflicts with other shortcuts
    private func checkForConflicts(shortcut: KeyboardShortcuts.Shortcut, showAlert: Bool = true) {
        // If "Allow any keyboard shortcut" is enabled, bypass validation
        if DefaultsManager.shared.allowAnyShortcut {
            // Clear the conflict state
            conflictingShortcut = nil
            return
        }

        // Convert to our Shortcut type
        guard let wrappedShortcut = ShortcutConverter.fromKeyboardShortcut(shortcut) else {
            return
        }

        // Use our wrapper to validate the shortcut
        let result = KeyboardShortcutsBridge.shared.validateShortcut(wrappedShortcut)

        if !result.isValid {
            // Set the conflicting shortcut name
            conflictingShortcut = result.conflictingApplication ?? "another shortcut"

            logger.debug(
                "Shortcut conflict detected for workspace '\(workspace.name)': \(conflictingShortcut ?? "unknown")",
                service: serviceName,
                category: .shortcuts
            )

            // Show an alert if requested
            if showAlert {
                showingConflictAlert = true
            }
        } else {
            // Clear the conflict state
            conflictingShortcut = nil
        }
    }

    var body: some View {
        VStack(spacing: 0) {
            HStack {
                // Name section
                if isEditing {
                    TextField("Workspace Name", text: $editedName)
                        .textFieldStyle(.roundedBorder)
                        .frame(minWidth: 150, idealWidth: 200, maxWidth: .infinity)
                        .focused($isNameFieldFocused)
                        .onSubmit {
                            if !editedName.isEmpty {
                                onSave(editedName)
                                isEditing = false
                            }
                        }
                        .onAppear {
                            isNameFieldFocused = true
                        }
                } else {
                    Text(workspace.name)
                        .frame(
                            minWidth: 150, idealWidth: 200, maxWidth: .infinity, alignment: .leading
                        )
                        .onTapGesture(count: 2) {
                            isEditing = true
                        }
                }

                // Shortcut section - Use KeyboardShortcuts.Recorder
                if let id = workspace.id {
                    let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)

                    // Create the recorder with onChange handler
                    KeyboardShortcuts.Recorder(
                        for: shortcutName,
                        onChange: { newShortcut in
                            logger.debug(
                                "Shortcut changed for workspace '\(workspace.name)'",
                                service: serviceName,
                                category: .shortcuts
                            )

                            // Check for conflicts if a new shortcut was set
                            if let shortcut = newShortcut {
                                // Validate the shortcut
                                checkForConflicts(shortcut: shortcut)
                            } else {
                                // Clear conflict state if shortcut was removed
                                conflictingShortcut = nil
                            }

                            // Synchronize the shortcut with WorkspaceService
                            if let shortcut = newShortcut, let key = shortcut.key {
                                // Convert to our format for storage
                                let keyCode = UInt16(key.rawValue)
                                let modifiers = shortcut.modifiers.rawValue

                                // Update the workspace in the service
                                workspaceService.updateWorkspaceShortcut(
                                    id: id,
                                    keyCode: keyCode,
                                    modifiers: modifiers
                                )

                                // Force synchronize to ensure changes are written immediately
                                UserDefaults.standard.synchronize()

                                logger.debug(
                                    "Updated workspace shortcut - Key code: \(keyCode), Modifiers: \(modifiers)",
                                    service: serviceName,
                                    category: .shortcuts
                                )
                            } else if newShortcut == nil {
                                // If the shortcut was removed, update the workspace to remove the shortcut
                                workspaceService.updateWorkspaceShortcut(
                                    id: id,
                                    keyCode: 0,
                                    modifiers: 0
                                )

                                // Force synchronize to ensure changes are written immediately
                                UserDefaults.standard.synchronize()

                                logger.debug(
                                    "Cleared workspace shortcut",
                                    service: serviceName,
                                    category: .shortcuts
                                )
                            }

                            // Post notification to refresh menu
                            NotificationCenter.default.post(
                                name: Notification.Name("RefreshStatusMenu"), object: nil)
                        }
                    )
                    .frame(width: 150)
                    .disabled(isEditing)
                } else {
                    Text("No ID")
                        .foregroundColor(.secondary)
                        .frame(width: 150)
                }

                // Action buttons
                if isEditing {
                    Button("Save") {
                        if !editedName.isEmpty {
                            onSave(editedName)
                            isEditing = false
                        }
                    }
                    .keyboardShortcut(.return, modifiers: [])

                    Button("Cancel") {
                        editedName = workspace.name
                        isEditing = false
                    }
                    .keyboardShortcut(.escape, modifiers: [])
                } else {
                    // Menu button (3 vertical dots)
                    Menu {
                        Button {
                            workspaceService.triggerRestoreWorkspace(workspace: workspace)
                        } label: {
                            Label("Restore Workspace", systemImage: "arrow.clockwise")
                        }

                        Divider()

                        Button {
                            // Open edit window
                            openEditWindow(for: workspace)
                        } label: {
                            Label("Edit Workspace", systemImage: "pencil")
                        }

                        Button {
                            showingDeleteAlert = true
                        } label: {
                            Label("Delete Workspace", systemImage: "trash")
                        }
                    } label: {
                        ZStack {
                            Rectangle()
                                .fill(Color.gray.opacity(0.1))
                                .frame(width: 30, height: 30)
                                .cornerRadius(4)

                            Text("⋮")  // Vertical ellipsis character
                                .font(.system(size: 18, weight: .bold))
                                .foregroundColor(.secondary)
                        }
                    }
                    .menuStyle(.borderlessButton)
                    .menuIndicator(.hidden)
                    .fixedSize()  // This ensures the menu only activates when clicking on the icon
                }
            }
            .ohSnapRowStyle()

            // Show warning if there's a conflict
            if let conflictName = conflictingShortcut {
                Text("⚠️ This shortcut conflicts with \(conflictName)")
                    .ohSnapCaptionStyle()
                    .foregroundColor(.orange)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 4)
            }
        }
        .onAppear {
            // Observe shortcut changes
            NotificationCenter.default.addObserver(
                forName: Notification.Name("KeyboardShortcuts_shortcutByNameDidChange"),
                object: nil,
                queue: .main
            ) { notification in
                guard let id = workspace.id,
                    let nameObj = notification.userInfo?["name"] as? KeyboardShortcuts.Name,
                    nameObj == KeyboardShortcuts.Name.workspaceShortcut(for: id)
                else {
                    return
                }

                // Force a refresh when this shortcut changes
                refreshTrigger = UUID()

                // Check for conflicts with the new shortcut
                if let shortcut = KeyboardShortcuts.getShortcut(for: nameObj) {
                    // Validate the shortcut but don't show alert for notification updates
                    checkForConflicts(shortcut: shortcut, showAlert: false)

                    logger.debug(
                        "Validated shortcut after change notification for workspace '\(workspace.name)'",
                        service: serviceName,
                        category: .shortcuts
                    )
                } else {
                    // Clear conflict state if shortcut was removed
                    conflictingShortcut = nil
                }
            }

            // Check if the current shortcut has conflicts
            if let id = workspace.id {
                let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)
                if let shortcut = KeyboardShortcuts.getShortcut(for: shortcutName) {
                    // Validate the shortcut but don't show alert on initial load
                    checkForConflicts(shortcut: shortcut, showAlert: false)

                    logger.debug(
                        "Validated existing shortcut for workspace '\(workspace.name)'",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
            }
        }
        .contextMenu {
            Button {
                workspaceService.triggerRestoreWorkspace(workspace: workspace)
            } label: {
                Label("Restore Workspace", systemImage: "arrow.clockwise")
            }

            Button {
                // Open edit window
                openEditWindow(for: workspace)
            } label: {
                Label("Edit Workspace", systemImage: "pencil")
            }

            Button {
                showingDeleteAlert = true
            } label: {
                Label("Delete Workspace", systemImage: "trash")
            }
        }
        .alert("Delete Workspace", isPresented: $showingDeleteAlert) {
            Button("Delete", role: .destructive) {
                onDelete()
            }
            Button("Cancel", role: .cancel) {}
        } message: {
            Text("Are you sure you want to delete '\(workspace.name)'?")
        }
        .alert("Shortcut Conflict", isPresented: $showingConflictAlert) {
            Button("OK", role: .cancel) {
                // Reset the shortcut if there's a conflict
                if let id = workspace.id {
                    let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)
                    KeyboardShortcuts.disable(shortcutName)

                    // Update the workspace in the service
                    workspaceService.updateWorkspaceShortcut(
                        id: id,
                        keyCode: 0,
                        modifiers: 0
                    )

                    logger.debug(
                        "Cleared conflicting shortcut for workspace '\(workspace.name)'",
                        service: serviceName,
                        category: .shortcuts
                    )
                }
            }
        } message: {
            if let conflictingShortcut = conflictingShortcut {
                Text(
                    "This keyboard shortcut cannot be used as it's already used by \(conflictingShortcut)."
                )
            } else {
                Text("This keyboard shortcut cannot be used as it conflicts with another shortcut.")
            }
        }
    }

    // Direct method to open edit window
    func openEditWindow(for workspace: Workspace) {
        print(
            "[WorkspaceSettingsRow] openEditWindow called for workspace: \(workspace.name)")

        // Create the window directly
        let editWorkspaceView = EditWorkspaceView(workspace: workspace)
            .environmentObject(workspaceService)

        let editWorkspaceWindow = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 600, height: 700),
            styleMask: [.titled, .closable, .miniaturizable, .resizable, .fullSizeContentView],
            backing: .buffered, defer: false
        )

        let hostingView = NSHostingView(rootView: editWorkspaceView)
        editWorkspaceWindow.contentView = hostingView
        editWorkspaceWindow.title = "Edit Workspace"
        editWorkspaceWindow.isReleasedWhenClosed = true
        editWorkspaceWindow.level = .floating  // Ensure window appears above other apps

        // Use intelligent positioning instead of simple center()
        WindowPositioningService.shared.positionWindow(
            editWorkspaceWindow,
            preferredSize: NSSize(width: 600, height: 700),
            context: .settingsRow
        )

        let windowController = NSWindowController(window: editWorkspaceWindow)
        windowController.showWindow(nil)

        editWorkspaceWindow.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)

        print("[WorkspaceSettingsRow] Edit window should now be visible")
    }
}
