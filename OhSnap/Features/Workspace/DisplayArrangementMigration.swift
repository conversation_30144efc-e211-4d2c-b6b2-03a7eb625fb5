import CoreGraphics
import Foundation

// MARK: - Display Arrangement Migration System

/// Represents the type of display arrangement
enum DisplayArrangementType: String, Codable {
    case single = "Single"
    case horizontal = "Horizontal"
    case vertical = "Vertical"
    case mixed = "Mixed"
}

/// Represents a mapping between displays in different arrangements
struct DisplayMapping {
    let sourceDisplayID: CGDirectDisplayID
    let targetDisplayID: CGDirectDisplayID
    let transformationMatrix: CGAffineTransform
    let confidence: Float  // 0.0 to 1.0, how confident we are in this mapping
}

/// Comprehensive analysis of display arrangement changes
struct DisplayArrangementAnalysis {
    let oldArrangement: DisplayArrangementInfo
    let newArrangement: DisplayArrangementInfo
    let oldType: DisplayArrangementType
    let newType: DisplayArrangementType
    let changeType: ArrangementChangeType
    let displayMappings: [DisplayMapping]
    let addedDisplays: [CGDirectDisplayID]
    let removedDisplays: [CGDirectDisplayID]
    let migrationStrategy: MigrationStrategy
    let confidence: Float  // Overall confidence in the migration
}

/// Types of arrangement changes
enum ArrangementChangeType: String, Codable {
    case noChange = "No Change"
    case typeChange = "Type Change"  // horizontal ↔ vertical ↔ mixed
    case displayAdded = "Display Added"
    case displayRemoved = "Display Removed"
    case displayMoved = "Display Moved"
    case mainDisplayChanged = "Main Display Changed"
    case complex = "Complex"  // Multiple changes
}

/// Migration strategies for different scenarios
enum MigrationStrategy: String, Codable {
    case directMapping = "Direct Mapping"  // 1:1 display mapping
    case proportionalMapping = "Proportional Mapping"  // Scale to fit new arrangement
    case mainDisplayFallback = "Main Display Fallback"  // Move all to main display
    case intelligentDistribution = "Intelligent Distribution"  // Distribute across available displays
    case noMigration = "No Migration"  // Too complex, keep original positions
}

/// Service responsible for analyzing and migrating display arrangements
class DisplayArrangementMigrationService {
    private static let logger = LoggingService.shared
    private static let serviceName = "DisplayArrangementMigration"

    /// Analyze the differences between two display arrangements
    static func analyzeArrangementChange(
        from oldArrangement: DisplayArrangementInfo,
        to newArrangement: DisplayArrangementInfo
    ) -> DisplayArrangementAnalysis {

        logger.info(
            "┌─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )
        logger.info(
            "│ DISPLAY ARRANGEMENT ANALYSIS STARTED",
            service: serviceName
        )
        logger.info(
            "├─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )

        // Determine arrangement types
        let oldType = determineArrangementType(oldArrangement)
        let newType = determineArrangementType(newArrangement)

        logger.info(
            "│ OLD ARRANGEMENT: \(oldType.rawValue) (\(oldArrangement.displayIDs.count) displays)",
            service: serviceName
        )
        logger.info(
            "│ NEW ARRANGEMENT: \(newType.rawValue) (\(newArrangement.displayIDs.count) displays)",
            service: serviceName
        )

        // Identify added and removed displays
        let oldDisplaySet = Set(oldArrangement.displayIDs)
        let newDisplaySet = Set(newArrangement.displayIDs)
        let addedDisplays = Array(newDisplaySet.subtracting(oldDisplaySet))
        let removedDisplays = Array(oldDisplaySet.subtracting(newDisplaySet))

        logger.info(
            "│ ADDED DISPLAYS: \(addedDisplays.count) - \(addedDisplays)",
            service: serviceName
        )
        logger.info(
            "│ REMOVED DISPLAYS: \(removedDisplays.count) - \(removedDisplays)",
            service: serviceName
        )

        // Determine change type
        let changeType = determineChangeType(
            oldType: oldType,
            newType: newType,
            addedCount: addedDisplays.count,
            removedCount: removedDisplays.count,
            mainDisplayChanged: oldArrangement.mainDisplayID != newArrangement.mainDisplayID
        )

        logger.info(
            "│ CHANGE TYPE: \(changeType.rawValue)",
            service: serviceName
        )

        // Create display mappings
        let displayMappings = createDisplayMappings(
            from: oldArrangement,
            to: newArrangement,
            changeType: changeType
        )

        logger.info(
            "│ DISPLAY MAPPINGS: \(displayMappings.count)",
            service: serviceName
        )

        // Determine migration strategy
        let migrationStrategy = determineMigrationStrategy(
            changeType: changeType,
            mappings: displayMappings,
            oldType: oldType,
            newType: newType
        )

        logger.info(
            "│ MIGRATION STRATEGY: \(migrationStrategy.rawValue)",
            service: serviceName
        )

        // Calculate overall confidence
        let confidence = calculateMigrationConfidence(
            changeType: changeType,
            mappings: displayMappings,
            strategy: migrationStrategy
        )

        logger.info(
            "│ MIGRATION CONFIDENCE: \(String(format: "%.1f", confidence * 100))%",
            service: serviceName
        )
        logger.info(
            "└─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )

        return DisplayArrangementAnalysis(
            oldArrangement: oldArrangement,
            newArrangement: newArrangement,
            oldType: oldType,
            newType: newType,
            changeType: changeType,
            displayMappings: displayMappings,
            addedDisplays: addedDisplays,
            removedDisplays: removedDisplays,
            migrationStrategy: migrationStrategy,
            confidence: confidence
        )
    }

    /// Determine the arrangement type from DisplayArrangementInfo
    private static func determineArrangementType(_ arrangement: DisplayArrangementInfo)
        -> DisplayArrangementType
    {
        guard arrangement.displayIDs.count > 1 else { return .single }

        let frames = arrangement.displayFrames.values
        let xDifference = frames.map { $0.origin.x }.max()! - frames.map { $0.origin.x }.min()!
        let yDifference = frames.map { $0.origin.y }.max()! - frames.map { $0.origin.y }.min()!

        if xDifference > yDifference {
            return .horizontal
        } else if yDifference > xDifference {
            return .vertical
        } else {
            return .mixed
        }
    }

    /// Determine the type of change between arrangements
    private static func determineChangeType(
        oldType: DisplayArrangementType,
        newType: DisplayArrangementType,
        addedCount: Int,
        removedCount: Int,
        mainDisplayChanged: Bool
    ) -> ArrangementChangeType {

        // Count the number of changes
        var changeCount = 0

        if oldType != newType { changeCount += 1 }
        if addedCount > 0 { changeCount += 1 }
        if removedCount > 0 { changeCount += 1 }
        if mainDisplayChanged { changeCount += 1 }

        // Determine primary change type
        if changeCount == 0 {
            return .noChange
        } else if changeCount > 1 {
            return .complex
        } else {
            // Single change type
            if oldType != newType {
                return .typeChange
            } else if addedCount > 0 {
                return .displayAdded
            } else if removedCount > 0 {
                return .displayRemoved
            } else if mainDisplayChanged {
                return .mainDisplayChanged
            } else {
                return .displayMoved
            }
        }
    }

    /// Create mappings between displays in old and new arrangements
    private static func createDisplayMappings(
        from oldArrangement: DisplayArrangementInfo,
        to newArrangement: DisplayArrangementInfo,
        changeType: ArrangementChangeType
    ) -> [DisplayMapping] {

        var mappings: [DisplayMapping] = []

        // Get common displays (displays that exist in both arrangements)
        let oldDisplaySet = Set(oldArrangement.displayIDs)
        let newDisplaySet = Set(newArrangement.displayIDs)
        let commonDisplays = oldDisplaySet.intersection(newDisplaySet)

        logger.debug(
            "Creating mappings for \(commonDisplays.count) common displays",
            service: serviceName
        )

        // For common displays, create direct mappings
        for displayID in commonDisplays {
            guard let oldFrame = oldArrangement.displayFrames[displayID],
                let newFrame = newArrangement.displayFrames[displayID]
            else {
                continue
            }

            // Calculate transformation matrix
            let transform = calculateTransformationMatrix(from: oldFrame, to: newFrame)

            // Calculate confidence based on how much the display changed
            let confidence = calculateDisplayMappingConfidence(from: oldFrame, to: newFrame)

            let mapping = DisplayMapping(
                sourceDisplayID: displayID,
                targetDisplayID: displayID,
                transformationMatrix: transform,
                confidence: confidence
            )

            mappings.append(mapping)

            logger.debug(
                "Direct mapping: Display \(displayID) (confidence: \(String(format: "%.2f", confidence)))",
                service: serviceName
            )
        }

        // For removed displays, try to find best alternative mappings
        let removedDisplays = oldDisplaySet.subtracting(newDisplaySet)
        for removedDisplayID in removedDisplays {
            guard let removedFrame = oldArrangement.displayFrames[removedDisplayID] else {
                continue
            }

            // Find the best alternative display in the new arrangement
            if let bestAlternative = findBestAlternativeDisplay(
                for: removedFrame,
                in: newArrangement,
                excluding: Set(mappings.map { $0.targetDisplayID })
            ) {

                let newFrame = newArrangement.displayFrames[bestAlternative]!
                let transform = calculateTransformationMatrix(from: removedFrame, to: newFrame)
                let confidence =
                    calculateDisplayMappingConfidence(from: removedFrame, to: newFrame) * 0.7  // Reduce confidence for alternative mappings

                let mapping = DisplayMapping(
                    sourceDisplayID: removedDisplayID,
                    targetDisplayID: bestAlternative,
                    transformationMatrix: transform,
                    confidence: confidence
                )

                mappings.append(mapping)

                logger.debug(
                    "Alternative mapping: Display \(removedDisplayID) → \(bestAlternative) (confidence: \(String(format: "%.2f", confidence)))",
                    service: serviceName
                )
            }
        }

        return mappings
    }

    /// Calculate transformation matrix between two display frames
    private static func calculateTransformationMatrix(from oldFrame: CGRect, to newFrame: CGRect)
        -> CGAffineTransform
    {
        // Calculate scale factors
        let scaleX = newFrame.width / oldFrame.width
        let scaleY = newFrame.height / oldFrame.height

        // Calculate translation
        let translateX = newFrame.origin.x - oldFrame.origin.x
        let translateY = newFrame.origin.y - oldFrame.origin.y

        // Create transformation matrix
        var transform = CGAffineTransform.identity
        transform = transform.scaledBy(x: scaleX, y: scaleY)
        transform = transform.translatedBy(x: translateX / scaleX, y: translateY / scaleY)

        return transform
    }

    /// Calculate confidence for a display mapping based on how similar the frames are
    private static func calculateDisplayMappingConfidence(
        from oldFrame: CGRect, to newFrame: CGRect
    ) -> Float {
        // Calculate similarity factors
        let sizeRatio =
            min(oldFrame.width / newFrame.width, newFrame.width / oldFrame.width)
            * min(oldFrame.height / newFrame.height, newFrame.height / oldFrame.height)

        let positionDistance = sqrt(
            pow(oldFrame.origin.x - newFrame.origin.x, 2)
                + pow(oldFrame.origin.y - newFrame.origin.y, 2))
        let maxDistance = sqrt(
            pow(oldFrame.width + newFrame.width, 2) + pow(oldFrame.height + newFrame.height, 2))
        let positionSimilarity = max(0, 1 - (positionDistance / maxDistance))

        // Combine factors (weighted average)
        let confidence = Float(sizeRatio * 0.6 + positionSimilarity * 0.4)

        return min(1.0, max(0.0, confidence))
    }

    /// Find the best alternative display for a removed display
    private static func findBestAlternativeDisplay(
        for removedFrame: CGRect,
        in newArrangement: DisplayArrangementInfo,
        excluding excludedDisplays: Set<CGDirectDisplayID>
    ) -> CGDirectDisplayID? {

        var bestDisplay: CGDirectDisplayID?
        var bestScore: Float = 0.0

        for displayID in newArrangement.displayIDs {
            guard !excludedDisplays.contains(displayID),
                let frame = newArrangement.displayFrames[displayID]
            else {
                continue
            }

            let score = calculateDisplayMappingConfidence(from: removedFrame, to: frame)
            if score > bestScore {
                bestScore = score
                bestDisplay = displayID
            }
        }

        return bestDisplay
    }

    /// Determine the best migration strategy based on the analysis
    private static func determineMigrationStrategy(
        changeType: ArrangementChangeType,
        mappings: [DisplayMapping],
        oldType: DisplayArrangementType,
        newType: DisplayArrangementType
    ) -> MigrationStrategy {

        switch changeType {
        case .noChange:
            return .directMapping

        case .typeChange:
            // Arrangement type changed (e.g., horizontal to vertical)
            if mappings.count == oldType.rawValue.count
                && mappings.allSatisfy({ $0.confidence > 0.8 })
            {
                return .directMapping
            } else {
                return .proportionalMapping
            }

        case .displayAdded:
            // New display added - use intelligent distribution
            return .intelligentDistribution

        case .displayRemoved:
            // Display removed - map to remaining displays
            if mappings.count > 0 && mappings.allSatisfy({ $0.confidence > 0.6 }) {
                return .proportionalMapping
            } else {
                return .mainDisplayFallback
            }

        case .displayMoved:
            // Display positions changed
            if mappings.allSatisfy({ $0.confidence > 0.7 }) {
                return .directMapping
            } else {
                return .proportionalMapping
            }

        case .mainDisplayChanged:
            // Main display changed
            return .proportionalMapping

        case .complex:
            // Multiple changes - use conservative approach
            if mappings.count > 0 && mappings.allSatisfy({ $0.confidence > 0.8 }) {
                return .proportionalMapping
            } else {
                return .mainDisplayFallback
            }
        }
    }

    /// Calculate overall migration confidence
    private static func calculateMigrationConfidence(
        changeType: ArrangementChangeType,
        mappings: [DisplayMapping],
        strategy: MigrationStrategy
    ) -> Float {

        // Base confidence based on change type
        let baseConfidence: Float
        switch changeType {
        case .noChange: baseConfidence = 1.0
        case .typeChange: baseConfidence = 0.8
        case .displayAdded: baseConfidence = 0.7
        case .displayRemoved: baseConfidence = 0.6
        case .displayMoved: baseConfidence = 0.8
        case .mainDisplayChanged: baseConfidence = 0.7
        case .complex: baseConfidence = 0.4
        }

        // Adjust based on mapping quality
        let mappingConfidence: Float
        if mappings.isEmpty {
            mappingConfidence = 0.0
        } else {
            mappingConfidence = mappings.map { $0.confidence }.reduce(0, +) / Float(mappings.count)
        }

        // Adjust based on strategy
        let strategyMultiplier: Float
        switch strategy {
        case .directMapping: strategyMultiplier = 1.0
        case .proportionalMapping: strategyMultiplier = 0.9
        case .intelligentDistribution: strategyMultiplier = 0.8
        case .mainDisplayFallback: strategyMultiplier = 0.6
        case .noMigration: strategyMultiplier = 0.0
        }

        // Calculate final confidence
        let finalConfidence = (baseConfidence * 0.4 + mappingConfidence * 0.6) * strategyMultiplier

        return min(1.0, max(0.0, finalConfidence))
    }
}

// MARK: - Window Migration Engine

/// Engine responsible for migrating window positions based on display arrangement analysis
class WindowMigrationEngine {
    private static let logger = LoggingService.shared
    private static let serviceName = "WindowMigrationEngine"

    /// Migrate a workspace's window positions to a new display arrangement
    static func migrateWorkspace(
        _ workspace: Workspace,
        using analysis: DisplayArrangementAnalysis
    ) -> Workspace {

        logger.info(
            "┌─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )
        logger.info(
            "│ WORKSPACE MIGRATION STARTED",
            service: serviceName
        )
        logger.info(
            "├─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )
        logger.info(
            "│ WORKSPACE: \(workspace.name)",
            service: serviceName
        )
        logger.info(
            "│ WINDOWS: \(workspace.windowInfos.count)",
            service: serviceName
        )
        logger.info(
            "│ STRATEGY: \(analysis.migrationStrategy.rawValue)",
            service: serviceName
        )
        logger.info(
            "│ CONFIDENCE: \(String(format: "%.1f", analysis.confidence * 100))%",
            service: serviceName
        )
        logger.info(
            "├─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )

        // Check if migration is needed
        if analysis.changeType == .noChange {
            logger.info(
                "│ No migration needed - display arrangement unchanged",
                service: serviceName
            )
            logger.info(
                "└─────────────────────────────────────────────────────────────────────────",
                service: serviceName
            )
            return workspace
        }

        // Check if migration confidence is too low
        if analysis.confidence < 0.3 {
            logger.warning(
                "│ Migration confidence too low (\(String(format: "%.1f", analysis.confidence * 100))%) - skipping migration",
                service: serviceName
            )
            logger.info(
                "└─────────────────────────────────────────────────────────────────────────",
                service: serviceName
            )
            return workspace
        }

        // Migrate windows based on strategy
        let migratedWindowInfos: [WorkspaceWindowInfo]

        switch analysis.migrationStrategy {
        case .directMapping:
            migratedWindowInfos = migrateWithDirectMapping(
                windowInfos: workspace.windowInfos,
                analysis: analysis
            )

        case .proportionalMapping:
            migratedWindowInfos = migrateWithProportionalMapping(
                windowInfos: workspace.windowInfos,
                analysis: analysis
            )

        case .intelligentDistribution:
            migratedWindowInfos = migrateWithIntelligentDistribution(
                windowInfos: workspace.windowInfos,
                analysis: analysis
            )

        case .mainDisplayFallback:
            migratedWindowInfos = migrateToMainDisplay(
                windowInfos: workspace.windowInfos,
                analysis: analysis
            )

        case .noMigration:
            logger.info(
                "│ No migration strategy available - keeping original positions",
                service: serviceName
            )
            migratedWindowInfos = workspace.windowInfos
        }

        logger.info(
            "│ MIGRATION COMPLETE: \(migratedWindowInfos.count) windows processed",
            service: serviceName
        )
        logger.info(
            "└─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )

        // Create new workspace with migrated windows and updated display arrangement
        return Workspace(
            id: workspace.id,
            name: workspace.name,
            windowInfos: migratedWindowInfos,
            shortcutKeyCode: workspace.shortcutKeyCode,
            shortcutModifiers: workspace.shortcutModifiers,
            displayArrangement: analysis.newArrangement,  // Update to new arrangement
            customLayout: workspace.customLayout
        )
    }

    /// Migrate windows using direct display mapping
    private static func migrateWithDirectMapping(
        windowInfos: [WorkspaceWindowInfo],
        analysis: DisplayArrangementAnalysis
    ) -> [WorkspaceWindowInfo] {

        logger.debug("Using direct mapping migration", service: serviceName)

        return windowInfos.compactMap { windowInfo in
            // Find the display mapping for this window's monitor
            guard let monitorID = windowInfo.monitorID,
                let sourceDisplayID = findDisplayIDForMonitorUUID(
                    monitorID, in: analysis.oldArrangement),
                let mapping = analysis.displayMappings.first(where: {
                    $0.sourceDisplayID == sourceDisplayID
                })
            else {

                logger.debug(
                    "No mapping found for window \(windowInfo.appBundleIdentifier ?? "unknown") - keeping original position",
                    service: serviceName
                )
                return windowInfo
            }

            // Apply transformation to the window frame
            let transformedFrame = windowInfo.frame.applying(mapping.transformationMatrix)

            // Create new monitor UUID for the target display
            let newMonitorID = createMonitorUUID(for: mapping.targetDisplayID)

            logger.debug(
                "Mapped window \(windowInfo.appBundleIdentifier ?? "unknown"): Display \(mapping.sourceDisplayID) → \(mapping.targetDisplayID)",
                service: serviceName
            )

            return WorkspaceWindowInfo(
                frame: transformedFrame,
                monitorID: newMonitorID,
                appBundleIdentifier: windowInfo.appBundleIdentifier,
                isFullscreen: windowInfo.isFullscreen,
                zOrder: windowInfo.zOrder
            )
        }
    }

    /// Migrate windows using proportional mapping (scale to fit new arrangement)
    private static func migrateWithProportionalMapping(
        windowInfos: [WorkspaceWindowInfo],
        analysis: DisplayArrangementAnalysis
    ) -> [WorkspaceWindowInfo] {

        logger.debug("Using proportional mapping migration", service: serviceName)

        return windowInfos.compactMap { windowInfo in
            // Find the best target display for this window
            guard let monitorID = windowInfo.monitorID,
                let sourceDisplayID = findDisplayIDForMonitorUUID(
                    monitorID, in: analysis.oldArrangement)
            else {

                // Fallback to main display if no source display found
                return migrateWindowToMainDisplay(windowInfo, analysis: analysis)
            }

            // Find the best mapping for this display
            if let mapping = analysis.displayMappings.first(where: {
                $0.sourceDisplayID == sourceDisplayID
            }) {
                // Use existing mapping
                let transformedFrame = windowInfo.frame.applying(mapping.transformationMatrix)
                let newMonitorID = createMonitorUUID(for: mapping.targetDisplayID)

                return WorkspaceWindowInfo(
                    frame: transformedFrame,
                    monitorID: newMonitorID,
                    appBundleIdentifier: windowInfo.appBundleIdentifier,
                    isFullscreen: windowInfo.isFullscreen,
                    zOrder: windowInfo.zOrder
                )
            } else {
                // No direct mapping - use proportional scaling to main display
                return migrateWindowToMainDisplay(windowInfo, analysis: analysis)
            }
        }
    }

    /// Migrate windows using intelligent distribution across available displays
    private static func migrateWithIntelligentDistribution(
        windowInfos: [WorkspaceWindowInfo],
        analysis: DisplayArrangementAnalysis
    ) -> [WorkspaceWindowInfo] {

        logger.debug("Using intelligent distribution migration", service: serviceName)

        // Distribute windows evenly across available displays
        let availableDisplays = analysis.newArrangement.displayIDs
        var displayIndex = 0

        return windowInfos.map { windowInfo in
            let targetDisplayID = availableDisplays[displayIndex % availableDisplays.count]
            displayIndex += 1

            // Scale window to fit the target display
            let scaledFrame = scaleWindowToDisplay(
                windowFrame: windowInfo.frame,
                targetDisplayID: targetDisplayID,
                newArrangement: analysis.newArrangement
            )

            let newMonitorID = createMonitorUUID(for: targetDisplayID)

            logger.debug(
                "Distributed window \(windowInfo.appBundleIdentifier ?? "unknown") to display \(targetDisplayID)",
                service: serviceName
            )

            return WorkspaceWindowInfo(
                frame: scaledFrame,
                monitorID: newMonitorID,
                appBundleIdentifier: windowInfo.appBundleIdentifier,
                isFullscreen: windowInfo.isFullscreen,
                zOrder: windowInfo.zOrder
            )
        }
    }

    /// Migrate all windows to the main display
    private static func migrateToMainDisplay(
        windowInfos: [WorkspaceWindowInfo],
        analysis: DisplayArrangementAnalysis
    ) -> [WorkspaceWindowInfo] {

        logger.debug("Using main display fallback migration", service: serviceName)

        return windowInfos.map { windowInfo in
            migrateWindowToMainDisplay(windowInfo, analysis: analysis)
        }
    }

    // MARK: - Helper Functions

    /// Find display ID for a monitor UUID in a display arrangement
    private static func findDisplayIDForMonitorUUID(
        _ monitorUUID: UUID,
        in arrangement: DisplayArrangementInfo
    ) -> CGDirectDisplayID? {

        // Use the same UUID creation logic as WindowLayoutManager
        for displayID in arrangement.displayIDs {
            let displayUUID = createMonitorUUID(for: displayID)
            if displayUUID == monitorUUID {
                return displayID
            }
        }
        return nil
    }

    /// Create a consistent UUID for a display ID (same logic as WindowLayoutManager)
    private static func createMonitorUUID(for displayID: CGDirectDisplayID) -> UUID {
        // Convert the display ID to a string
        let displayIDString = String(displayID)

        // Create a deterministic UUID by using a fixed pattern with the display ID
        let hexString = String(format: "%08x", displayID)
        let uuidString =
            "\(hexString.prefix(8))-\(hexString.prefix(4))-4\(hexString.prefix(3))-a\(hexString.prefix(3))-\(displayIDString.padding(toLength: 12, withPad: "0", startingAt: 0))"

        // Create UUID from the formatted string, fallback to a random UUID if invalid
        return UUID(uuidString: uuidString) ?? UUID()
    }

    /// Migrate a single window to the main display
    private static func migrateWindowToMainDisplay(
        _ windowInfo: WorkspaceWindowInfo,
        analysis: DisplayArrangementAnalysis
    ) -> WindowInfo {

        let mainDisplayID = analysis.newArrangement.mainDisplayID
        let newMonitorID = createMonitorUUID(for: mainDisplayID)

        // Scale window to fit main display if needed
        let scaledFrame = scaleWindowToDisplay(
            windowFrame: windowInfo.frame,
            targetDisplayID: mainDisplayID,
            newArrangement: analysis.newArrangement
        )

        logger.debug(
            "Migrated window \(windowInfo.appBundleIdentifier ?? "unknown") to main display",
            service: serviceName
        )

        return WorkspaceWindowInfo(
            frame: scaledFrame,
            monitorID: newMonitorID,
            appBundleIdentifier: windowInfo.appBundleIdentifier,
            isFullscreen: windowInfo.isFullscreen,
            zOrder: windowInfo.zOrder
        )
    }

    /// Scale a window frame to fit a target display
    private static func scaleWindowToDisplay(
        windowFrame: CGRect,
        targetDisplayID: CGDirectDisplayID,
        newArrangement: DisplayArrangementInfo
    ) -> CGRect {

        guard let targetFrame = newArrangement.displayFrames[targetDisplayID] else {
            // If we can't find the target display, return the original frame
            return windowFrame
        }

        // Ensure the window fits within the display bounds
        let maxWidth = targetFrame.width * 0.9  // Leave some margin
        let maxHeight = targetFrame.height * 0.9

        var scaledFrame = windowFrame

        // Scale down if the window is too large
        if scaledFrame.width > maxWidth {
            let scale = maxWidth / scaledFrame.width
            scaledFrame.size.width = maxWidth
            scaledFrame.size.height *= scale
        }

        if scaledFrame.height > maxHeight {
            let scale = maxHeight / scaledFrame.height
            scaledFrame.size.height = maxHeight
            scaledFrame.size.width *= scale
        }

        // Ensure the window position is within the display
        scaledFrame.origin.x = max(0.1, min(scaledFrame.origin.x, 0.9 - scaledFrame.width))
        scaledFrame.origin.y = max(0.1, min(scaledFrame.origin.y, 0.9 - scaledFrame.height))

        return scaledFrame
    }
}
