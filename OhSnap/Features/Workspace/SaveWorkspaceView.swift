import AppKit
import Carbon.HIToolbox
import Foundation
import KeyboardShortcuts
import SwiftUI

// Window delegate to handle window closing
class SaveWorkspaceWindowDelegate: NSObject, NSWindowDelegate {
    let tempShortcutName = KeyboardShortcuts.Name("temp_workspace_shortcut")

    // Callback to reset state in the view
    var onWindowClose: (() -> Void)?

    func windowWillClose(_ notification: Notification) {
        let logger = LoggingService.shared
        logger.debug(
            "Window will close, resetting temporary shortcut",
            service: "SaveWorkspaceWindowDelegate",
            category: .userInterface
        )
        KeyboardShortcuts.reset(tempShortcutName)

        // Call the callback to reset state in the view
        onWindowClose?()
    }
}

struct SaveWorkspaceView: View {
    @EnvironmentObject var workspaceService: WorkspaceService
    @Environment(\.dismiss) var dismiss

    // Logger
    private let logger = LoggingService.shared
    private let serviceName = "SaveWorkspaceView"

    // Form State
    @State private var workspaceName = ""
    @State private var customLayoutName = ""
    @FocusState private var isNameFieldFocused: Bool

    // App selection state
    @State private var appItems: [AppSelectionItem] = []
    @State private var selectedCount: Int = 0

    // Shortcut Recording State
    @State private var shortcutDisplayString: String = "None"
    @State private var recordedKeyCode: UInt16? = nil
    @State private var recordedModifiers: UInt? = nil

    // Input data
    var windowInfos: [WindowInfo]

    // Window reference
    @State var window: NSWindow?

    // Window delegate to handle window closing
    private let windowDelegate = SaveWorkspaceWindowDelegate()

    private let labelWidth: CGFloat = 200

    // Create a separate view for the row
    struct WorkspaceAppRow: View {
        @Binding var appItem: AppSelectionItem
        @Binding var selectedCount: Int
        var appItems: [AppSelectionItem]
        let logger: LoggingService
        let serviceName: String

        var body: some View {
            HStack(spacing: 12) {
                Image(nsImage: appItem.appIcon)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 20, height: 20)

                appItem.displayNameView
                    .lineLimit(1)

                Spacer()

                Toggle(
                    "",
                    isOn: Binding(
                        get: { appItem.isSelected },
                        set: { newValue in
                            appItem.isSelected = newValue
                            selectedCount = appItems.filter { $0.isSelected }.count

                            logger.debug(
                                "Toggle changed for \(appItem.appName): \(newValue), Selected count: \(selectedCount)",
                                service: serviceName,
                                category: .userInterface
                            )
                        }
                    )
                )
                .labelsHidden()
                .toggleStyle(.switch)
            }
            .ohSnapRowStyle()
            .contentShape(Rectangle())
            .onTapGesture {
                appItem.isSelected.toggle()
                selectedCount = appItems.filter { $0.isSelected }.count
                logger.debug(
                    "Row tapped for \(appItem.appName): \(appItem.isSelected), Selected count: \(selectedCount)",
                    service: serviceName,
                    category: .userInterface
                )
            }
        }
    }

    // Update the app list content
    private var appListContent: some View {
        VStack(spacing: 0) {
            ForEach(appItems.indices, id: \.self) { index in
                WorkspaceAppRow(
                    appItem: $appItems[index],
                    selectedCount: $selectedCount,
                    appItems: appItems,
                    logger: logger,
                    serviceName: serviceName
                )
                .background(
                    index % 2 == 0 ? Color.clear : Color.gray.opacity(0.05)
                )
            }
        }
    }

    var body: some View {
        WorkspaceFormView(
            workspaceName: $workspaceName,
            customLayoutName: $customLayoutName,
            appItems: $appItems,
            selectedCount: $selectedCount,
            shortcutDisplayString: $shortcutDisplayString,
            recordedKeyCode: $recordedKeyCode,
            recordedModifiers: $recordedModifiers,
            onSave: saveWorkspace,
            onCancel: {
                // Reset the temporary shortcut when Cancel is clicked
                let tempShortcutName = KeyboardShortcuts.Name("temp_workspace_shortcut")
                KeyboardShortcuts.reset(tempShortcutName)

                // Reset our local state
                resetShortcutState()

                logger.debug(
                    "Cancel clicked, temporary shortcut reset",
                    service: serviceName,
                    category: .userInterface
                )

                dismiss()
            },
            onUpdatePositions: updateCurrentPositions
        )
        .frame(width: 600, height: 800)
        .onAppear {
            // Load preview on appear
            if appItems.isEmpty {
                prepareAppItems()
            }

            // Set up window delegate when the view appears
            if let window = NSApp.windows.first(where: { $0.isKeyWindow }) {
                self.window = window

                // Set the window delegate to handle window closing
                windowDelegate.onWindowClose = { [self] in
                    // Reset the local state when the window is closed
                    resetShortcutState()
                }
                window.delegate = windowDelegate
                logger.debug(
                    "Set window delegate to handle window closing",
                    service: serviceName,
                    category: .userInterface
                )
            }
        }
        .onDisappear {
            // Reset the temporary shortcut when the view disappears
            // This ensures the shortcut is reset when the window is closed without saving
            let tempShortcutName = KeyboardShortcuts.Name("temp_workspace_shortcut")
            KeyboardShortcuts.reset(tempShortcutName)

            // Reset our local state
            resetShortcutState()

            logger.debug(
                "View disappeared, temporary shortcut reset",
                service: serviceName,
                category: .userInterface
            )
        }
    }

    // Conflict checking
    struct ShortcutConflict {
        let type: String  // "workspace" or "action"
        let name: String  // Name of the conflicting item
    }

    // Check for conflicts with existing shortcuts
    private func checkForShortcutConflicts(keyCode: UInt16, modifiers: UInt) -> ShortcutConflict? {
        // Check for conflicts with workspaces
        for workspace in workspaceService.workspaces {
            if let workspaceKeyCode = workspace.shortcutKeyCode,
                let workspaceModifiers = workspace.shortcutModifiers,
                workspaceKeyCode == keyCode && workspaceModifiers == modifiers
            {
                return ShortcutConflict(type: "workspace", name: workspace.name)
            }
        }

        // Check for conflicts with regular shortcuts
        let shortcuts = [
            ("Left Half", "leftHalf"),
            ("Right Half", "rightHalf"),
            ("Top Half", "topHalf"),
            ("Bottom Half", "bottomHalf"),
            ("Top Left Quarter", "topLeftQuarter"),
            ("Top Right Quarter", "topRightQuarter"),
            ("Bottom Left Quarter", "bottomLeftQuarter"),
            ("Bottom Right Quarter", "bottomRightQuarter"),
            ("Left Third", "leftThird"),
            ("Center Third", "centerThird"),
            ("Right Third", "rightThird"),
            ("Left Two Thirds", "leftTwoThirds"),
            ("Center Two Thirds", "centerTwoThirds"),
            ("Right Two Thirds", "rightTwoThirds"),
            ("Fullscreen", "fullscreen"),
        ]

        for (name, action) in shortcuts {
            let shortcut = ShortcutDefaults.shared.getShortcut(for: action)
            if shortcut.keyCode == keyCode && shortcut.modifiers == modifiers {
                return ShortcutConflict(type: "action", name: name)
            }
        }

        // Check for system shortcut conflicts
        if let systemConflict = SystemShortcutChecker.shared.checkForSystemConflict(
            keyCode: keyCode, modifiers: modifiers)
        {
            return ShortcutConflict(
                type: "system", name: systemConflict.replacingOccurrences(of: "System: ", with: ""))
        }

        return nil
    }

    // Alert state
    @State private var showingConflictAlert = false
    @State private var conflictAlertMessage = ""

    private func showConflictAlert(conflictType: String, conflictName: String) {
        logger.warning(
            "Showing conflict alert: \(conflictName) \(conflictType)",
            service: serviceName,
            category: .shortcuts
        )

        // Show an NSAlert instead of using SwiftUI alert
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "Shortcut Already in Use"
            alert.informativeText =
                "This shortcut is already assigned to \(conflictName) \(conflictType)."
            alert.alertStyle = .warning
            alert.addButton(withTitle: "OK")
            alert.runModal()
            logger.debug(
                "NSAlert shown",
                service: serviceName,
                category: .userInterface
            )
        }
    }

    // MARK: - App Selection Preparation
    private func prepareAppItems() {
        // Get OhSnap's bundle ID
        let ownBundleID = Bundle.main.bundleIdentifier

        // Convert window infos to app selection items, excluding OhSnap windows
        appItems =
            windowInfos
            .filter { windowInfo in
                // Filter out OhSnap's own windows
                windowInfo.appBundleIdentifier != ownBundleID
            }
            .map { windowInfo in
                AppSelectionItem(windowInfo: windowInfo, isSelected: true)
            }

        // Sort by app name and then by display
        appItems.sort { item1, item2 in
            if item1.appName == item2.appName {
                // If same app, sort by display
                let display1 = item1.windowInfo.monitorID?.uuidString ?? ""
                let display2 = item2.windowInfo.monitorID?.uuidString ?? ""
                return display1 < display2
            }
            return item1.appName < item2.appName
        }

        // Initialize the selected count
        selectedCount = appItems.count

        logger.debug(
            "Prepared \(appItems.count) app items for selection (excluding OhSnap windows), all selected by default",
            service: serviceName,
            category: .userInterface
        )
    }

    // MARK: - Update Current Positions
    private func updateCurrentPositions() {
        // Log the start of the update process
        logger.info(
            "┌─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )
        logger.info(
            "│ UPDATING WORKSPACE POSITIONS - STARTED",
            service: serviceName
        )
        logger.info(
            "└─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )

        // Show loading toast
        ToastManager.shared.showLoading(
            title: "Updating Positions",
            message: "Refreshing display arrangement and capturing window positions..."
        )

        // Force refresh the NSScreen.screens to ensure we have the latest display arrangement
        // This is important after display arrangement changes in macOS
        let screens = NSScreen.screens

        // Check if we have a vertical arrangement with Retina displays
        let isVertical = isVerticalArrangement(screens: screens)
        let hasRetina = screens.contains(where: { $0.backingScaleFactor == 2.0 })

        // Log display arrangement information
        logger.info(
            "│ DISPLAY ARRANGEMENT:",
            service: serviceName
        )
        logger.info(
            "│ - Total displays: \(screens.count)",
            service: serviceName
        )
        logger.info(
            "│ - Arrangement: \(isVertical ? "Vertical" : "Horizontal")",
            service: serviceName
        )
        logger.info(
            "│ - Has Retina displays: \(hasRetina)",
            service: serviceName
        )

        // Log each display's information
        for (index, screen) in screens.enumerated() {
            logger.info(
                "│ DISPLAY #\(index):",
                service: serviceName
            )
            logger.info(
                "│ - Frame: \(screen.frame)",
                service: serviceName
            )
            logger.info(
                "│ - Visible Frame: \(screen.visibleFrame)",
                service: serviceName
            )
            logger.info(
                "│ - Scale Factor: \(screen.backingScaleFactor)",
                service: serviceName
            )
        }

        // Get current window positions from WindowCaptureService with normalized coordinates
        // This will force a refresh of all display information and window positions
        let currentWindowInfos = WindowCaptureService.getCurrentAppsAndWindowPositionsNormalized()

        // Get OhSnap's bundle ID
        let ownBundleID = Bundle.main.bundleIdentifier

        // Filter out OhSnap's own windows
        let filteredWindowInfos = currentWindowInfos.filter {
            $0.appBundleIdentifier != ownBundleID
        }

        // Log window capture information
        logger.info(
            "│ WINDOW CAPTURE:",
            service: serviceName
        )
        logger.info(
            "│ - Total windows captured: \(currentWindowInfos.count)",
            service: serviceName
        )
        logger.info(
            "│ - Windows after filtering: \(filteredWindowInfos.count)",
            service: serviceName
        )

        // Create a fresh set of app items from the current window positions
        let newAppItems = filteredWindowInfos.map { windowInfo in
            // Check if this window's app was previously selected
            let wasSelected =
                appItems.first { item in
                    item.windowInfo.appBundleIdentifier == windowInfo.appBundleIdentifier
                }?.isSelected ?? true  // Default to selected if not found

            return AppSelectionItem(windowInfo: windowInfo, isSelected: wasSelected)
        }

        // Sort by app name and then by display (same as in prepareAppItems)
        let sortedAppItems = newAppItems.sorted { item1, item2 in
            if item1.appName == item2.appName {
                // If same app, sort by display
                let display1 = item1.windowInfo.monitorID?.uuidString ?? ""
                let display2 = item2.windowInfo.monitorID?.uuidString ?? ""
                return display1 < display2
            }
            return item1.appName < item2.appName
        }

        // Update the appItems array with the fresh data
        appItems = sortedAppItems

        // Update the selected count
        selectedCount = appItems.filter { $0.isSelected }.count

        // Log the update completion
        logger.info(
            "┌─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )
        logger.info(
            "│ UPDATING WORKSPACE POSITIONS - COMPLETED",
            service: serviceName
        )
        logger.info(
            "│ - Updated with \(appItems.count) windows, \(selectedCount) selected",
            service: serviceName
        )
        logger.info(
            "└─────────────────────────────────────────────────────────────────────────",
            service: serviceName
        )

        // Show success toast
        ToastManager.shared.completeLoading(
            title: "Positions Updated",
            message: "Window positions have been refreshed with the current display arrangement.",
            duration: 2.0
        )
    }

    // MARK: - Reset Shortcut State
    func resetShortcutState() {
        // Reset our local state
        recordedKeyCode = nil
        recordedModifiers = nil
        shortcutDisplayString = "None"

        logger.debug(
            "Local shortcut state reset",
            service: serviceName,
            category: .shortcuts
        )
    }

    // Helper method to determine if screens are arranged vertically
    private func isVerticalArrangement(screens: [NSScreen]) -> Bool {
        guard screens.count > 1 else { return false }

        // Sort screens by Y position (bottom to top)
        let sortedScreens = screens.sorted(by: { $0.frame.minY < $1.frame.minY })

        // Check if screens are stacked vertically
        for i in 0..<(sortedScreens.count - 1) {
            let currentScreen = sortedScreens[i]
            let nextScreen = sortedScreens[i + 1]

            // Calculate horizontal overlap
            let horizontalOverlap =
                min(currentScreen.frame.maxX, nextScreen.frame.maxX)
                - max(currentScreen.frame.minX, nextScreen.frame.minX)

            // If there's significant horizontal overlap, screens are arranged vertically
            if horizontalOverlap > 0 {
                return true
            }
        }

        return false
    }

    // MARK: - Save Workspace
    func saveWorkspace() {
        // Check if we have a vertical arrangement with Retina displays
        let isVertical = isVerticalArrangement(screens: NSScreen.screens)
        let hasRetina = NSScreen.screens.contains(where: { $0.backingScaleFactor == 2.0 })

        logger.info(
            "Saving workspace: vertical=\(isVertical), hasRetina=\(hasRetina)",
            service: serviceName
        )

        // Show loading toast
        ToastManager.shared.showLoading(
            title: "Saving Workspace",
            message: "Capturing window information..."
        )

        // Create custom layout if needed
        var createdCustomLayout: CustomLayout?
        if !customLayoutName.isEmpty {
            createdCustomLayout = createCustomLayout()
        }

        // Get only the selected window infos
        let selectedWindowInfos =
            appItems
            .filter { $0.isSelected }
            .map { $0.windowInfo }

        // Log window info details before saving
        let logger = LoggingService.shared
        logger.debug(
            "Saving workspace with \(selectedWindowInfos.count) windows:",
            service: "SaveWorkspaceView",
            category: .windowPositioning
        )

        for (index, info) in selectedWindowInfos.enumerated() {
            logger.debug(
                "Window \(index):",
                service: "SaveWorkspaceView",
                category: .windowPositioning
            )
            logger.debug(
                "  - Frame: \(info.frame)",
                service: "SaveWorkspaceView",
                category: .windowPositioning
            )
            logger.debug(
                "  - Frame details: x=\(info.frame.origin.x), y=\(info.frame.origin.y), width=\(info.frame.width), height=\(info.frame.height)",
                service: "SaveWorkspaceView",
                category: .windowPositioning
            )
            logger.debug(
                "  - App: \(info.appBundleIdentifier ?? "unknown")",
                service: "SaveWorkspaceView",
                category: .windowPositioning
            )
            logger.debug(
                "  - Monitor ID: \(info.monitorID?.uuidString ?? "unknown")",
                service: "SaveWorkspaceView",
                category: .windowPositioning
            )
            logger.debug(
                "  - Fullscreen: \(info.isFullscreen)",
                service: "SaveWorkspaceView",
                category: .windowPositioning
            )

            // Log screen information for this window
            if let screen = NSScreen.screens.first(where: {
                $0.frame.contains(CGPoint(x: info.frame.midX, y: info.frame.midY))
            }) {
                logger.debug(
                    "  - Screen frame: \(screen.frame)",
                    service: "SaveWorkspaceView",
                    category: .screenDetection
                )
                logger.debug(
                    "  - Screen visible frame: \(screen.visibleFrame)",
                    service: "SaveWorkspaceView",
                    category: .screenDetection
                )
                if let screenNumber = screen.deviceDescription[
                    NSDeviceDescriptionKey("NSScreenNumber")] as? NSNumber
                {
                    logger.debug(
                        "  - Screen number: \(screenNumber)",
                        service: "SaveWorkspaceView",
                        category: .screenDetection
                    )
                }
                logger.debug(
                    "  - Screen backing scale factor: \(screen.backingScaleFactor)",
                    service: "SaveWorkspaceView",
                    category: .screenDetection
                )
            } else {
                logger.warning(
                    "  - No screen found containing this window",
                    service: "SaveWorkspaceView",
                    category: .screenDetection
                )
            }
        }

        let workspaceId = UUID()

        // Log shortcut details before saving
        if let keyCode = recordedKeyCode, let modifiers = recordedModifiers {
            logger.info(
                "Saving workspace with shortcut - KeyCode: \(keyCode), Modifiers: \(modifiers)",
                service: "SaveWorkspaceView",
                category: .shortcuts
            )

            // Check if it's a number key with just Command
            let isNumberKey = (keyCode >= 18 && keyCode <= 29)  // Number keys 1-0
            let isJustCmd = (modifiers == NSEvent.ModifierFlags.command.rawValue)

            if isNumberKey && isJustCmd {
                logger.warning(
                    "Saving workspace with potential system shortcut conflict (Cmd+Number)",
                    service: "SaveWorkspaceView",
                    category: .shortcuts
                )
            }
        } else {
            logger.info(
                "Saving workspace without shortcut",
                service: "SaveWorkspaceView",
                category: .shortcuts
            )
        }

        // Capture the current display arrangement
        let displayArrangement = WindowLayoutManager.captureCurrentDisplayArrangement()

        // Log display arrangement information
        logger.info(
            "Captured display arrangement for workspace: \(displayArrangement.displayIDs.count) displays",
            service: serviceName
        )

        // Get the shortcut from the temporary recorder
        let tempShortcutName = KeyboardShortcuts.Name("temp_workspace_shortcut")
        let tempShortcut = KeyboardShortcuts.getShortcut(for: tempShortcutName)

        // Extract shortcut data from the temporary shortcut
        var finalKeyCode: UInt16? = recordedKeyCode
        var finalModifiers: UInt? = recordedModifiers

        if let shortcut = tempShortcut, let key = shortcut.key {
            finalKeyCode = UInt16(key.rawValue)
            finalModifiers = shortcut.modifiers.rawValue
        }

        // Create the workspace with only selected windows and display arrangement
        let newWorkspace = Workspace(
            id: workspaceId,
            name: workspaceName,
            windowInfos: selectedWindowInfos,
            // Save recorded shortcut data
            shortcutKeyCode: finalKeyCode,
            shortcutModifiers: finalModifiers,
            // Save display arrangement
            displayArrangement: displayArrangement,
            customLayout: createdCustomLayout
        )

        // Add the workspace to the service
        workspaceService.addWorkspace(newWorkspace)
        logger.info(
            "Workspace '\(workspaceName)' saved successfully with \(selectedWindowInfos.count) windows",
            service: serviceName)

        // Show success toast first
        ToastManager.shared.completeLoading(
            title: "Workspace Saved",
            message: "\(workspaceName) has been saved with \(selectedWindowInfos.count) apps.",
            duration: 3.0
        )

        // Register the workspace shortcut immediately
        // Try direct access first, fallback to notification if needed
        if let appDelegate = NSApp.delegate as? AppDelegate {
            appDelegate.shortcutService.registerWorkspaceShortcutImmediately(for: newWorkspace)
        } else {
            // Fallback: Use notification-based approach
            let userInfo = ["workspace": newWorkspace, "immediate": true] as [String: Any]
            NotificationCenter.default.post(
                name: Notification.Name("RegisterWorkspaceShortcutImmediately"),
                object: nil,
                userInfo: userInfo
            )
        }

        // Clear the temporary shortcut AFTER registering the workspace shortcut
        if tempShortcut != nil {
            KeyboardShortcuts.reset(tempShortcutName)
        }

        // Force menu refresh to show updated shortcut
        NotificationCenter.default.post(name: Notification.Name("RefreshStatusMenu"), object: nil)

        // Add a small delay to ensure shortcut registration completes before dismissing
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            self.dismiss()
        }
    }

    func createCustomLayout() -> CustomLayout? {
        // Find the main display based on coordinates (0,0)
        let mainScreen =
            NSScreen.screens.first { screen in
                screen.frame.origin.x == 0 && screen.frame.origin.y == 0
            } ?? NSScreen.screens.first

        guard let screen = mainScreen else { return nil }
        let screenFrame = screen.visibleFrame
        let firstFrame = CGRect(
            x: screenFrame.minX, y: screenFrame.minY, width: screenFrame.width / 2,
            height: screenFrame.height / 2)
        let layout = [firstFrame]
        return CustomLayout(name: customLayoutName, layout: layout)
    }

    // Helper function to format shortcut for display
    private func formatShortcut(keyCode: UInt16, modifiers: UInt) -> String? {
        var modifierString = ""

        // Add modifier symbols
        if modifiers & NSEvent.ModifierFlags.command.rawValue != 0 {
            modifierString += "⌘"
        }
        if modifiers & NSEvent.ModifierFlags.option.rawValue != 0 {
            modifierString += "⌥"
        }
        if modifiers & NSEvent.ModifierFlags.control.rawValue != 0 {
            modifierString += "⌃"
        }
        if modifiers & NSEvent.ModifierFlags.shift.rawValue != 0 {
            modifierString += "⇧"
        }

        // Get key character
        let keyChar = keyCodeToString(keyCode)

        return modifierString + keyChar
    }

    // Helper function to convert key code to string
    private func keyCodeToString(_ keyCode: UInt16) -> String {
        switch keyCode {
        case UInt16(kVK_Return): return "↩"
        case UInt16(kVK_Tab): return "⇥"
        case UInt16(kVK_Space): return "Space"
        case UInt16(kVK_Delete): return "⌫"
        case UInt16(kVK_Escape): return "⎋"
        case UInt16(kVK_Command): return "⌘"
        case UInt16(kVK_Shift): return "⇧"
        case UInt16(kVK_CapsLock): return "⇪"
        case UInt16(kVK_Option): return "⌥"
        case UInt16(kVK_Control): return "⌃"
        case UInt16(kVK_RightCommand): return "⌘"
        case UInt16(kVK_RightShift): return "⇧"
        case UInt16(kVK_RightOption): return "⌥"
        case UInt16(kVK_RightControl): return "⌃"
        case UInt16(kVK_F1): return "F1"
        case UInt16(kVK_F2): return "F2"
        case UInt16(kVK_F3): return "F3"
        case UInt16(kVK_F4): return "F4"
        case UInt16(kVK_F5): return "F5"
        case UInt16(kVK_F6): return "F6"
        case UInt16(kVK_F7): return "F7"
        case UInt16(kVK_F8): return "F8"
        case UInt16(kVK_F9): return "F9"
        case UInt16(kVK_F10): return "F10"
        case UInt16(kVK_F11): return "F11"
        case UInt16(kVK_F12): return "F12"
        case UInt16(kVK_F13): return "F13"
        case UInt16(kVK_F14): return "F14"
        case UInt16(kVK_F15): return "F15"
        case UInt16(kVK_F16): return "F16"
        case UInt16(kVK_F17): return "F17"
        case UInt16(kVK_F18): return "F18"
        case UInt16(kVK_F19): return "F19"
        case UInt16(kVK_F20): return "F20"
        case UInt16(kVK_Help): return "Help"
        case UInt16(kVK_Home): return "Home"
        case UInt16(kVK_PageUp): return "Page Up"
        case UInt16(kVK_ForwardDelete): return "⌦"
        case UInt16(kVK_End): return "End"
        case UInt16(kVK_PageDown): return "Page Down"
        case UInt16(kVK_LeftArrow): return "←"
        case UInt16(kVK_RightArrow): return "→"
        case UInt16(kVK_DownArrow): return "↓"
        case UInt16(kVK_UpArrow): return "↑"
        case UInt16(kVK_ANSI_Keypad0): return "0"
        case UInt16(kVK_ANSI_Keypad1): return "1"
        case UInt16(kVK_ANSI_Keypad2): return "2"
        case UInt16(kVK_ANSI_Keypad3): return "3"
        case UInt16(kVK_ANSI_Keypad4): return "4"
        case UInt16(kVK_ANSI_Keypad5): return "5"
        case UInt16(kVK_ANSI_Keypad6): return "6"
        case UInt16(kVK_ANSI_Keypad7): return "7"
        case UInt16(kVK_ANSI_Keypad8): return "8"
        case UInt16(kVK_ANSI_Keypad9): return "9"
        case UInt16(kVK_ANSI_KeypadMultiply): return "*"
        case UInt16(kVK_ANSI_KeypadPlus): return "+"
        case UInt16(kVK_ANSI_KeypadMinus): return "-"
        case UInt16(kVK_ANSI_KeypadDecimal): return "."
        case UInt16(kVK_ANSI_KeypadDivide): return "/"
        case UInt16(kVK_ANSI_KeypadEnter): return "↩"
        case UInt16(kVK_ANSI_KeypadEquals): return "="
        case UInt16(kVK_ANSI_0): return "0"
        case UInt16(kVK_ANSI_1): return "1"
        case UInt16(kVK_ANSI_2): return "2"
        case UInt16(kVK_ANSI_3): return "3"
        case UInt16(kVK_ANSI_4): return "4"
        case UInt16(kVK_ANSI_5): return "5"
        case UInt16(kVK_ANSI_6): return "6"
        case UInt16(kVK_ANSI_7): return "7"
        case UInt16(kVK_ANSI_8): return "8"
        case UInt16(kVK_ANSI_9): return "9"
        case UInt16(kVK_ANSI_A): return "A"
        case UInt16(kVK_ANSI_B): return "B"
        case UInt16(kVK_ANSI_C): return "C"
        case UInt16(kVK_ANSI_D): return "D"
        case UInt16(kVK_ANSI_E): return "E"
        case UInt16(kVK_ANSI_F): return "F"
        case UInt16(kVK_ANSI_G): return "G"
        case UInt16(kVK_ANSI_H): return "H"
        case UInt16(kVK_ANSI_I): return "I"
        case UInt16(kVK_ANSI_J): return "J"
        case UInt16(kVK_ANSI_K): return "K"
        case UInt16(kVK_ANSI_L): return "L"
        case UInt16(kVK_ANSI_M): return "M"
        case UInt16(kVK_ANSI_N): return "N"
        case UInt16(kVK_ANSI_O): return "O"
        case UInt16(kVK_ANSI_P): return "P"
        case UInt16(kVK_ANSI_Q): return "Q"
        case UInt16(kVK_ANSI_R): return "R"
        case UInt16(kVK_ANSI_S): return "S"
        case UInt16(kVK_ANSI_T): return "T"
        case UInt16(kVK_ANSI_U): return "U"
        case UInt16(kVK_ANSI_V): return "V"
        case UInt16(kVK_ANSI_W): return "W"
        case UInt16(kVK_ANSI_X): return "X"
        case UInt16(kVK_ANSI_Y): return "Y"
        case UInt16(kVK_ANSI_Z): return "Z"
        case UInt16(kVK_ANSI_Equal): return "="
        case UInt16(kVK_ANSI_Minus): return "-"
        case UInt16(kVK_ANSI_RightBracket): return "]"
        case UInt16(kVK_ANSI_LeftBracket): return "["
        case UInt16(kVK_ANSI_Quote): return "'"
        case UInt16(kVK_ANSI_Semicolon): return ";"
        case UInt16(kVK_ANSI_Backslash): return "\\"
        case UInt16(kVK_ANSI_Comma): return ","
        case UInt16(kVK_ANSI_Slash): return "/"
        case UInt16(kVK_ANSI_Period): return "."
        case UInt16(kVK_ANSI_Grave): return "`"
        default: return "Unknown"
        }
    }
}

// Preview needs adjustment if needed, but focus is on implementation first
#Preview {
    struct PreviewWrapper: View {
        @StateObject var workspaceService = WorkspaceService(
            snappingService: WindowSnappingService())

        // Create dummy window infos with a static UUID
        static let dummyMonitorID = UUID(uuidString: "00000000-0000-4000-A000-000000000000")

        let dummyWindowInfos = [
            WindowInfo(
                frame: CGRect(x: 100, y: 100, width: 800, height: 600),
                monitorID: Self.dummyMonitorID,
                appBundleIdentifier: "com.apple.Safari",
                isFullscreen: false
            ),
            WindowInfo(
                frame: CGRect(x: 200, y: 300, width: 600, height: 400),
                monitorID: Self.dummyMonitorID,
                appBundleIdentifier: "com.apple.finder",
                isFullscreen: false
            ),
            WindowInfo(
                frame: CGRect(x: 300, y: 200, width: 700, height: 500),
                monitorID: Self.dummyMonitorID,
                appBundleIdentifier: "com.apple.mail",
                isFullscreen: false
            ),
        ]

        @State var showSheet = true

        var body: some View {
            VStack { Text("Preview Host") }
                .frame(width: 200, height: 100)
                .sheet(isPresented: $showSheet) {
                    SaveWorkspaceView(windowInfos: dummyWindowInfos)
                        .environmentObject(workspaceService)
                }
        }
    }
    return PreviewWrapper()
}
