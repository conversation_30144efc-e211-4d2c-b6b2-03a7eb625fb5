import Combine
import SwiftUI

/// Represents an app window that can be selected for inclusion in a workspace
class AppSelectionItem: Identifiable, Equatable, ObservableObject {
    let id = UUID()
    let windowInfo: WorkspaceWindowInfo
    @Published var isSelected: Bool = true

    // Get a color for an app using the shared AppColorUtility
    var previewColor: Color {
        guard let bundleID = windowInfo.appBundleIdentifier else {
            return Color.gray
        }
        return AppColorUtility.getAppColor(
            bundleID: bundleID,
            windowFrame: windowInfo.frame
        )
    }

    init(windowInfo: WorkspaceWindowInfo, isSelected: Bool = true) {
        self.windowInfo = windowInfo
        self.isSelected = isSelected
    }

    // Computed properties for display
    var appName: String {
        if let bundleID = windowInfo.appBundleIdentifier {
            return getAppName(for: bundleID) ?? bundleID.components(separatedBy: ".").last
                ?? "Unknown"
        }
        return "Unknown App"
    }

    var appIcon: NSImage {
        if let bundleID = windowInfo.appBundleIdentifier,
            let appURL = NSWorkspace.shared.urlForApplication(withBundleIdentifier: bundleID)
        {
            // icon(forFile:) returns NSImage, not optional
            let icon = NSWorkspace.shared.icon(forFile: appURL.path)
            return icon
        }
        return NSImage(named: NSImage.applicationIconName) ?? NSImage()
    }

    var displayText: String {
        if let screenID = windowInfo.monitorID,
            let screenIndex = getScreenIndex(for: screenID)
        {
            return "\(appName) (Display \(screenIndex + 1))"
        }
        return appName
    }

    // This will be used when we want the full view with icon and color dot
    var displayNameView: some View {
        HStack(spacing: 4) {
            // Color dot
            Circle()
                .fill(previewColor)
                .frame(width: 8, height: 8)

            // App name with display number
            Text(displayText)
        }
    }

    // Helper functions
    private func getAppName(for bundleID: String) -> String? {
        if let appURL = NSWorkspace.shared.urlForApplication(withBundleIdentifier: bundleID),
            let appBundle = Bundle(url: appURL),
            let appName = appBundle.infoDictionary?["CFBundleName"] as? String
        {
            return appName
        }
        return nil
    }

    private func getScreenIndex(for screenID: UUID) -> Int? {
        let screens = NSScreen.screens
        for (index, screen) in screens.enumerated() {
            if let screenNumber = screen.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
                as? NSNumber,
                UUID(uuidString: String(screenNumber.intValue)) == screenID
            {
                return index
            }
        }
        return nil
    }

    static func == (lhs: AppSelectionItem, rhs: AppSelectionItem) -> Bool {
        return lhs.id == rhs.id
    }
}
