import Cocoa
import CoreGraphics
import Foundation

class WindowCaptureService {
    private static let logger = LoggingService.shared
    private static let serviceName = "WindowCaptureService"

    // Configuration options
    private static var filterSystemApps = true

    /// Enable or disable system app filtering
    /// - Parameter enabled: Whether to filter out system apps
    static func setSystemAppFiltering(enabled: Bool) {
        filterSystemApps = enabled
    }

    /// Get all current window positions with normalized coordinates
    /// - Returns: Array of WindowInfo objects with normalized coordinates
    static func getCurrentAppsAndWindowPositionsNormalized() -> [WorkspaceWindowInfo] {
        // Force refresh the NSScreen.screens to ensure we have the latest display arrangement
        // This is important after display arrangement changes in macOS
        let _ = NSScreen.screens

        // Get the raw window positions
        let windowInfos = getCurrentAppsAndWindowPositions()
        var normalizedWindowInfos = [WorkspaceWindowInfo]()

        // Get all displays for reference - this will force a refresh of display information
        let displays = WindowLayoutManager.getAllDisplays()

        // Start building the comprehensive log
        var captureLog = """
            ┌─────────────────────────────────────────────────────────────────────────
            │ WINDOW CAPTURE STARTED
            ├─────────────────────────────────────────────────────────────────────────
            │ DISPLAY INFORMATION:
            │ - Found \(displays.count) displays
            │ - Arrangement: \(determineDisplayArrangement(displays: displays))
            │
            │ WINDOW NORMALIZATION:
            │ - Total windows captured: \(windowInfos.count)
            └─────────────────────────────────────────────────────────────────────────
            """

        // Track windows that couldn't be assigned to a display
        var unassignedWindows = 0

        // Normalize each window position relative to its display
        for windowInfo in windowInfos {
            let appName =
                windowInfo.appBundleIdentifier?.components(separatedBy: ".").last ?? "unknown app"

            // Find the best display for this window using percentage-based approach
            guard let displayInfo = WindowLayoutManager.findBestDisplay(for: windowInfo.frame)
            else {
                // If we can't find a display, just use the original window info
                unassignedWindows += 1
                captureLog += """
                    ┌─────────────────────────────────────────────────────────────────────────
                    │ ⚠️ UNASSIGNED WINDOW - \(appName)
                    ├─────────────────────────────────────────────────────────────────────────
                    │ - APP BUNDLE ID: \(windowInfo.appBundleIdentifier ?? "unknown")
                    │ - FRAME: \(windowInfo.frame)
                    │ - Z-ORDER: \(windowInfo.zOrder)
                    │ - FULLSCREEN: \(windowInfo.isFullscreen)
                    └─────────────────────────────────────────────────────────────────────────

                    """

                normalizedWindowInfos.append(windowInfo)
                continue
            }

            // Calculate the percentage of window in this display
            let percentage = WindowLayoutManager.percentageOf(
                windowInfo.frame, withinFrameOfScreen: displayInfo.frame)

            // Create a UUID from the display ID that can be consistently mapped back
            let monitorID = createConsistentUUID(from: displayInfo.id)

            // Normalize the window frame relative to the display's visible frame and collect log information
            let normalizationResult = WindowLayoutManager.normalizeFrameWithLog(
                windowInfo.frame,
                in: displayInfo,
                appName: appName,
                bundleID: windowInfo.appBundleIdentifier,
                windowTitle: nil)

            // Extract the normalized frame from the result
            let normalizedFrame = normalizationResult.normalizedFrame

            // Create a new WindowInfo with the normalized frame and correct monitor ID
            let normalizedWindowInfo = WorkspaceWindowInfo(
                frame: normalizedFrame,
                monitorID: monitorID,
                appBundleIdentifier: windowInfo.appBundleIdentifier,
                isFullscreen: windowInfo.isFullscreen,
                zOrder: windowInfo.zOrder
            )

            normalizedWindowInfos.append(normalizedWindowInfo)

            // Add detailed window information to the log using the normalization result
            captureLog += """
                ┌─────────────────────────────────────────────────────────────────────────
                │ WINDOW NORMALIZATION - \(normalizationResult.appName ?? appName)
                ├─────────────────────────────────────────────────────────────────────────
                │ APP INFO:
                │ - APP NAME: \(normalizationResult.appName ?? appName)
                │ - APP BUNDLE ID: \(normalizationResult.bundleID ?? windowInfo.appBundleIdentifier ?? "unknown")
                │ - ORIGINAL FRAME: minX: \(normalizationResult.originalFrame.minX), minY: \(normalizationResult.originalFrame.minY), width: \(normalizationResult.originalFrame.width), height: \(normalizationResult.originalFrame.height)
                │ - ABSOLUTE OFFSET: dx: \(normalizationResult.dx), dy: \(normalizationResult.dy)
                │ - NORMALIZED FRAME: x: \(normalizedFrame.origin.x), y: \(normalizedFrame.origin.y), width: \(normalizedFrame.width), height: \(normalizedFrame.height)
                │ - Z-ORDER: \(windowInfo.zOrder)
                │ - BELONGS TO DISPLAY ID: \(normalizationResult.displayID) \(displayInfo.isMain ? "(MAIN DISPLAY)" : "")
                │ - PERCENTAGE IN DISPLAY: \(Int(percentage * 100))%
                │ - DISPLAY FRAME: minX: \(normalizationResult.displayFrame.minX), minY: \(normalizationResult.displayFrame.minY), width: \(normalizationResult.displayFrame.width), height: \(normalizationResult.displayFrame.height)
                │ - VISIBLE FRAME: minX: \(normalizationResult.displayVisibleFrame.minX), minY: \(normalizationResult.displayVisibleFrame.minY), width: \(normalizationResult.displayVisibleFrame.width), height: \(normalizationResult.displayVisibleFrame.height)
                │ - SCALE FACTOR: \(normalizationResult.scaleFactor)
                └─────────────────────────────────────────────────────────────────────────

                """
        }

        // Add summary information
        captureLog += """
            ┌─────────────────────────────────────────────────────────────────────────
            │ WINDOW NORMALIZATION SUMMARY
            ├─────────────────────────────────────────────────────────────────────────
            │ - Total displays: \(displays.count)
            │ - Total windows captured: \(windowInfos.count)
            │ - Windows successfully normalized: \(normalizedWindowInfos.count - unassignedWindows)
            │ - Windows without display assignment: \(unassignedWindows)
            └─────────────────────────────────────────────────────────────────────────
            """

        // Log the comprehensive information
        logger.info(captureLog, service: serviceName, category: .workspaces)

        return normalizedWindowInfos
    }

    static func getCurrentAppsAndWindowPositions() -> [WorkspaceWindowInfo] {
        var results = [WorkspaceWindowInfo]()

        // Define options to exclude desktop elements and get only on-screen windows
        // Using .optionOnScreenAboveWindow with kCGNullWindowID gives us windows in z-order (front to back)
        let options: CGWindowListOption = [.excludeDesktopElements, .optionOnScreenAboveWindow]
        guard
            let windowInfos = CGWindowListCopyWorkspaceWindowInfo(options, kCGNullWindowID)
                as? [[String: Any]]
        else {
            return results
        }

        // Filter out non-application windows and system apps to get a cleaner list
        let filteredWindowInfos = windowInfos.filter { windowInfo in
            guard let layer = windowInfo[kCGWindowLayer as String] as? Int else {
                return false
            }

            // Only include normal application windows (layer 0)
            guard layer == 0 else { return false }

            // Get the owner name to filter out system apps
            if filterSystemApps, let ownerName = windowInfo[kCGWindowOwnerName as String] as? String
            {
                // Filter out System Preferences/Settings and other system apps
                let systemAppsToExclude = [
                    "System Preferences",
                    "System Settings",
                    "Finder",
                    "Notification Center",
                    "Control Center",
                ]

                if systemAppsToExclude.contains(ownerName) {
                    // Filter out system app silently
                    return false
                }
            }

            return true
        }

        // Process windows in z-order (z-order 0 is frontmost)
        for (zIndex, windowInfo) in filteredWindowInfos.enumerated() {
            guard windowInfo[kCGWindowOwnerName as String] as? String != nil,
                let boundsDict = windowInfo[kCGWindowBounds as String] as? [String: Any],
                let windowID = windowInfo[kCGWindowNumber as String] as? Int,
                let ownerPID = windowInfo[kCGWindowOwnerPID as String] as? Int
            else {
                continue
            }

            // Convert bounds dictionary to CGRect
            var bounds = CGRect()
            if let cfDict = boundsDict as CFDictionary?,
                CGRectMakeWithDictionaryRepresentation(cfDict, &bounds)
            {
                // Get app bundle identifier
                let appBundleIdentifier = getBundleIdentifier(pid: pid_t(ownerPID)) ?? ""

                // Filter out system apps by bundle identifier if filtering is enabled
                if filterSystemApps && !appBundleIdentifier.isEmpty {
                    let systemBundleIDsToExclude = [
                        "com.apple.systempreferences",
                        "com.apple.finder",
                        "com.apple.systemuiserver",
                        "com.apple.dock",
                        "com.apple.notificationcenterui",
                        "com.apple.controlcenter",
                        "com.apple.preference",
                        "com.apple.systemsettings",
                    ]

                    // Skip this window if it's a system app
                    if systemBundleIDsToExclude.contains(where: {
                        appBundleIdentifier.hasPrefix($0)
                    }) {
                        continue
                    }
                }

                // Check if window is fullscreen
                let isFullscreen = isWindowFullscreen(windowID: CGWindowID(windowID))

                // Find the best display for this window
                let displayInfo = WindowLayoutManager.findBestDisplay(for: bounds)

                // Create a consistent UUID from the display ID
                let monitorID: UUID? = displayInfo.map { createConsistentUUID(from: $0.id) }

                let capturedInfo = WorkspaceWindowInfo(
                    frame: bounds,  // Use the original bounds from CGWindowList
                    monitorID: monitorID,
                    appBundleIdentifier: appBundleIdentifier,
                    isFullscreen: isFullscreen,
                    zOrder: zIndex  // Add z-order information (0 is frontmost)
                )

                results.append(capturedInfo)
            }
        }

        return results
    }

    private static func getBundleIdentifier(pid: pid_t) -> String? {
        let app = NSRunningApplication(processIdentifier: pid)
        return app?.bundleIdentifier
    }

    private static func isWindowFullscreen(windowID: CGWindowID) -> Bool {
        guard
            let window = CGWindowListCopyWorkspaceWindowInfo(
                [.optionIncludingWindow],
                windowID
            ) as? [[String: Any]],
            let firstWindow = window.first,
            let bounds = firstWindow[kCGWindowBounds as String] as? [String: Any],
            let frame = CGRect(dictionaryRepresentation: bounds as CFDictionary)
        else {
            return false
        }

        guard let screen = NSScreen.main else { return false }
        return frame.size == screen.frame.size
    }

    // Helper method to create a consistent UUID from a display ID
    private static func createConsistentUUID(from displayID: CGDirectDisplayID) -> UUID {
        // Convert the display ID to a string
        let displayIDString = String(displayID)

        // Create a UUID by using a fixed pattern with the display ID
        // Format: xxxxxxxx-xxxx-4xxx-axxx-xxxxxxxxxxxx where x is derived from display ID
        let hexString = String(format: "%08x", displayID)
        let uuidString =
            "\(hexString.prefix(8))-\(hexString.prefix(4))-4\(hexString.prefix(3))-a\(hexString.prefix(3))-\(displayIDString.padding(toLength: 12, withPad: "0", startingAt: 0))"

        // Log the UUID creation
        logger.debug(
            "Creating consistent UUID for display ID: \(displayID) -> \(uuidString)",
            service: serviceName,
            category: .workspaces
        )

        // Create UUID from the formatted string, fallback to a random UUID if invalid
        let uuid = UUID(uuidString: uuidString) ?? UUID()

        // Log if we had to fall back to a random UUID
        if UUID(uuidString: uuidString) == nil {
            logger.warning(
                "Failed to create UUID from string: \(uuidString), using random UUID instead: \(uuid.uuidString)",
                service: serviceName,
                category: .workspaces
            )
        }

        return uuid
    }

    /// Determines if displays are arranged horizontally or vertically
    private static func determineDisplayArrangement(displays: [DisplayInfo]) -> String {
        guard displays.count > 1 else { return "Single Display" }

        // Check if displays are primarily arranged horizontally or vertically
        let xDifference =
            displays.map { $0.frame.origin.x }.max()! - displays.map { $0.frame.origin.x }.min()!
        let yDifference =
            displays.map { $0.frame.origin.y }.max()! - displays.map { $0.frame.origin.y }.min()!

        if xDifference > yDifference {
            return "Horizontal"
        } else if yDifference > xDifference {
            return "Vertical"
        } else {
            return "Mixed"
        }
    }
}

// Extension to easily get dictionary representation for CoreGraphics functions
extension CGRect {
    var dictionaryRepresentation: CFDictionary? {
        // Call the actual CoreGraphics function, not the extension itself
        return CGRectCreateDictionaryRepresentation(self)
    }
}

// MARK: - Helper Functions
extension WindowCaptureService {
    /// Helper to determine if screens are arranged vertically
    static func isVerticalScreenArrangement(screens: [NSScreen]) -> Bool {
        guard screens.count > 1 else { return false }

        // Calculate the total width and height of the arrangement
        var minX: CGFloat = .infinity
        var maxX: CGFloat = -.infinity
        var minY: CGFloat = .infinity
        var maxY: CGFloat = -.infinity

        for screen in screens {
            minX = min(minX, screen.frame.minX)
            maxX = max(maxX, screen.frame.maxX)
            minY = min(minY, screen.frame.minY)
            maxY = max(maxY, screen.frame.maxY)
        }

        let totalWidth = maxX - minX
        let totalHeight = maxY - minY

        // Simple approach: if height is greater than width, it's vertical
        let isVertical = totalHeight > totalWidth

        // Create a comprehensive log for screen arrangement detection
        let arrangementLog = """
            SCREEN ARRANGEMENT:

            - Total screens: \(screens.count)
            - Total width: \(totalWidth)
            - Total height: \(totalHeight)
            - Arrangement: \(isVertical ? "Vertical" : "Horizontal")
            """

        // Log the arrangement information
        logger.debug(arrangementLog, service: serviceName, category: .workspaces)

        return isVertical
    }
}
