import AppKit

/// A utility class for monitoring global mouse events
class EventMonitor {
    private var globalMonitor: Any?
    private var localMonitor: Any?
    private let mask: NSEvent.EventTypeMask
    private let handler: (NSEvent) -> Void

    // Logger
    private let logger = LoggingService.shared
    private let serviceName = "EventMonitor"

    init(mask: NSEvent.EventTypeMask, handler: @escaping (NSEvent) -> Void) {
        self.mask = mask
        self.handler = handler
    }

    deinit {
        stop()
    }

    /// Start monitoring events
    func start() {
        guard globalMonitor == nil && localMonitor == nil else { return }

        // Use a combination of global and local monitors to ensure we catch all events
        // Global monitor doesn't receive events when the app is active
        // Local monitor only receives events when the app is active

        // First, create a global monitor for when our app isn't active
        globalMonitor = NSEvent.addGlobalMonitorForEvents(matching: mask, handler: handler)

        // Then, create a local monitor for when our app is active
        localMonitor = NSEvent.addLocalMonitorForEvents(matching: mask) { [handler] event in
            handler(event)
            return event
        }

        logger.debug(
            "Started monitoring events with mask: \(mask.rawValue)",
            service: serviceName,
            category: .general
        )
    }

    /// Stop monitoring events
    func stop() {
        if let monitor = globalMonitor {
            NSEvent.removeMonitor(monitor)
            self.globalMonitor = nil
            logger.debug(
                "Stopped global monitor",
                service: serviceName,
                category: .general
            )
        }

        if let monitor = localMonitor {
            NSEvent.removeMonitor(monitor)
            self.localMonitor = nil
            logger.debug(
                "Stopped local monitor",
                service: serviceName,
                category: .general
            )
        }
    }

    /// Check if the monitor is currently active
    var isActive: Bool {
        return globalMonitor != nil || localMonitor != nil
    }
}
