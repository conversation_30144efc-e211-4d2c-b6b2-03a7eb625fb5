import AppKit
import Foundation

/// Utility class for Stage Manager detection and configuration
class StageUtil {
    /// Whether the system is capable of running Stage Manager
    static var stageCapable: Bool {
        if #available(macOS 13.0, *) {
            return true
        }
        return false
    }
    
    /// Whether Stage Manager is enabled
    static var stageEnabled: Bool {
        return DefaultsManager.shared.stageEnabled
    }
    
    /// Whether the Stage Manager strip is shown
    static var stageStripShow: Bool {
        return true // In a real implementation, this would check system settings
    }
    
    /// Position of the Stage Manager strip
    static var stageStripPosition: StageStripPosition {
        return .left // Default position, could be configurable
    }
}

/// Position of the Stage Manager strip
enum StageStripPosition {
    case left
    case right
}