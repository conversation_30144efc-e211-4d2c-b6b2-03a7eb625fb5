//
//  AppDelegateAdapter.swift
//  OhSnap - Rectangle Integration
//
//  Adapter to bridge Rectangle's AppDelegate pattern with OhSnap's architecture
//

import Foundation

/// Rectangle's AppDelegate adapter that provides the static windowHistory property
/// This allows <PERSON><PERSON><PERSON><PERSON>'s code to work with OhSnap's architecture
class AppDelegate {
    /// Static window history instance to match <PERSON><PERSON>ang<PERSON>'s pattern
    static let windowHistory = WindowHistory()
    
    /// Private initializer to prevent instantiation
    /// This class only provides static access to windowHistory
    private init() {}
}
