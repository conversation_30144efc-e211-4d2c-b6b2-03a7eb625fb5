import AppKit
import CoreGraphics

extension CGRect {
    /// Returns the center point of the rectangle
    var center: CGPoint {
        return CGPoint(x: midX, y: midY)
    }

    /// Flips the Y coordinate for the AX coordinate system
    /// This matches Rectangle's implementation exactly
    var screenFlipped: CGRect {
        guard !isNull else {
            return self
        }
        return .init(
            origin: .init(x: origin.x, y: NSScreen.screens[0].frame.maxY - maxY), size: size)
    }

    /// Returns true if the rectangle is wider than it is tall
    var isLandscape: Bool { width > height }

    /// Returns the number of edges shared with another rectangle
    func numSharedEdges(withRect rect: CGRect) -> Int {
        var sharedEdgeCount = 0
        if minX == rect.minX { sharedEdgeCount += 1 }
        if maxX == rect.maxX { sharedEdgeCount += 1 }
        if minY == rect.minY { sharedEdgeCount += 1 }
        if maxY == rect.maxY { sharedEdgeCount += 1 }
        return sharedEdgeCount
    }
}

extension CGPoint {
    /// Flips the Y coordinate for the AX coordinate system
    /// This matches Rectangle's implementation exactly
    var screenFlipped: CGPoint {
        .init(x: x, y: NSScreen.screens[0].frame.maxY - y)
    }
}
