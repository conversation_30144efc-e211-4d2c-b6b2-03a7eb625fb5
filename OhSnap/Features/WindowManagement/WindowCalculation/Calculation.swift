import AppKit
import Foundation

// Base protocol for all calculations
protocol Calculation {
    func calculateRect(_ params: RectCalculationParameters) -> RectResult
}

// Parameters for rectangle calculations
struct RectCalculationParameters {
    let window: WindowInfo
    let visibleFrameOfScreen: CGRect
    let action: WindowDirection
    let frameOfScreen: CGRect

    init(
        window: WindowInfo, visibleFrameOfScreen: CGRect, action: WindowDirection,
        frameOfScreen: CGRect
    ) {
        self.window = window
        self.visibleFrameOfScreen = visibleFrameOfScreen
        self.action = action
        self.frameOfScreen = frameOfScreen
    }
}

// Result of a rectangle calculation
struct RectResult {
    let rect: CGRect
    let subAction: WindowDirection?

    init(_ rect: CGRect, subAction: WindowDirection? = nil) {
        self.rect = rect
        self.subAction = subAction
    }
}

// Parameters for window calculations
struct WindowCalculationParameters {
    let window: WindowInfo
    let screens: [NSScreen]
    let currentScreen: NSScreen
    let action: WindowDirection

    var usableScreens: UsableScreens {
        return UsableScreens(screens: screens, currentScreen: currentScreen)
    }

    func asRectParams() -> RectCalculationParameters {
        return RectCalculationParameters(
            window: window,
            visibleFrameOfScreen: currentScreen.visibleFrame,
            action: action,
            frameOfScreen: currentScreen.frame
        )
    }
}

// Result of a window calculation
struct WindowCalculationResult {
    let rect: CGRect
    let screen: NSScreen
    let resultingAction: WindowDirection
    let resultingSubAction: WindowDirection?

    init(
        rect: CGRect, screen: NSScreen, resultingAction: WindowDirection,
        resultingSubAction: WindowDirection? = nil
    ) {
        self.rect = rect
        self.screen = screen
        self.resultingAction = resultingAction
        self.resultingSubAction = resultingSubAction
    }
}

// Helper class for screen management
class UsableScreens {
    let screens: [NSScreen]
    let currentScreen: NSScreen
    let logger = LoggingService.shared
    let serviceName = "UsableScreens"

    /// Number of available screens
    var numScreens: Int {
        return screens.count
    }

    init(screens: [NSScreen], currentScreen: NSScreen) {
        self.screens = screens
        self.currentScreen = currentScreen
        logger.debug("Initialized with \(screens.count) screens", service: serviceName)
    }

    /// Get the next screen in the sequence
    func nextScreen() -> NSScreen {
        guard let currentIndex = screens.firstIndex(of: currentScreen), screens.count > 1 else {
            return currentScreen
        }

        let nextIndex = (currentIndex + 1) % screens.count
        logger.debug("Moving from screen \(currentIndex) to \(nextIndex)", service: serviceName)
        return screens[nextIndex]
    }

    /// Get the previous screen in the sequence
    func previousScreen() -> NSScreen {
        guard let currentIndex = screens.firstIndex(of: currentScreen), screens.count > 1 else {
            return currentScreen
        }

        let prevIndex = (currentIndex - 1 + screens.count) % screens.count
        logger.debug("Moving from screen \(currentIndex) to \(prevIndex)", service: serviceName)
        return screens[prevIndex]
    }

    /// Get the adjusted visible frame for a screen, accounting for Stage Manager
    func adjustedVisibleFrame(
        for screen: NSScreen, ignoreTodo: Bool = false, ignoreStage: Bool = false
    ) -> CGRect {
        return screen.adjustedVisibleFrame(ignoreTodo: ignoreTodo, ignoreStage: ignoreStage)
    }

    /// Convert a rect from one screen's coordinate system to another
    func convertRect(_ rect: CGRect, from fromScreen: NSScreen, to toScreen: NSScreen) -> CGRect {
        // Get the global coordinates
        let globalRect = CGRect(
            x: rect.origin.x + fromScreen.frame.origin.x,
            y: rect.origin.y + fromScreen.frame.origin.y,
            width: rect.width,
            height: rect.height
        )

        // Convert to the target screen's coordinate system
        return CGRect(
            x: globalRect.origin.x - toScreen.frame.origin.x,
            y: globalRect.origin.y - toScreen.frame.origin.y,
            width: globalRect.width,
            height: globalRect.height
        )
    }

    /// Scale a rect to account for different display scales
    func scaleRect(_ rect: CGRect, fromScale: CGFloat, toScale: CGFloat) -> CGRect {
        let scaleFactor = toScale / fromScale

        return CGRect(
            x: rect.origin.x * scaleFactor,
            y: rect.origin.y * scaleFactor,
            width: rect.width * scaleFactor,
            height: rect.height * scaleFactor
        )
    }

    /// Get a properly adjusted rect for a secondary display
    func getAdjustedRectForSecondaryDisplay(_ rect: CGRect, screen: NSScreen) -> CGRect {
        // Get the screen's scale factor
        let scaleFactor = screen.backingScaleFactor
        let mainScaleFactor = NSScreen.main?.backingScaleFactor ?? 1.0

        // Calculate the scale ratio between the screens
        let scaleRatio = scaleFactor / mainScaleFactor

        // Log scale information
        logger.debug("Screen scale factor: \(scaleFactor)", service: serviceName)
        logger.debug("Main screen scale factor: \(mainScaleFactor)", service: serviceName)
        logger.debug("Scale ratio: \(scaleRatio)", service: serviceName)

        // Get the visible frame dimensions
        let visibleFrame = screen.visibleFrame
        logger.debug("Visible frame: \(visibleFrame)", service: serviceName)

        // Calculate the adjusted rect
        var adjustedRect = rect

        // Ensure width doesn't exceed visible frame width
        if adjustedRect.width > visibleFrame.width {
            adjustedRect.size.width = visibleFrame.width
        }

        // Ensure height doesn't exceed visible frame height
        if adjustedRect.height > visibleFrame.height {
            adjustedRect.size.height = visibleFrame.height
        }

        // Ensure right edge doesn't exceed visible frame right edge
        if adjustedRect.maxX > visibleFrame.maxX {
            adjustedRect.origin.x = visibleFrame.maxX - adjustedRect.width
        }

        // Ensure bottom edge doesn't exceed visible frame bottom edge
        if adjustedRect.maxY > visibleFrame.maxY {
            adjustedRect.origin.y = visibleFrame.maxY - adjustedRect.height
        }

        // Ensure left edge isn't less than visible frame left edge
        if adjustedRect.minX < visibleFrame.minX {
            adjustedRect.origin.x = visibleFrame.minX
        }

        // Ensure top edge isn't less than visible frame top edge
        if adjustedRect.minY < visibleFrame.minY {
            adjustedRect.origin.y = visibleFrame.minY
        }

        // Ensure the rect is properly aligned with pixel boundaries
        adjustedRect = CGRect(
            x: round(adjustedRect.origin.x),
            y: round(adjustedRect.origin.y),
            width: floor(adjustedRect.width),  // Use floor to prevent exceeding boundaries
            height: floor(adjustedRect.height) // Use floor to prevent exceeding boundaries
        )

        // Log the adjustment
        logger.debug("Original rect: \(rect)", service: serviceName)
        logger.debug("Adjusted rect: \(adjustedRect)", service: serviceName)

        return adjustedRect
    }
}
