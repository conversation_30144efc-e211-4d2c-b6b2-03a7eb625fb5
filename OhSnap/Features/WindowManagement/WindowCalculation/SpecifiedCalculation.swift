import AppKit
import Foundation

class SpecifiedCalculation: WindowCalculation {
    private let specifiedHeight: CGFloat
    private let specifiedWidth: CGFloat

    override init() {
        let defaults = DefaultsManager.shared
        self.specifiedHeight = defaults.specifiedHeight
        self.specifiedWidth = defaults.specifiedWidth
        super.init()
    }

    override func calculateRect(_ params: RectCalculationParameters) -> RectResult {
        let visibleFrameOfScreen = params.visibleFrameOfScreen
        var calculatedWindowRect = visibleFrameOfScreen

        calculatedWindowRect.size.height = specifiedHeight <= 1
            ? visibleFrameOfScreen.height * specifiedHeight
            : round(specifiedHeight)

        calculatedWindowRect.size.width = specifiedWidth <= 1
            ? visibleFrameOfScreen.width * specifiedWidth
            : min(visibleFrameOfScreen.width, round(specifiedWidth))

        // Center the window
        calculatedWindowRect.origin.x = round((visibleFrameOfScreen.width - calculatedWindowRect.width) / 2.0) + visibleFrameOfScreen.minX
        calculatedWindowRect.origin.y = round((visibleFrameOfScreen.height - calculatedWindowRect.height) / 2.0) + visibleFrameOfScreen.minY

        return RectResult(calculatedWindowRect)
    }
}