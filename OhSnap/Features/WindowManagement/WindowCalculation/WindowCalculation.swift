import AppKit
import Foundation

class WindowCalculation: Calculation {
    func calculate(_ params: WindowCalculationParameters) -> WindowCalculationResult? {
        let rectResult = calculateRect(params.asRectParams())
        
        if rectResult.rect.isNull {
            return nil
        }
        
        return WindowCalculationResult(
            rect: rectResult.rect,
            screen: params.usableScreens.currentScreen,
            resultingAction: params.action,
            resultingSubAction: rectResult.subAction
        )
    }
    
    func calculateRect(_ params: RectCalculationParameters) -> RectResult {
        // Default implementation returns empty rect
        // Subclasses should override this
        return RectResult(.null)
    }
}