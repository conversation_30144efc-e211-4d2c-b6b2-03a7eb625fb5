import AppKit
import Foundation

/// Manages snap area configurations for different screen orientations
class SnapAreaModel {
    static let instance = SnapAreaModel()

    private let logger = LoggingService.shared
    private let serviceName = "SnapAreaModel"

    // Default configurations
    static let defaultLandscape: [Directional: SnapAreaConfig] = [
        .topLeft: SnapAreaConfig(action: .topLeftQuarter),
        .top: SnapAreaConfig(action: .topHalf),
        .topRight: SnapAreaConfig(action: .topRightQuarter),
        .left: SnapAreaConfig(action: .leftHalf),
        .right: SnapAreaConfig(action: .rightHalf),
        .bottomLeft: SnapAreaConfig(action: .bottomLeftQuarter),
        .bottom: SnapAreaConfig(action: .bottomHalf),
        .bottomRight: SnapAreaConfig(action: .bottomRightQuarter),
        .center: SnapAreaConfig(action: .maximize),
    ]

    static let defaultPortrait: [Directional: SnapAreaConfig] = [
        .topLeft: SnapAreaConfig(action: .topLeftQuarter),
        .top: SnapAreaConfig(action: .topHalf),
        .topRight: SnapAreaConfig(action: .topRightQuarter),
        .left: SnapAreaConfig(action: .leftHalf),
        .right: SnapAreaConfig(action: .rightHalf),
        .bottomLeft: SnapAreaConfig(action: .bottomLeftQuarter),
        .bottom: SnapAreaConfig(action: .bottomHalf),
        .bottomRight: SnapAreaConfig(action: .bottomRightQuarter),
        .center: SnapAreaConfig(action: .maximize),
    ]

    // User configurations (loaded from UserDefaults)
    private(set) var landscape: [Directional: SnapAreaConfig]
    private(set) var portrait: [Directional: SnapAreaConfig]

    private init() {
        // Load configurations from UserDefaults or use defaults
        self.landscape = UserDefaults.standard.landscapeSnapAreas ?? SnapAreaModel.defaultLandscape
        self.portrait = UserDefaults.standard.portraitSnapAreas ?? SnapAreaModel.defaultPortrait

        logger.info(
            "Initialized with \(landscape.count) landscape and \(portrait.count) portrait configurations",
            service: serviceName)
    }

    func reload() {
        landscape = UserDefaults.standard.landscapeSnapAreas ?? SnapAreaModel.defaultLandscape
        portrait = UserDefaults.standard.portraitSnapAreas ?? SnapAreaModel.defaultPortrait
        logger.info("Reloaded configurations", service: serviceName)
    }
}

// MARK: - UserDefaults Extension
extension UserDefaults {
    private enum Keys {
        static let landscapeSnapAreas = "landscapeSnapAreas"
        static let portraitSnapAreas = "portraitSnapAreas"
    }

    var landscapeSnapAreas: [Directional: SnapAreaConfig]? {
        get {
            guard let data = data(forKey: Keys.landscapeSnapAreas) else { return nil }
            do {
                let decoder = JSONDecoder()
                let rawDict = try decoder.decode([String: SnapAreaConfig].self, from: data)
                return rawDict.compactMapKeys { Int($0).flatMap { Directional(rawValue: $0) } }
            } catch {
                print("Error decoding landscape snap areas: \(error)")
                return nil
            }
        }
        set {
            guard let newValue = newValue else {
                removeObject(forKey: Keys.landscapeSnapAreas)
                return
            }

            do {
                let rawDict = newValue.mapKeys { String($0.rawValue) }
                let encoder = JSONEncoder()
                let data = try encoder.encode(rawDict)
                set(data, forKey: Keys.landscapeSnapAreas)
            } catch {
                print("Error encoding landscape snap areas: \(error)")
            }
        }
    }

    var portraitSnapAreas: [Directional: SnapAreaConfig]? {
        get {
            guard let data = data(forKey: Keys.portraitSnapAreas) else { return nil }
            do {
                let decoder = JSONDecoder()
                let rawDict = try decoder.decode([String: SnapAreaConfig].self, from: data)
                return rawDict.compactMapKeys { Int($0).flatMap { Directional(rawValue: $0) } }
            } catch {
                print("Error decoding portrait snap areas: \(error)")
                return nil
            }
        }
        set {
            guard let newValue = newValue else {
                removeObject(forKey: Keys.portraitSnapAreas)
                return
            }

            do {
                let rawDict = newValue.mapKeys { String($0.rawValue) }
                let encoder = JSONEncoder()
                let data = try encoder.encode(rawDict)
                set(data, forKey: Keys.portraitSnapAreas)
            } catch {
                print("Error encoding portrait snap areas: \(error)")
            }
        }
    }
}

// MARK: - Dictionary Extensions
extension Dictionary {
    func mapKeys<T>(_ transform: (Key) -> T) -> [T: Value] {
        var result: [T: Value] = [:]
        for (key, value) in self {
            result[transform(key)] = value
        }
        return result
    }

    func compactMapKeys<T>(_ transform: (Key) -> T?) -> [T: Value] {
        var result: [T: Value] = [:]
        for (key, value) in self {
            if let transformedKey = transform(key) {
                result[transformedKey] = value
            }
        }
        return result
    }
}

// MARK: - NSScreen Extension
extension NSScreen {
    var isLandscape: Bool {
        return frame.width >= frame.height
    }

    var isPortrait: Bool {
        return frame.height > frame.width
    }

    /// Returns true if any connected display is in portrait orientation
    static var portraitDisplayConnected: Bool {
        NSScreen.screens.contains(where: { !$0.frame.isLandscape })
    }

    /// Get the adjusted visible frame, accounting for Stage Manager
    func adjustedVisibleFrame(ignoreTodo: Bool = false, ignoreStage: Bool = false) -> CGRect {
        var newFrame = visibleFrame

        // Handle Stage Manager strip if enabled
        if !ignoreStage && DefaultsManager.shared.stageSize > 0 {
            if StageUtil.stageCapable && StageUtil.stageEnabled && StageUtil.stageStripShow {
                let stageSize =
                    DefaultsManager.shared.stageSize < 1
                    ? newFrame.size.width * DefaultsManager.shared.stageSize
                    : DefaultsManager.shared.stageSize

                if StageUtil.stageStripPosition == .left {
                    newFrame.origin.x += stageSize
                }
                newFrame.size.width -= stageSize
            }
        }

        return newFrame
    }
}
