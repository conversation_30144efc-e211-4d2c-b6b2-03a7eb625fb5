import AppKit
import Carbon

/// Manages window snapping by tracking mouse events and detecting snap areas
@MainActor
class SnappingManager: @unchecked Sendable {
    // MARK: - Properties

    // Event monitoring
    private var eventMonitor: EventMonitor?
    private var localModifierMonitor: Any?
    private var globalModifierMonitor: Any?

    // Window tracking
    private var windowElement: AXUIElement?
    private var windowId: CGWindowID?
    private var windowMoving: Bool = false
    private var initialWindowRect: CGRect?

    // Snap operation state
    private var isPerformingSnap: Bool = false
    private var lastSnapTime: Date = Date.distantPast

    // Snap area detection
    private var currentSnapArea: SnapArea?

    // Visual feedback
    private var box: FootprintWindow?

    // Dependencies
    private let windowSnappingService: WindowSnappingService
    private let logger = LoggingService.shared
    private let serviceName = "SnappingManager"

    // Configuration
    private var enabled: Bool = true
    private var snapModifiers: NSEvent.ModifierFlags?
    private var avoidSystemConflicts: Bool = true

    // Native macOS feature detection
    private var lastMouseLocation: NSPoint = .zero
    private var potentialSystemConflict: Bool = false

    // MARK: - Initialization

    init(windowSnappingService: WindowSnappingService) {
        self.windowSnappingService = windowSnappingService

        // Load configuration from UserDefaults
        loadConfiguration()

        // Create footprint window
        box = FootprintWindow()

        // Setup event monitoring
        setupEventMonitoring()

        logger.info(
            "Initialized with snap modifiers: \(String(describing: snapModifiers))",
            service: serviceName)
    }

    deinit {
        // Stop all monitors
        eventMonitor?.stop()

        // For the modifier monitors, we need to handle them differently due to MainActor isolation
        if let monitor = localModifierMonitor {
            NSEvent.removeMonitor(monitor)
            localModifierMonitor = nil
        }

        if let monitor = globalModifierMonitor {
            NSEvent.removeMonitor(monitor)
            globalModifierMonitor = nil
        }
    }

    // MARK: - Configuration

    private func loadConfiguration() {
        // Load enabled state
        enabled = UserDefaults.standard.bool(forKey: "dragToSnapEnabled")

        // Load snap modifiers
        if let modifierValue = UserDefaults.standard.object(forKey: "snapModifiers") as? UInt {
            snapModifiers = NSEvent.ModifierFlags(rawValue: modifierValue)
        } else {
            snapModifiers = nil
        }

        // Load avoid system conflicts setting (default to true if not set)
        if UserDefaults.standard.object(forKey: "avoidSystemConflicts") == nil {
            UserDefaults.standard.set(true, forKey: "avoidSystemConflicts")
            avoidSystemConflicts = true
        } else {
            avoidSystemConflicts = UserDefaults.standard.bool(forKey: "avoidSystemConflicts")
        }
    }

    func setEnabled(_ enabled: Bool) {
        self.enabled = enabled
        UserDefaults.standard.set(enabled, forKey: "dragToSnapEnabled")

        if enabled {
            startMonitoring()
        } else {
            stopMonitoring()
        }

        logger.info("Drag-to-snap \(enabled ? "enabled" : "disabled")", service: serviceName)
    }

    func setSnapModifiers(_ modifiers: NSEvent.ModifierFlags?) {
        self.snapModifiers = modifiers
        UserDefaults.standard.set(modifiers?.rawValue, forKey: "snapModifiers")

        // Restart the modifier monitor with the new modifiers
        if enabled {
            startModifierMonitor()
        }

        logger.info("Snap modifiers set to: \(String(describing: modifiers))", service: serviceName)
    }

    func setAvoidSystemConflicts(_ avoid: Bool) {
        self.avoidSystemConflicts = avoid
        UserDefaults.standard.set(avoid, forKey: "avoidSystemConflicts")
        logger.info("Avoid system conflicts set to: \(avoid)", service: serviceName)
    }

    // MARK: - Event Monitoring

    private func setupEventMonitoring() {
        eventMonitor = EventMonitor(mask: [.leftMouseDown, .leftMouseUp, .leftMouseDragged]) {
            [weak self] event in
            self?.handle(event: event)
        }

        if enabled {
            startMonitoring()
        }
    }

    private func startMonitoring() {
        eventMonitor?.start()
        startModifierMonitor()
        logger.info("Started event monitoring", service: serviceName)
    }

    private func stopMonitoring() {
        eventMonitor?.stop()
        stopModifierMonitor()
        logger.info("Stopped event monitoring", service: serviceName)
    }

    private func startModifierMonitor() {
        // Stop any existing monitors first
        stopModifierMonitor()

        // Only start if we have modifiers to monitor
        guard let requiredModifiers = snapModifiers, !requiredModifiers.isEmpty else {
            return
        }

        // Create a local monitor for modifier key changes (when our app is active)
        localModifierMonitor = NSEvent.addLocalMonitorForEvents(matching: .flagsChanged) {
            [weak self] event in
            guard let self = self else { return event }

            // Check if we're in the middle of a drag operation
            if self.windowMoving && self.currentSnapArea != nil {
                // Get current modifier flags
                let currentFlags = event.modifierFlags.intersection(.deviceIndependentFlagsMask)

                // Check if the required modifiers are still pressed
                if !currentFlags.contains(requiredModifiers) {
                    self.logger.debug(
                        "Modifier key released (local), canceling snap", service: self.serviceName)

                    // Reset state on the main thread
                    DispatchQueue.main.async {
                        self.resetState()
                    }
                }
            }

            return event
        }

        // Create a global monitor for modifier key changes (when our app is not active)
        globalModifierMonitor = NSEvent.addGlobalMonitorForEvents(matching: .flagsChanged) { [weak self] event in
            guard let self = self else { return }

            // Check if we're in the middle of a drag operation
            if self.windowMoving && self.currentSnapArea != nil {
                // Get current modifier flags
                let currentFlags = event.modifierFlags.intersection(.deviceIndependentFlagsMask)

                // Check if the required modifiers are still pressed
                if !currentFlags.contains(requiredModifiers) {
                    self.logger.debug("Modifier key released (global), canceling snap", service: self.serviceName)

                    // Reset state on the main thread
                    DispatchQueue.main.async {
                        self.resetState()
                    }
                }
            }
        }

        logger.debug("Started modifier key monitors", service: serviceName)
    }

    private func stopModifierMonitor() {
        // Stop local monitor
        if let monitor = localModifierMonitor {
            NSEvent.removeMonitor(monitor)
            localModifierMonitor = nil
        }

        // Stop global monitor
        if let monitor = globalModifierMonitor {
            NSEvent.removeMonitor(monitor)
            globalModifierMonitor = nil
        }

        logger.debug("Stopped modifier key monitors", service: serviceName)
    }

    // MARK: - Event Handling

    private func handle(event: NSEvent) {
        // Check if drag-to-snap is enabled
        guard enabled else { return }

        // Check if modifier keys are required and pressed
        if let requiredModifiers = snapModifiers {
            let currentModifiers = event.modifierFlags.intersection(.deviceIndependentFlagsMask)
            guard currentModifiers.contains(requiredModifiers) else { return }
        }

        switch event.type {
        case .leftMouseDown:
            handleMouseDown()

        case .leftMouseDragged:
            handleMouseDragged()

        case .leftMouseUp:
            handleMouseUp()

        default:
            break
        }
    }

    private func handleMouseDown() {
        // Check if we're already performing a snap operation
        if isPerformingSnap {
            logger.debug("Ignoring mouse down while performing snap", service: serviceName)
            return
        }

        // Check if we've snapped recently (within 300ms)
        let now = Date()
        let timeSinceLastSnap = now.timeIntervalSince(lastSnapTime)
        if timeSinceLastSnap < 0.3 {
            logger.debug(
                "Ignoring mouse down too soon after last snap (\(timeSinceLastSnap)s)",
                service: serviceName)
            return
        }

        // Reset state
        resetState()

        // Reset conflict detection state
        lastMouseLocation = .zero
        potentialSystemConflict = false

        // Log the mouse down event
        logger.debug("Mouse down detected", service: serviceName)

        // Get window under cursor with retry
        windowElement = getWindowUnderCursorWithRetry()

        // Get window ID and initial rect
        if let element = windowElement {
            windowId = AccessibilityElement.getWindowID(element)
            logger.debug(
                "Found window under cursor with ID: \(windowId ?? 0)", service: serviceName)

            // Get initial window rect using a detached task
            Task.detached { [weak self, element] in
                guard let self = self else { return }
                do {
                    let frame = try await AccessibilityElement().getFrame(element)

                    // Log the initial frame
                    let serviceName = self.serviceName

                    // Update on main thread
                    await MainActor.run {
                        self.initialWindowRect = frame
                        self.logger.debug("Initial window frame: \(frame)", service: serviceName)
                    }
                } catch {
                    let errorMessage = "Failed to get initial window rect: \(error)"
                    let serviceName = self.serviceName
                    await MainActor.run {
                        self.logger.error(errorMessage, service: serviceName)
                    }
                }
            }
        } else {
            logger.warning("No window found under cursor after retries", service: serviceName)
        }
    }

    private func handleMouseDragged() {
        guard let element = windowElement, let initialRect = initialWindowRect else { return }

        // Check if window is moving using a detached task
        Task.detached { [weak self, element, initialRect] in
            guard let self = self else { return }
            do {
                let currentRect = try await AccessibilityElement().getFrame(element)

                // Capture values that will be needed inside the MainActor block
                let serviceName = self.serviceName

                // Update on main thread
                await MainActor.run {
                    // Detect if window has started moving
                    if !self.windowMoving && currentRect.origin != initialRect.origin {
                        self.windowMoving = true
                        self.logger.debug("Window started moving", service: serviceName)
                    }

                    // If window is moving, check for snap areas
                    if self.windowMoving {
                        // Get the current mouse location
                        let mouseLocation = NSEvent.mouseLocation
                        self.logger.debug("Mouse location: \(mouseLocation)", service: serviceName)

                        // Check if modifier keys are required and still pressed
                        if let requiredModifiers = self.snapModifiers, !requiredModifiers.isEmpty {
                            // Get current modifier flags
                            let currentFlags = NSEvent.modifierFlags

                            // Check if the required modifiers are still pressed
                            if !currentFlags.contains(requiredModifiers) {
                                self.logger.debug(
                                    "Modifier key released during drag, canceling snap",
                                    service: serviceName)

                                // Hide any existing footprint
                                if self.currentSnapArea != nil {
                                    self.currentSnapArea = nil
                                    self.box?.hide(animated: true)
                                }

                                return
                            }
                        }

                        // Check for potential conflicts with macOS native features
                        self.detectPotentialSystemConflict(mouseLocation: mouseLocation)

                        // Store the current mouse location for the next frame
                        self.lastMouseLocation = mouseLocation

                        // If we detect a potential conflict with macOS, don't try to snap
                        if self.potentialSystemConflict {
                            self.logger.debug(
                                "Potential conflict with macOS detected, avoiding snap",
                                service: serviceName)

                            // Hide any existing footprint
                            if self.currentSnapArea != nil {
                                self.currentSnapArea = nil
                                self.box?.hide(animated: true)
                            }

                            return
                        }

                        // Check if we're near a screen edge
                        let newSnapArea = self.snapAreaContainingCursor(
                            priorSnapArea: self.currentSnapArea)

                        // If snap area changed, update visual feedback
                        if newSnapArea != self.currentSnapArea {
                            // Log the change
                            if let oldArea = self.currentSnapArea {
                                self.logger.debug(
                                    "Leaving snap area: \(oldArea.directional.description)",
                                    service: serviceName
                                )
                            }

                            if let newArea = newSnapArea {
                                self.logger.debug(
                                    "Entering snap area: \(newArea.directional.description)",
                                    service: serviceName
                                )
                            }

                            // Update the current snap area
                            self.currentSnapArea = newSnapArea

                            if let snapArea = newSnapArea {
                                // Calculate target frame
                                let targetFrame = self.calculateTargetFrame(for: snapArea)

                                // Show visual feedback with animation
                                self.box?.show(at: targetFrame, animated: true)
                                self.logger.debug(
                                    "Showing footprint for \(snapArea.directional.description)",
                                    service: serviceName
                                )
                            } else {
                                // Hide visual feedback with animation
                                self.box?.hide(animated: true)
                                self.logger.debug("Hiding footprint", service: serviceName)
                            }
                        }
                    }
                }
            } catch {
                let errorMessage = "Failed to get current window rect: \(error)"
                let serviceName = self.serviceName
                await MainActor.run {
                    self.logger.error(errorMessage, service: serviceName)
                }
            }
        }
    }

    private func handleMouseUp() {
        // Check if we're already performing a snap operation
        guard !isPerformingSnap else {
            logger.debug("Ignoring mouse up while performing snap", service: serviceName)
            return
        }

        // Check if we've snapped recently (within 500ms)
        let now = Date()
        let timeSinceLastSnap = now.timeIntervalSince(lastSnapTime)
        if timeSinceLastSnap < 0.5 {
            logger.debug(
                "Ignoring mouse up too soon after last snap (\(timeSinceLastSnap)s)",
                service: serviceName)
            return
        }

        // Check if modifier keys are required and still pressed
        if let requiredModifiers = self.snapModifiers, !requiredModifiers.isEmpty {
            // Get current modifier flags
            let currentFlags = NSEvent.modifierFlags

            // Check if the required modifiers are still pressed
            if !currentFlags.contains(requiredModifiers) {
                logger.debug(
                    "Modifier key not pressed during mouse up, canceling snap", service: serviceName
                )
                resetState()
                return
            }
        }

        // If window was moving and we have a snap area, perform the snap
        if windowMoving, let snapArea = currentSnapArea, let element = windowElement {
            // Store a local reference to the element to avoid it being cleared
            let elementToSnap = element

            // Mark that we're performing a snap
            isPerformingSnap = true
            lastSnapTime = now

            // Log the snap action
            logger.info(
                "Snapping window to \(snapArea.directional.description)",
                service: serviceName
            )

            // Perform the snap
            performSnap(element: elementToSnap, to: snapArea)
        } else {
            // If we're not snapping, just reset the state
            resetState()
        }
    }

    /// Reset all state variables to their default values
    private func resetState() {
        // Reset state
        windowMoving = false
        currentSnapArea = nil
        windowElement = nil
        windowId = nil
        initialWindowRect = nil
        isPerformingSnap = false

        // Reset conflict detection state
        potentialSystemConflict = false

        // Hide visual feedback
        box?.hide()

        // Log state reset
        logger.debug("Reset state after operation", service: serviceName)
    }

    // MARK: - Window Detection

    /// Gets the window under the cursor with multiple retries
    private func getWindowUnderCursorWithRetry() -> AXUIElement? {
        // First attempt
        if let window = AccessibilityElement.getWindowElementUnderCursor() {
            logger.debug("Found window under cursor on first attempt", service: serviceName)
            return window
        }

        // If first attempt fails, wait a tiny bit and try again (up to 3 more times)
        for i in 1...3 {
            // Small delay between attempts
            Thread.sleep(forTimeInterval: 0.05 * Double(i))

            if let window = AccessibilityElement.getWindowElementUnderCursor() {
                logger.debug("Found window under cursor on retry #\(i)", service: serviceName)
                return window
            }
        }

        // If all attempts fail, try one last time with a slightly longer delay
        Thread.sleep(forTimeInterval: 0.2)
        if let window = AccessibilityElement.getWindowElementUnderCursor() {
            logger.debug("Found window under cursor on final retry", service: serviceName)
            return window
        }

        logger.warning(
            "Failed to find window under cursor after multiple attempts", service: serviceName)
        return nil
    }

    // MARK: - System Conflict Detection

    /// Detects potential conflicts with macOS native window management features
    private func detectPotentialSystemConflict(mouseLocation: NSPoint) {
        // Reset the conflict flag
        potentialSystemConflict = false

        // If conflict avoidance is disabled, don't do any detection
        if !avoidSystemConflicts {
            return
        }

        // If this is the first mouse location, we can't detect velocity
        if lastMouseLocation == .zero {
            return
        }

        // Calculate mouse velocity (how fast the mouse is moving)
        let deltaX = mouseLocation.x - lastMouseLocation.x
        let deltaY = mouseLocation.y - lastMouseLocation.y
        let velocity = sqrt(deltaX * deltaX + deltaY * deltaY)

        // Check if mouse is moving very quickly (potential flick gesture)
        let isHighVelocity = velocity > 50.0  // Threshold for "fast" mouse movement

        // Check if mouse is near the top of any screen (potential Mission Control trigger)
        var isNearScreenTop = false
        for screen in NSScreen.screens {
            let topEdgeY = screen.frame.maxY
            let distanceFromTop = abs(topEdgeY - mouseLocation.y)
            if distanceFromTop < 20 {  // Within 20 pixels of top edge
                isNearScreenTop = true
                break
            }
        }

        // Check if mouse is near the menu bar (potential menu interaction)
        let isNearMenuBar = mouseLocation.y > (NSScreen.main?.frame.maxY ?? 0) - 25

        if let mainScreen = NSScreen.main {
            let isMovingTowardLeft = deltaX < -20 && mouseLocation.x < mainScreen.frame.minX + 50
            let isMovingTowardRight = deltaX > 20 && mouseLocation.x > mainScreen.frame.maxX - 50
            let isMovingTowardTop = deltaY > 20 && isNearScreenTop
            let isMovingTowardBottom = deltaY < -20 && mouseLocation.y < mainScreen.frame.minY + 50

            if isMovingTowardLeft || isMovingTowardRight || isMovingTowardTop
                || isMovingTowardBottom
            {
                // Mouse is moving quickly toward a screen edge
                if isHighVelocity {
                    logger.debug("Detected fast movement toward screen edge", service: serviceName)
                    potentialSystemConflict = true
                    return
                }
            }
        }

        // Detect potential Mission Control activation
        if isNearScreenTop && deltaY > 10 {
            logger.debug("Detected potential Mission Control activation", service: serviceName)
            potentialSystemConflict = true
            return
        }

        // Detect potential menu bar interaction
        if isNearMenuBar {
            logger.debug("Detected potential menu bar interaction", service: serviceName)
            potentialSystemConflict = true
            return
        }

        // Detect potential Stage Manager interaction (if mouse moves to left/right edge quickly)
        if isHighVelocity
            && (mouseLocation.x < 20 || mouseLocation.x > (NSScreen.main?.frame.maxX ?? 0) - 20)
        {
            logger.debug("Detected potential Stage Manager interaction", service: serviceName)
            potentialSystemConflict = true
            return
        }
    }

    // MARK: - Snap Area Detection

    private func snapAreaContainingCursor(priorSnapArea: SnapArea?) -> SnapArea? {
        let mouseLocation = NSEvent.mouseLocation

        for screen in NSScreen.screens {
            guard
                let directional = directionalLocationOfCursor(
                    location: mouseLocation, screen: screen)
            else {
                continue
            }

            // Get configuration based on screen orientation
            let config =
                screen.isLandscape
                ? SnapAreaModel.instance.landscape[directional]
                : SnapAreaModel.instance.portrait[directional]

            if let action = config?.action {
                return SnapArea(screen: screen, directional: directional, action: action)
            }
        }

        return nil
    }

    private func directionalLocationOfCursor(location: NSPoint, screen: NSScreen) -> Directional? {
        // Get screen frame
        let frame = screen.frame

        // Check if cursor is in screen
        guard frame.contains(location) else { return nil }

        // Calculate relative position in screen (0.0 to 1.0)
        let relX = (location.x - frame.minX) / frame.width
        let relY = (location.y - frame.minY) / frame.height

        // Determine directional based on position
        // Screen is divided into a 3x3 grid

        // Top row
        if relY >= 0.67 {
            if relX < 0.33 { return .topLeft }
            if relX < 0.67 { return .top }
            return .topRight
        }

        // Middle row
        if relY >= 0.33 {
            if relX < 0.33 { return .left }
            if relX < 0.67 { return .center }
            return .right
        }

        // Bottom row
        if relX < 0.33 { return .bottomLeft }
        if relX < 0.67 { return .bottom }
        return .bottomRight
    }

    // MARK: - Snapping

    private func calculateTargetFrame(for snapArea: SnapArea) -> CGRect {
        let screen = snapArea.screen
        let direction = snapArea.action

        // Create a dummy WindowInfo for calculation
        let dummyInfo = WindowInfo(
            frame: .zero,
            monitorID: nil,
            appBundleIdentifier: nil,
            isFullscreen: false
        )

        // Use WindowCalculationService to calculate the target frame
        let calculationService = WindowCalculationService()
        return calculationService.calculateWindowRect(
            for: direction,
            window: dummyInfo,
            screen: screen
        )
    }

    private func performSnap(element: AXUIElement, to snapArea: SnapArea) {
        // Create a new task for the snap operation
        Task {
            do {
                // Convert directional to window direction
                let direction = snapArea.action

                // Log the snap operation
                logger.debug(
                    "Starting snap operation to \(snapArea.directional.description) on screen \(snapArea.screen)",
                    service: serviceName
                )

                // Use WindowMover to move the window
                let windowMover = WindowMover()
                try await windowMover.moveWindow(element, to: direction, on: snapArea.screen)

                // Log success
                logger.info(
                    "Successfully snapped window to \(snapArea.directional.description)",
                    service: serviceName
                )

                // Add a small delay to ensure the window has time to move before the next drag operation
                try await Task.sleep(nanoseconds: 300_000_000)  // 300ms

                // Ensure we're ready for the next snap operation
                await MainActor.run {
                    // Reset state using our dedicated method
                    resetState()

                    logger.debug("Snap operation completed and state reset", service: serviceName)
                }
            } catch {
                logger.error("Failed to snap window: \(error)", service: serviceName)

                // Ensure we're ready for the next snap operation even if this one failed
                await MainActor.run {
                    // Reset state using our dedicated method
                    resetState()

                    logger.debug("Snap operation failed but state reset", service: serviceName)
                }
            }
        }
    }
}
