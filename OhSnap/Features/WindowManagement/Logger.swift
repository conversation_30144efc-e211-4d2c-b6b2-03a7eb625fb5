//
//  Logger.swift
//  OhSnap - Rectangle Integration
//
//  Adapter to bridge Rectangle's Logger with OhSnap's LoggingService
//

import Foundation

/// Rectangle's Logger adapter that uses OhSnap's LoggingService
class Logger {
    static var logging = true // Always enabled in OhSnap
    
    private static let loggingService = LoggingService.shared
    private static let serviceName = "Rectangle"
    
    static func log(_ string: String) {
        if logging {
            loggingService.info(string, service: serviceName)
        }
    }
    
    static func showLogging(sender: Any?) {
        // In OhSnap, logging is handled through the main logging system
        // This method is kept for Rectangle compatibility but doesn't need implementation
        log("Rectangle logging requested - using OhSnap's logging system")
    }
}
