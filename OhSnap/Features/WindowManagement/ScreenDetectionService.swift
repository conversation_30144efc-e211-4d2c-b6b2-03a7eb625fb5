import AppKit

// Adapted from Rectangle (https://github.com/rxhanson/Rectangle) under GPL-3.0
class ScreenDetectionService: ScreenDetectable {
    private let logger = LoggingService.shared
    private let serviceName = "ScreenDetectionService"

    func getScreenContaining(_ rect: CGRect) -> NSScreen? {
        let screens = NSScreen.screens

        logger.debug(
            "Finding screen containing rect: \(rect)", service: serviceName,
            category: .screenDetection)
        logger.debug(
            "Available screens: \(screens.count)", service: serviceName, category: .screenDetection)

        // Log information about all screens
        for (index, screen) in screens.enumerated() {
            logger.debug(
                "Screen \(index) frame: \(screen.frame)", service: serviceName,
                category: .screenDetection)
            logger.debug(
                "Screen \(index) visible frame: \(screen.visibleFrame)", service: serviceName,
                category: .screenDetection)

            if let screenNumber = screen.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
                as? NSNumber
            {
                logger.debug(
                    "Screen \(index) number: \(screenNumber)", service: serviceName,
                    category: .screenDetection)
            }

            if screen == NSScreen.main {
                logger.debug(
                    "Screen \(index) is main screen", service: serviceName,
                    category: .screenDetection)
            }
        }

        // Use Rectangle's approach for screen detection
        var result: NSScreen? = NSScreen.main
        var largestPercentageOfRectWithinFrameOfScreen: CGFloat = 0.0

        // Convert to flipped coordinates for screen detection
        let normalizedRect = rect.screenFlipped

        logger.debug(
            "Normalized rect for screen detection: \(normalizedRect)", service: serviceName,
            category: .screenDetection)

        // First check if any screen fully contains the rect
        for (index, screen) in screens.enumerated() {
            let currentFrameOfScreen = screen.frame

            if currentFrameOfScreen.contains(normalizedRect) {
                logger.debug(
                    "Screen \(index) fully contains the rect", service: serviceName,
                    category: .screenDetection)
                result = screen
                break
            }

            // If no screen fully contains the rect, find the screen with the largest percentage
            let percentageOfRectWithinCurrentFrameOfScreen = percentageOf(normalizedRect, withinFrameOfScreen: currentFrameOfScreen)

            logger.debug(
                "Screen \(index) contains \(percentageOfRectWithinCurrentFrameOfScreen * 100)% of the rect",
                service: serviceName,
                category: .screenDetection)

            if percentageOfRectWithinCurrentFrameOfScreen > largestPercentageOfRectWithinFrameOfScreen {
                largestPercentageOfRectWithinFrameOfScreen = percentageOfRectWithinCurrentFrameOfScreen
                result = screen
                logger.debug(
                    "New best screen by percentage: \(index)", service: serviceName,
                    category: .screenDetection)
            }
        }

        if let screenNumber = result?.deviceDescription[
            NSDeviceDescriptionKey("NSScreenNumber")] as? NSNumber
        {
            logger.debug(
                "Selected screen, number: \(screenNumber)", service: serviceName,
                category: .screenDetection)
        }

        return result
    }

    /// Get all available screens
    func getAllScreens() -> [NSScreen] {
        let screens = NSScreen.screens

        logger.debug(
            "Getting all screens: \(screens.count) available", service: serviceName,
            category: .screenDetection)

        // Log information about all screens
        for (index, screen) in screens.enumerated() {
            if let screenNumber = screen.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
                as? NSNumber
            {
                logger.debug(
                    "Screen \(index) number: \(screenNumber)", service: serviceName,
                    category: .screenDetection)
            }

            if screen == NSScreen.main {
                logger.debug(
                    "Screen \(index) is main screen", service: serviceName,
                    category: .screenDetection)
            }
        }

        return screens
    }

    /// Calculate the percentage of a rectangle that is within a screen's frame
    /// This matches Rectangle's implementation
    private func percentageOf(_ rect: CGRect, withinFrameOfScreen frameOfScreen: CGRect) -> CGFloat
    {
        let intersectionOfRectAndFrameOfScreen = rect.intersection(frameOfScreen)
        if intersectionOfRectAndFrameOfScreen.isNull {
            return 0.0
        }

        let areaOfRect = rect.width * rect.height
        if areaOfRect == 0.0 {
            return 0.0
        }

        let areaOfIntersectionOfRectAndFrameOfScreen =
            intersectionOfRectAndFrameOfScreen.width * intersectionOfRectAndFrameOfScreen.height
        return areaOfIntersectionOfRectAndFrameOfScreen / areaOfRect
    }
}
