//
//  RectangleWindowSnappingBridge.swift
//  OhSnap - Rectangle Integration
//
//  Bridge to connect Rectangle's WindowManager with OhSnap's existing interfaces
//

import AppKit
import Foundation

/// Bridge service that adapts Rectangle's WindowManager to OhSnap's WindowSnappingService interface
/// This allows OhSnap's workspace functionality to work with Rectangle's window management
class RectangleWindowSnappingBridge {
    private let windowManager: WindowManager
    private let logger = LoggingService.shared
    private let serviceName = "RectangleWindowSnappingBridge"
    
    init(windowManager: WindowManager) {
        self.windowManager = windowManager
        logger.info("Initialized Rectangle window snapping bridge", service: serviceName)
    }
    
    /// Snap the frontmost window to the specified position using Rectangle's system
    func snapFrontmostWindow(to position: SnapPosition) {
        logger.info("Snapping frontmost window to \(position)", service: serviceName)
        
        // Convert OhSnap's SnapPosition to Rectangle's WindowAction
        guard let windowAction = convertSnapPositionToWindowAction(position) else {
            logger.warning("Unable to convert snap position \(position) to Rectangle action", service: serviceName)
            return
        }
        
        // Execute using Rectangle's WindowManager
        let executionParameters = ExecutionParameters(windowAction)
        windowManager.execute(executionParameters)
        
        logger.info("Successfully executed Rectangle action \(windowAction.name)", service: serviceName)
    }
    
    /// Convert OhSnap's SnapPosition enum to Rectangle's WindowAction enum
    private func convertSnapPositionToWindowAction(_ position: SnapPosition) -> WindowAction? {
        switch position {
        case .leftHalf:
            return .leftHalf
        case .rightHalf:
            return .rightHalf
        case .topHalf:
            return .topHalf
        case .bottomHalf:
            return .bottomHalf
        case .fullscreen:
            return .maximize
        case .leftThird:
            return .firstThird
        case .centerThird:
            return .centerThird
        case .rightThird:
            return .lastThird
        case .topLeftQuarter:
            return .topLeft
        case .topRightQuarter:
            return .topRight
        case .bottomLeftQuarter:
            return .bottomLeft
        case .bottomRightQuarter:
            return .bottomRight
        case .leftTwoThirds:
            return .firstTwoThirds
        case .rightTwoThirds:
            return .lastTwoThirds
        case .center:
            return .center
        }
    }
}
