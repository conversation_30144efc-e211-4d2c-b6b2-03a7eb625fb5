import AppKit
import Foundation

/// Represents adjacent screens in a multi-display setup
class AdjacentScreens {
    let prev: NSScreen
    let current: NSScreen
    let next: NSScreen
    
    init(prev: NSScreen, current: NSScreen, next: NSScreen) {
        self.prev = prev
        self.current = current
        self.next = next
    }
}

/// Extension to UsableScreens to handle adjacent screens
extension UsableScreens {
    /// Get adjacent screens (previous, current, next)
    var adjacentScreens: AdjacentScreens? {
        guard numScreens > 1 else { return nil }
        
        guard let currentIndex = screens.firstIndex(of: currentScreen) else {
            return nil
        }
        
        let prevIndex = (currentIndex - 1 + numScreens) % numScreens
        let nextIndex = (currentIndex + 1) % numScreens
        
        return AdjacentScreens(
            prev: screens[prevIndex],
            current: currentScreen,
            next: screens[nextIndex]
        )
    }
    
    /// Get the screen to the left of the current screen
    func screenToLeft() -> NSScreen? {
        guard numScreens > 1 else { return nil }
        
        // Find screens that are to the left of the current screen
        let screensToLeft = screens.filter { screen in
            // A screen is to the left if its right edge is to the left of the current screen's left edge
            let screenMaxX = screen.frame.maxX
            let currentMinX = currentScreen.frame.minX
            return screenMaxX <= currentMinX
        }
        
        // Find the rightmost screen among those to the left
        return screensToLeft.max(by: { $0.frame.maxX < $1.frame.maxX })
    }
    
    /// Get the screen to the right of the current screen
    func screenToRight() -> NSScreen? {
        guard numScreens > 1 else { return nil }
        
        // Find screens that are to the right of the current screen
        let screensToRight = screens.filter { screen in
            // A screen is to the right if its left edge is to the right of the current screen's right edge
            let screenMinX = screen.frame.minX
            let currentMaxX = currentScreen.frame.maxX
            return screenMinX >= currentMaxX
        }
        
        // Find the leftmost screen among those to the right
        return screensToRight.min(by: { $0.frame.minX < $1.frame.minX })
    }
    
    /// Get the screen above the current screen
    func screenAbove() -> NSScreen? {
        guard numScreens > 1 else { return nil }
        
        // Find screens that are above the current screen
        let screensAbove = screens.filter { screen in
            // A screen is above if its bottom edge is above the current screen's top edge
            let screenMinY = screen.frame.minY
            let currentMaxY = currentScreen.frame.maxY
            return screenMinY >= currentMaxY
        }
        
        // Find the bottommost screen among those above
        return screensAbove.min(by: { $0.frame.minY < $1.frame.minY })
    }
    
    /// Get the screen below the current screen
    func screenBelow() -> NSScreen? {
        guard numScreens > 1 else { return nil }
        
        // Find screens that are below the current screen
        let screensBelow = screens.filter { screen in
            // A screen is below if its top edge is below the current screen's bottom edge
            let screenMaxY = screen.frame.maxY
            let currentMinY = currentScreen.frame.minY
            return screenMaxY <= currentMinY
        }
        
        // Find the topmost screen among those below
        return screensBelow.max(by: { $0.frame.maxY < $1.frame.maxY })
    }
}