import AppKit
import Carbon.HIToolbox
import KeyboardShortcuts

extension KeyboardShortcuts.Name {
    // Window positioning shortcuts - Based on the image provided
    // Left, Right, Top, Bottom
    static let leftHalf = Self(
        "leftHalf", default: .init(.leftArrow, modifiers: [.control, .option]))
    static let rightHalf = Self(
        "rightHalf", default: .init(.rightArrow, modifiers: [.control, .option]))
    static let topHalf = Self("topHalf", default: .init(.upArrow, modifiers: [.control, .option]))
    static let bottomHalf = Self(
        "bottomHalf", default: .init(.downArrow, modifiers: [.control, .option]))

    // Quarters
    static let topLeftQuarter = Self(
        "topLeftQuarter", default: .init(.u, modifiers: [.control, .option]))
    static let topRightQuarter = Self(
        "topRightQuarter", default: .init(.i, modifiers: [.control, .option]))
    static let bottomLeftQuarter = Self(
        "bottomLeftQuarter", default: .init(.j, modifiers: [.control, .option]))
    static let bottomRightQuarter = Self(
        "bottomRightQuarter", default: .init(.k, modifiers: [.control, .option]))

    // Thirds
    static let leftThird = Self("leftThird", default: .init(.d, modifiers: [.control, .option]))
    static let centerThird = Self(
        "centerThird", default: .init(.f, modifiers: [.control, .option]))
    static let rightThird = Self("rightThird", default: .init(.g, modifiers: [.control, .option]))

    // Two Thirds
    static let leftTwoThirds = Self(
        "leftTwoThirds", default: .init(.e, modifiers: [.control, .option]))
    static let centerTwoThirds = Self(
        "centerTwoThirds", default: .init(.r, modifiers: [.control, .option]))
    static let rightTwoThirds = Self(
        "rightTwoThirds", default: .init(.t, modifiers: [.control, .option]))

    // Fullscreen (Maximize)
    static let fullscreen = Self(
        "fullscreen", default: .init(.return, modifiers: [.control, .option]))

    // Helper to get all predefined shortcuts
    static var allPredefinedShortcuts: [Self] {
        return [
            .leftHalf, .rightHalf, .topHalf, .bottomHalf,
            .topLeftQuarter, .topRightQuarter, .bottomLeftQuarter, .bottomRightQuarter,
            .leftThird, .centerThird, .rightThird,
            .leftTwoThirds, .centerTwoThirds, .rightTwoThirds,
            .fullscreen,
        ]
    }

    // Helper to create workspace shortcuts dynamically
    static func workspaceShortcut(for id: UUID) -> Self {
        return Self("workspace_\(id.uuidString)")
    }

    // Helper to extract workspace ID from a shortcut name
    static func workspaceId(from name: Self) -> UUID? {
        let nameString = String(describing: name)
        if nameString.starts(with: "workspace_") {
            let components = nameString.split(separator: "_")
            if components.count > 1, let idString = components.last {
                return UUID(uuidString: String(idString))
            }
        }
        return nil
    }

    // Helper to get the action name from a shortcut name
    static func actionName(from name: Self) -> String? {
        // Check if it's a predefined shortcut
        for shortcut in allPredefinedShortcuts {
            if shortcut == name {
                return String(describing: shortcut).replacingOccurrences(
                    of: "KeyboardShortcuts.Name.", with: "")
            }
        }

        // Check if it's a workspace shortcut
        let nameString = String(describing: name)
        if nameString.starts(with: "workspace_") {
            return nameString
        }

        return nil
    }
}

// Helper to get all shortcuts including workspace shortcuts
extension KeyboardShortcuts.Name {
    static func getAllShortcuts() -> [Self] {
        var cases = allPredefinedShortcuts

        // Add workspace shortcuts
        if let appDelegate = NSApplication.shared.delegate as? AppDelegate {
            let workspaces = appDelegate.workspaceService.workspaces
            for workspace in workspaces {
                if let id = workspace.id {
                    cases.append(workspaceShortcut(for: id))
                }
            }
        }

        return cases
    }
}
