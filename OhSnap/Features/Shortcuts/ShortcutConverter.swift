import AppKit
import Carbon.HIToolbox
import Foundation
import KeyboardShortcuts
import MASShortcut

/// Utility class for converting between different shortcut formats
public class ShortcutConverter {
    /// Convert a Shortcut to a KeyboardShortcuts.Shortcut
    /// - Parameter shortcut: The shortcut to convert
    /// - Returns: The KeyboardShortcuts.Shortcut
    public static func toKeyboardShortcut(_ shortcut: Shortcut) -> KeyboardShortcuts.Shortcut? {
        let key = KeyboardShortcuts.Key(rawValue: shortcut.keyCode)
        return KeyboardShortcuts.Shortcut(key, modifiers: shortcut.modifiers)
    }

    /// Convert a KeyboardShortcuts.Shortcut to a Shortcut
    /// - Parameter keyboardShortcut: The KeyboardShortcuts.Shortcut to convert
    /// - Returns: The Shortcut
    public static func fromKeyboardShortcut(_ keyboardShortcut: KeyboardShortcuts.Shortcut)
        -> Shortcut?
    {
        guard let key = keyboardShortcut.key else { return nil }
        return Shortcut(keyCode: key.rawValue, modifiers: keyboardShortcut.modifiers)
    }

    /// Convert a Shortcut to a MASShortcut
    /// - Parameter shortcut: The shortcut to convert
    /// - Returns: The MASShortcut
    public static func toMASShortcut(_ shortcut: Shortcut) -> MASShortcut {
        return MASShortcut(keyCode: shortcut.keyCode, modifierFlags: shortcut.modifiers)
    }

    /// Convert a MASShortcut to a Shortcut
    /// - Parameter masShortcut: The MASShortcut to convert
    /// - Returns: The Shortcut
    public static func fromMASShortcut(_ masShortcut: MASShortcut) -> Shortcut {
        return Shortcut(keyCode: Int(masShortcut.keyCode), modifiers: masShortcut.modifierFlags)
    }

    /// Convert a KeyboardShortcuts.Shortcut to a MASShortcut
    /// - Parameter keyboardShortcut: The KeyboardShortcuts.Shortcut to convert
    /// - Returns: The MASShortcut
    public static func keyboardShortcutToMASShortcut(_ keyboardShortcut: KeyboardShortcuts.Shortcut)
        -> MASShortcut?
    {
        guard let key = keyboardShortcut.key else { return nil }
        return MASShortcut(keyCode: key.rawValue, modifierFlags: keyboardShortcut.modifiers)
    }

    /// Convert a MASShortcut to a KeyboardShortcuts.Shortcut
    /// - Parameter masShortcut: The MASShortcut to convert
    /// - Returns: The KeyboardShortcuts.Shortcut
    public static func masShortcutToKeyboardShortcut(_ masShortcut: MASShortcut)
        -> KeyboardShortcuts.Shortcut?
    {
        let key = KeyboardShortcuts.Key(rawValue: Int(masShortcut.keyCode))
        return KeyboardShortcuts.Shortcut(key, modifiers: masShortcut.modifierFlags)
    }

    /// Get a string representation of a shortcut
    /// - Parameter shortcut: The shortcut
    /// - Returns: The string representation
    public static func shortcutToString(_ shortcut: Shortcut) -> String? {
        return formatShortcut(
            keyCode: UInt16(shortcut.keyCode), modifiers: shortcut.modifiers.rawValue)
    }

    /// Get a string representation of a KeyboardShortcuts.Shortcut
    /// - Parameter keyboardShortcut: The KeyboardShortcuts.Shortcut
    /// - Returns: The string representation
    public static func keyboardShortcutToString(_ keyboardShortcut: KeyboardShortcuts.Shortcut)
        -> String?
    {
        guard let key = keyboardShortcut.key else { return nil }
        return formatShortcut(
            keyCode: UInt16(key.rawValue), modifiers: keyboardShortcut.modifiers.rawValue)
    }

    /// Get a string representation of a MASShortcut
    /// - Parameter masShortcut: The MASShortcut
    /// - Returns: The string representation
    public static func masShortcutToString(_ masShortcut: MASShortcut) -> String? {
        return formatShortcut(
            keyCode: UInt16(masShortcut.keyCode), modifiers: masShortcut.modifierFlags.rawValue)
    }
}
