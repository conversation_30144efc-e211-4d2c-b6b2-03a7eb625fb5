import AppKit
import Carbon.HIToolbox
import Foundation
import MASShortcut

// MARK: - Shortcut Model

/// A platform-agnostic representation of a keyboard shortcut
public struct Shortcut: Equatable, Hashable, Codable {
    /// The key code of the shortcut
    public let keyCode: Int

    /// The modifier flags of the shortcut (stored as raw value for Codable)
    private let _modifiers: UInt

    /// The modifier flags of the shortcut
    public var modifiers: NSEvent.ModifierFlags {
        return NSEvent.ModifierFlags(rawValue: _modifiers)
    }

    /// Initialize a new shortcut
    /// - Parameters:
    ///   - keyCode: The key code
    ///   - modifiers: The modifier flags
    public init(keyCode: Int, modifiers: NSEvent.ModifierFlags) {
        self.keyCode = keyCode
        self._modifiers = modifiers.rawValue
    }

    /// Initialize a shortcut from an NSEvent
    /// - Parameter event: The event to create the shortcut from
    public init?(event: NSEvent) {
        guard event.type == .keyDown || event.type == .keyUp else { return nil }
        self.keyCode = Int(event.keyCode)
        self._modifiers = event.modifierFlags.rawValue
    }

    /// Get a string representation of the shortcut
    public var displayString: String? {
        formatShortcut(keyCode: UInt16(keyCode), modifiers: _modifiers)
    }

    // MARK: - Codable Implementation

    private enum CodingKeys: String, CodingKey {
        case keyCode
        case modifiers = "_modifiers"
    }

    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        keyCode = try container.decode(Int.self, forKey: .keyCode)
        _modifiers = try container.decode(UInt.self, forKey: .modifiers)
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(keyCode, forKey: .keyCode)
        try container.encode(_modifiers, forKey: .modifiers)
    }
}

// MARK: - Shortcut Validation Result

/// Result of validating a shortcut
public struct ShortcutValidationResult {
    /// Whether the shortcut is valid
    public let isValid: Bool

    /// The reason why the shortcut is invalid, if any
    public let invalidReason: String?

    /// The application that conflicts with the shortcut, if any
    public let conflictingApplication: String?

    /// Initialize a new validation result
    /// - Parameters:
    ///   - isValid: Whether the shortcut is valid
    ///   - invalidReason: The reason why the shortcut is invalid, if any
    ///   - conflictingApplication: The application that conflicts with the shortcut, if any
    public init(isValid: Bool, invalidReason: String? = nil, conflictingApplication: String? = nil)
    {
        self.isValid = isValid
        self.invalidReason = invalidReason
        self.conflictingApplication = conflictingApplication
    }
}

// MARK: - Shortcut Manager Protocol

/// Protocol defining the interface for a shortcut manager
public protocol ShortcutManagerProtocol {
    /// Register a shortcut with a name and action
    /// - Parameters:
    ///   - shortcut: The shortcut to register
    ///   - name: The name of the shortcut
    ///   - action: The action to perform when the shortcut is triggered
    func registerShortcut(_ shortcut: Shortcut, forName name: String, action: @escaping () -> Void)

    /// Unregister a shortcut by name
    /// - Parameter name: The name of the shortcut to unregister
    func unregisterShortcut(forName name: String)

    /// Unregister all shortcuts
    func unregisterAllShortcuts()

    /// Get a shortcut by name
    /// - Parameter name: The name of the shortcut
    /// - Returns: The shortcut, if any
    func getShortcut(forName name: String) -> Shortcut?

    /// Set a shortcut for a name
    /// - Parameters:
    ///   - shortcut: The shortcut to set
    ///   - name: The name of the shortcut
    func setShortcut(_ shortcut: Shortcut?, forName name: String)

    /// Reset a shortcut to its default value
    /// - Parameter name: The name of the shortcut
    func resetShortcut(forName name: String)

    /// Reset all shortcuts to their default values
    func resetAllShortcuts()

    /// Validate a shortcut
    /// - Parameter shortcut: The shortcut to validate
    /// - Returns: The validation result
    func validateShortcut(_ shortcut: Shortcut) -> ShortcutValidationResult

    /// Check if a shortcut conflicts with system shortcuts
    /// - Parameter shortcut: The shortcut to check
    /// - Returns: Whether the shortcut conflicts with system shortcuts and an explanation if it does
    func checkSystemConflicts(for shortcut: Shortcut) -> (Bool, String?)

    /// Check if a shortcut conflicts with other applications
    /// - Parameter shortcut: The shortcut to check
    /// - Returns: Whether the shortcut conflicts with other applications and an explanation if it does
    func checkApplicationConflicts(for shortcut: Shortcut) -> (Bool, String?)
}

// MARK: - Shortcut Manager Implementation

/// A manager for keyboard shortcuts using MASShortcut
public class ShortcutManager {
    /// Shared instance of the shortcut manager
    public static let shared = ShortcutManager()

    /// The underlying implementation
    private let implementation: ShortcutManagerProtocol

    /// Initialize a new shortcut manager
    /// - Parameter implementation: The implementation to use
    private init(implementation: ShortcutManagerProtocol = MASShortcutManager()) {
        self.implementation = implementation
    }

    /// Register a shortcut with a name and action
    /// - Parameters:
    ///   - shortcut: The shortcut to register
    ///   - name: The name of the shortcut
    ///   - action: The action to perform when the shortcut is triggered
    public func registerShortcut(
        _ shortcut: Shortcut, forName name: String, action: @escaping () -> Void
    ) {
        implementation.registerShortcut(shortcut, forName: name, action: action)
    }

    /// Unregister a shortcut by name
    /// - Parameter name: The name of the shortcut to unregister
    public func unregisterShortcut(forName name: String) {
        implementation.unregisterShortcut(forName: name)
    }

    /// Unregister all shortcuts
    public func unregisterAllShortcuts() {
        implementation.unregisterAllShortcuts()
    }

    /// Get a shortcut by name
    /// - Parameter name: The name of the shortcut
    /// - Returns: The shortcut, if any
    public func getShortcut(forName name: String) -> Shortcut? {
        implementation.getShortcut(forName: name)
    }

    /// Set a shortcut for a name
    /// - Parameters:
    ///   - shortcut: The shortcut to set
    ///   - name: The name of the shortcut
    public func setShortcut(_ shortcut: Shortcut?, forName name: String) {
        implementation.setShortcut(shortcut, forName: name)
    }

    /// Reset a shortcut to its default value
    /// - Parameter name: The name of the shortcut
    public func resetShortcut(forName name: String) {
        implementation.resetShortcut(forName: name)
    }

    /// Reset all shortcuts to their default values
    public func resetAllShortcuts() {
        implementation.resetAllShortcuts()
    }

    /// Validate a shortcut
    /// - Parameter shortcut: The shortcut to validate
    /// - Returns: The validation result
    public func validateShortcut(_ shortcut: Shortcut) -> ShortcutValidationResult {
        implementation.validateShortcut(shortcut)
    }

    /// Check if a shortcut conflicts with system shortcuts
    /// - Parameter shortcut: The shortcut to check
    /// - Returns: Whether the shortcut conflicts with system shortcuts and an explanation if it does
    public func checkSystemConflicts(for shortcut: Shortcut) -> (Bool, String?) {
        implementation.checkSystemConflicts(for: shortcut)
    }

    /// Check if a shortcut conflicts with other applications
    /// - Parameter shortcut: The shortcut to check
    /// - Returns: Whether the shortcut conflicts with other applications and an explanation if it does
    public func checkApplicationConflicts(for shortcut: Shortcut) -> (Bool, String?) {
        implementation.checkApplicationConflicts(for: shortcut)
    }
}
