import AppKit
import Carbon.HIToolbox
import Foundation
import KeyboardShortcuts
import MASShortcut
import SwiftUI

/// A SwiftUI view that wraps KeyboardShortcuts.Recorder
public struct ShortcutRecorder: View {
    /// The name of the shortcut
    private let name: KeyboardShortcuts.Name

    /// The label for the recorder
    private let label: String?

    /// The action to perform when the shortcut changes
    private let onChange: ((Shortcut?) -> Void)?

    /// Initialize a new shortcut recorder
    /// - Parameters:
    ///   - label: The label for the recorder
    ///   - name: The name of the shortcut
    ///   - onChange: The action to perform when the shortcut changes
    public init(
        _ label: String? = nil,
        name: KeyboardShortcuts.Name,
        onChange: ((Shortcut?) -> Void)? = nil
    ) {
        self.label = label
        self.name = name
        self.onChange = onChange
    }

    public var body: some View {
        HStack {
            if let label = label {
                Text(label)
                    .frame(minWidth: 100, alignment: .leading)
            }

            KeyboardShortcuts.Recorder(for: name) { shortcut in
                if let shortcut = shortcut,
                    let wrappedShortcut = ShortcutConverter.fromKeyboardShortcut(shortcut)
                {
                    onChange?(wrappedShortcut)
                } else {
                    onChange?(nil)
                }
            }
        }
    }
}

/// A SwiftUI view that displays a shortcut
public struct ShortcutDisplay: View {
    /// The shortcut to display
    private let shortcut: Shortcut?

    /// The placeholder to display when there's no shortcut
    private let placeholder: String

    /// Initialize a new shortcut tag
    /// - Parameters:
    ///   - shortcut: The shortcut to display
    ///   - placeholder: The placeholder to display when there's no shortcut
    public init(shortcut: Shortcut?, placeholder: String = "Not Set") {
        self.shortcut = shortcut
        self.placeholder = placeholder
    }

    public var body: some View {
        Text(shortcut?.displayString ?? placeholder)
            .font(.system(.body, design: .monospaced))
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color(.controlBackgroundColor))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 4)
                    .stroke(Color(.separatorColor), lineWidth: 1)
            )
    }
}

/// A SwiftUI view that displays a row with a shortcut recorder
public struct ShortcutRecorderRow: View {
    /// The name of the shortcut
    private let name: String

    /// The action name for the shortcut
    private let action: KeyboardShortcuts.Name

    /// The action to perform when the shortcut is reset
    private let onReset: (() -> Void)?

    /// Initialize a new shortcut row
    /// - Parameters:
    ///   - name: The name of the shortcut
    ///   - action: The action name for the shortcut
    ///   - onReset: The action to perform when the shortcut is reset
    public init(
        name: String,
        action: KeyboardShortcuts.Name,
        onReset: (() -> Void)? = nil
    ) {
        self.name = name
        self.action = action
        self.onReset = onReset
    }

    public var body: some View {
        HStack {
            Text(name)
                .frame(minWidth: 100, alignment: .leading)

            Spacer()

            KeyboardShortcuts.Recorder(for: action)

            Menu {
                Button("Reset to Default") {
                    KeyboardShortcuts.reset(action)
                    onReset?()
                }

                Button("Clear Shortcut") {
                    KeyboardShortcuts.disable(action)
                    onReset?()
                }
            } label: {
                Image(systemName: "ellipsis")
                    .frame(width: 24, height: 24)
                    .contentShape(Rectangle())
            }
            .menuStyle(.borderlessButton)
            .frame(width: 24, height: 24)
        }
        .padding(.vertical, 4)
    }
}
