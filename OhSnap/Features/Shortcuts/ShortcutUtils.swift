import AppKit  // For NSEvent
import Carbon  // For TIS functions and Key Codes
import Foundation

// Define missing TIS property key manually
let kTISPropertyKeyboardType = "TISPropertyKeyboardType" as CFString

// Define constants if Carbon framework is not explicitly linked
private let kUCKeyActionDown: Int = 0
private let kUCKeyTranslateNoDeadKeysMask: Int = 1

// Global helper function to format shortcut for display
func formatShortcut(keyCode: UInt16, modifiers: UInt) -> String? {
    var displayString = ""
    let modifierFlags = NSEvent.ModifierFlags(rawValue: modifiers)

    // Order matters for standard display
    if modifierFlags.contains(.control) { displayString += "⌃" }  // Control
    if modifierFlags.contains(.option) { displayString += "⌥" }  // Option
    if modifierFlags.contains(.shift) { displayString += "⇧" }  // Shift
    if modifierFlags.contains(.command) { displayString += "⌘" }  // Command

    // Get character representation for the key code
    if let chars = keycodeToString(keyCode) {
        displayString += chars.uppercased()
    } else {
        // Fallback for non-character keys (like arrows, function keys)
        // This part might need more specific handling for common non-character keys
        displayString += "[\(keyCode)]"  // Simple fallback
    }

    // Don't return just modifiers if keycodeToString failed
    return displayString.count > modifierFlags.symbolicCharacterCount ? displayString : nil
}

// Global helper to convert keycode to string (simplified)
private func keycodeToString(_ keyCode: UInt16) -> String? {
    switch keyCode {
    // Arrow keys
    case UInt16(kVK_LeftArrow): return "←"
    case UInt16(kVK_RightArrow): return "→"
    case UInt16(kVK_UpArrow): return "↑"
    case UInt16(kVK_DownArrow): return "↓"

    // Letters
    case UInt16(kVK_ANSI_A): return "A"
    case UInt16(kVK_ANSI_B): return "B"
    case UInt16(kVK_ANSI_C): return "C"
    case UInt16(kVK_ANSI_D): return "D"
    case UInt16(kVK_ANSI_E): return "E"
    case UInt16(kVK_ANSI_F): return "F"
    case UInt16(kVK_ANSI_G): return "G"
    case UInt16(kVK_ANSI_H): return "H"
    case UInt16(kVK_ANSI_I): return "I"
    case UInt16(kVK_ANSI_J): return "J"
    case UInt16(kVK_ANSI_K): return "K"
    case UInt16(kVK_ANSI_L): return "L"
    case UInt16(kVK_ANSI_M): return "M"
    case UInt16(kVK_ANSI_N): return "N"
    case UInt16(kVK_ANSI_O): return "O"
    case UInt16(kVK_ANSI_P): return "P"
    case UInt16(kVK_ANSI_Q): return "Q"
    case UInt16(kVK_ANSI_R): return "R"
    case UInt16(kVK_ANSI_S): return "S"
    case UInt16(kVK_ANSI_T): return "T"
    case UInt16(kVK_ANSI_U): return "U"
    case UInt16(kVK_ANSI_V): return "V"
    case UInt16(kVK_ANSI_W): return "W"
    case UInt16(kVK_ANSI_X): return "X"
    case UInt16(kVK_ANSI_Y): return "Y"
    case UInt16(kVK_ANSI_Z): return "Z"

    // Numbers
    case UInt16(kVK_ANSI_0): return "0"
    case UInt16(kVK_ANSI_1): return "1"
    case UInt16(kVK_ANSI_2): return "2"
    case UInt16(kVK_ANSI_3): return "3"
    case UInt16(kVK_ANSI_4): return "4"
    case UInt16(kVK_ANSI_5): return "5"
    case UInt16(kVK_ANSI_6): return "6"
    case UInt16(kVK_ANSI_7): return "7"
    case UInt16(kVK_ANSI_8): return "8"
    case UInt16(kVK_ANSI_9): return "9"

    // Function keys
    case UInt16(kVK_F1): return "F1"
    case UInt16(kVK_F2): return "F2"
    case UInt16(kVK_F3): return "F3"
    case UInt16(kVK_F4): return "F4"
    case UInt16(kVK_F5): return "F5"
    case UInt16(kVK_F6): return "F6"
    case UInt16(kVK_F7): return "F7"
    case UInt16(kVK_F8): return "F8"
    case UInt16(kVK_F9): return "F9"
    case UInt16(kVK_F10): return "F10"
    case UInt16(kVK_F11): return "F11"
    case UInt16(kVK_F12): return "F12"

    // Special keys
    case UInt16(kVK_Space): return "Space"
    case UInt16(kVK_Return): return "⏎"
    case UInt16(kVK_Tab): return "⇥"
    case UInt16(kVK_Delete): return "⌫"
    case UInt16(kVK_Escape): return "⎋"
    case UInt16(kVK_ForwardDelete): return "⌦"
    case UInt16(kVK_Home): return "↖"
    case UInt16(kVK_End): return "↘"
    case UInt16(kVK_PageUp): return "⇞"
    case UInt16(kVK_PageDown): return "⇟"

    default: return nil
    }
}

// Global helper function to get keyboard type, handling potential nil values
private func GetCurrentKeyboardType() -> Int {
    let defaultKeyboardType = 0
    guard let source = TISCopyCurrentKeyboardInputSource().takeRetainedValue() as TISInputSource?,
        // Use the manually defined CFString constant
        let keyboardTypePtr = TISGetInputSourceProperty(source, kTISPropertyKeyboardType)
    else {
        print("Warning: Could not get keyboard type property pointer. Using default.")
        return defaultKeyboardType
    }

    // Safely convert the pointer to a CFTypeRef before checking its type ID
    let keyboardTypeCF = Unmanaged<CFTypeRef>.fromOpaque(keyboardTypePtr).takeUnretainedValue()

    if CFGetTypeID(keyboardTypeCF) == CFNumberGetTypeID() {
        // Now safely cast to CFNumber (bridged to NSNumber)
        let keyboardType = keyboardTypeCF as! CFNumber  // Force cast safe after type check
        return (keyboardType as NSNumber).intValue
    } else {
        print(
            "Warning: Keyboard type property was not a CFNumber (\(CFGetTypeID(keyboardTypeCF))). Using default."
        )
        return defaultKeyboardType
    }
}

// Global helper to check if a keycode represents a modifier key itself
// Note: Need to import Carbon.HIToolbox or define these constants
func isModifierKeyCode(_ keyCode: UInt16) -> Bool {
    switch Int(keyCode) {
    // Use constants from Carbon/HIToolbox/Events.h
    case kVK_Command, kVK_Shift, kVK_Option, kVK_Control, kVK_RightCommand, kVK_RightShift,
        kVK_RightOption, kVK_RightControl, kVK_CapsLock, kVK_Function:
        return true
    default:
        return false
    }
}

// Extension to count modifier symbols (for formatShortcut)
extension NSEvent.ModifierFlags {
    var symbolicCharacterCount: Int {
        var count = 0
        if contains(.control) { count += 1 }
        if contains(.option) { count += 1 }
        if contains(.shift) { count += 1 }
        if contains(.command) { count += 1 }
        return count
    }
}
