import AppKit
import Carbon
import Foundation
import os.log

/// Class to check for conflicting window management applications
class ConflictingAppsChecker {
    static let shared = ConflictingAppsChecker()

    /// Logger
    private let logger = LoggingService.shared
    private let serviceName = "ConflictingAppsChecker"

    // Known conflicting window management applications
    private let conflictingAppsIds: [String: String] = [
        "com.divisiblebyzero.Spectacle": "Spectacle",
        "com.crowdcafe.windowmagnet": "Magnet",
        "com.hegenberg.BetterSnapTool": "BetterSnapTool",
        "com.manytricks.Moom": "Moom",
        "com.knollsoft.Rectangle": "Rectangle",
    ]

    // Cache of running conflicting apps to avoid repeated checks
    private var runningConflictingApps: [String] = []
    private var lastCheckTime: Date = Date.distantPast

    // Known problematic applications with drag-to-snap
    private let problematicAppBundleIds: [String] = [
        "com.mathworks.matlab",
        "com.live2d.cubism.CECubismEditorApp",
        "com.aquafold.datastudio.DataStudio",
        "com.adobe.illustrator",
        "com.adobe.AfterEffects",
    ]

    // Java-based apps with dynamic bundle IDs
    private let problematicJavaAppNames: [String] = [
        "thinkorswim",
        "Trader Workstation",
    ]

    /// Check for other window management applications that might conflict with OhSnap
    /// - Returns: Tuple with (found conflict, app name)
    func checkForConflictingApps() -> (Bool, String?) {
        let runningApps = NSWorkspace.shared.runningApplications

        for app in runningApps {
            guard let bundleId = app.bundleIdentifier else { continue }

            if let conflictingAppName = conflictingAppsIds[bundleId] {
                return (true, conflictingAppName)
            }
        }

        return (false, nil)
    }

    /// Check for applications known to have issues with drag-to-snap
    /// - Returns: Tuple with (found problematic app, app name)
    func checkForProblematicApps() -> (Bool, String?) {
        let runningApps = NSWorkspace.shared.runningApplications

        // Check for known problematic apps by bundle ID
        for app in runningApps {
            guard let bundleId = app.bundleIdentifier else { continue }

            if problematicAppBundleIds.contains(bundleId) {
                return (true, app.localizedName ?? bundleId)
            }

            // Check for Java-based apps by name
            if let appName = app.localizedName,
                problematicJavaAppNames.contains(where: { appName.contains($0) })
            {
                return (true, appName)
            }
        }

        return (false, nil)
    }

    /// Check for conflicts with macOS built-in tiling features (macOS 15+)
    /// - Returns: True if there's a conflict with macOS tiling
    @available(macOS 15.0, *)
    func checkForBuiltInTilingConflict() -> Bool {
        // This is a placeholder for macOS 15+ tiling conflict detection
        // In a real implementation, we would check system settings
        return false
    }

    /// Get a list of running conflicting window management applications
    /// - Returns: Array of app names that might conflict with OhSnap
    func getRunningConflictingApps() -> [String] {
        // Only refresh the cache every 5 seconds to avoid performance issues
        let now = Date()
        if now.timeIntervalSince(lastCheckTime) > 5.0 {
            refreshRunningConflictingApps()
            lastCheckTime = now
        }

        return runningConflictingApps
    }

    /// Refresh the cache of running conflicting apps
    private func refreshRunningConflictingApps() {
        runningConflictingApps = []
        let runningApps = NSWorkspace.shared.runningApplications

        for app in runningApps {
            guard let bundleId = app.bundleIdentifier else { continue }

            if let conflictingAppName = conflictingAppsIds[bundleId] {
                runningConflictingApps.append(conflictingAppName)
                logger.debug(
                    "Found running conflicting app: \(conflictingAppName)",
                    service: serviceName,
                    category: .shortcuts
                )
            }
        }
    }

    /// Check if a shortcut might conflict with other window management applications
    /// - Parameters:
    ///   - keyCode: The key code of the shortcut
    ///   - modifiers: The modifier flags of the shortcut
    /// - Returns: Tuple with (has conflict, conflicting app name)
    func checkShortcutForConflicts(keyCode: Int, modifiers: NSEvent.ModifierFlags) -> (
        Bool, String?
    ) {
        // Get running conflicting apps
        let conflictingApps = getRunningConflictingApps()
        if conflictingApps.isEmpty {
            return (false, nil)
        }

        // Common shortcuts used by window management apps
        let commonShortcuts:
            [(keyCode: Int, modifiers: NSEvent.ModifierFlags, description: String)] = [
                // Left/Right half
                (Int(kVK_LeftArrow), [.control, .option], "Left Half"),
                (Int(kVK_RightArrow), [.control, .option], "Right Half"),

                // Top/Bottom half
                (Int(kVK_UpArrow), [.control, .option], "Top Half"),
                (Int(kVK_DownArrow), [.control, .option], "Bottom Half"),

                // Quarters
                (Int(kVK_ANSI_U), [.control, .option], "Top Left Quarter"),
                (Int(kVK_ANSI_I), [.control, .option], "Top Right Quarter"),
                (Int(kVK_ANSI_J), [.control, .option], "Bottom Left Quarter"),
                (Int(kVK_ANSI_K), [.control, .option], "Bottom Right Quarter"),

                // Thirds
                (Int(kVK_ANSI_D), [.control, .option], "Left Third"),
                (Int(kVK_ANSI_E), [.control, .option], "Center Third"),
                (Int(kVK_ANSI_F), [.control, .option], "Right Third"),

                // Two Thirds
                (Int(kVK_ANSI_S), [.control, .option], "Left Two Thirds"),
                (Int(kVK_ANSI_W), [.control, .option], "Center Two Thirds"),
                (Int(kVK_ANSI_R), [.control, .option], "Right Two Thirds"),

                // Center
                (Int(kVK_ANSI_C), [.control, .option], "Center"),

                // Fullscreen
                (Int(kVK_ANSI_F), [.control, .option, .command], "Fullscreen"),
            ]

        // Check if the shortcut matches any common window management shortcut
        for shortcut in commonShortcuts {
            if shortcut.keyCode == keyCode && shortcut.modifiers == modifiers {
                // This is a common shortcut used by window management apps
                let appNames = conflictingApps.joined(separator: ", ")
                logger.debug(
                    "Shortcut conflict detected: \(shortcut.description) with \(appNames)",
                    service: serviceName,
                    category: .shortcuts
                )
                return (true, appNames)
            }
        }

        return (false, nil)
    }
}
