import Foundation

/// Represents a single log entry in the system
struct LogEntry: Identifiable, Equatable {
    let id = UUID()
    let timestamp: Date
    let message: String
    let level: LogLevel
    let service: String
    let category: LogCategory
    
    /// Formatted timestamp string
    var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        return formatter.string(from: timestamp)
    }
    
    /// Returns the emoji prefix for the log level
    var levelPrefix: String {
        return level.prefix
    }
    
    /// Returns a color name based on log level for UI display
    var levelColor: String {
        switch level {
        case .debug: return "logDebug"
        case .info: return "logInfo"
        case .warning: return "logWarning"
        case .error: return "logError"
        }
    }
    
    static func == (lhs: LogEntry, rhs: LogEntry) -> Bool {
        return lhs.id == rhs.id
    }
}