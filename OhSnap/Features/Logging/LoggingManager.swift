import Foundation

// Notification for when logging settings change
extension Notification.Name {
    static let loggingSettingsChanged = Notification.Name("com.ohsnap.loggingSettingsChanged")
}

/// Utility class for controlling logging settings from anywhere in the app
class LoggingManager {
    static let shared = LoggingManager()
    static let loggingSettingsChanged = Notification.Name.loggingSettingsChanged

    // Keys for feature-specific logging presets
    struct PresetKeys {
        static let windowManagement = "preset.windowManagement"
        static let screenDetection = "preset.screenDetection"
        static let workspaces = "preset.workspaces"
        // workspacePreview preset removed
        static let userInterface = "preset.userInterface"
        static let system = "preset.system"
        static let shortcuts = "preset.shortcuts"
        static let generalUsage = "preset.generalUsage"
        static let warningsOnly = "preset.warningsOnly"
    }

    private let logger = LoggingService.shared
    private let serviceName = "LoggingManager"

    private init() {
        logger.info("Initialized with current settings:", service: serviceName)
        logCurrentSettings()
    }

    /// Log the current logging configuration
    func logCurrentSettings() {
        let service = LoggingService.shared

        logger.info("Logging enabled: \(service.isLoggingEnabled)", service: serviceName)
        logger.info("Minimum log level: \(service.minimumLogLevel.rawValue)", service: serviceName)

        let enabledCategories = LogCategory.allCases.filter { service.isCategoryEnabled($0) }
        logger.info(
            "Enabled categories: \(enabledCategories.map { $0.rawValue }.joined(separator: ", "))",
            service: serviceName)

        // Determine and log the current preset
        logCurrentPreset()
    }

    /// Determine and log which preset is currently active
    func logCurrentPreset() {
        let service = LoggingService.shared
        let currentLevel = service.minimumLogLevel
        let allCategoriesEnabled = LogCategory.allCases.allSatisfy { service.isCategoryEnabled($0) }

        // Always print to console to ensure visibility regardless of log settings
        #if DEBUG
            print("🔍 Checking current logging preset...")
        #endif

        // Check for Window Management preset
        if currentLevel == .debug && service.isCategoryEnabled(.windowSnapping)
            && service.isCategoryEnabled(.windowPositioning)
            && service.isCategoryEnabled(.windowCalculation)
            && !service.isCategoryEnabled(.screenDetection)
            && !service.isCategoryEnabled(.screenDebug)
        {
            logger.warning("Current preset: Window Management Debugging", service: serviceName)
            #if DEBUG
                print("🔍 Current preset: Window Management Debugging")
            #endif
            return
        }

        // Check for Screen Detection Debugging preset
        if currentLevel == .debug && service.isCategoryEnabled(.screenDetection)
            && service.isCategoryEnabled(.screenDebug)
            && !service.isCategoryEnabled(.windowSnapping)
            && !service.isCategoryEnabled(.windowPositioning)
            && !service.isCategoryEnabled(.windowCalculation)
        {
            logger.warning("Current preset: Screen Detection Debugging", service: serviceName)
            #if DEBUG
                print("🔍 Current preset: Screen Detection Debugging")
            #endif
            return
        }

        // Check for Workspaces preset
        if currentLevel == .debug && service.isCategoryEnabled(.workspaces)
            && !service.isCategoryEnabled(.windowSnapping)
            && !service.isCategoryEnabled(.screenDetection)
        {
            logger.warning("Current preset: Workspaces Debugging", service: serviceName)
            #if DEBUG
                print("🔍 Current preset: Workspaces Debugging")
            #endif
            return
        }

        // Workspace Preview preset check removed

        // Check for User Interface preset
        if currentLevel == .debug && service.isCategoryEnabled(.userInterface)
            && service.isCategoryEnabled(.menuBar) && service.isCategoryEnabled(.toast)
            && !service.isCategoryEnabled(.windowSnapping)
            && !service.isCategoryEnabled(.screenDetection)
        {
            logger.warning("Current preset: User Interface Debugging", service: serviceName)
            #if DEBUG
                print("🔍 Current preset: User Interface Debugging")
            #endif
            return
        }

        // Check for Shortcuts preset
        if currentLevel == .debug && service.isCategoryEnabled(.shortcuts)
            && service.isCategoryEnabled(.dragToSnap) && !service.isCategoryEnabled(.windowSnapping)
            && !service.isCategoryEnabled(.screenDetection)
        {
            logger.warning("Current preset: Shortcuts Debugging", service: serviceName)
            #if DEBUG
                print("🔍 Current preset: Shortcuts Debugging")
            #endif
            return
        }

        // Check for System preset
        if currentLevel == .debug && service.isCategoryEnabled(.system)
            && service.isCategoryEnabled(.startup) && service.isCategoryEnabled(.permissions)
            && service.isCategoryEnabled(.accessibility)
            && !service.isCategoryEnabled(.windowSnapping)
            && !service.isCategoryEnabled(.screenDetection)
        {
            logger.warning("Current preset: System Debugging", service: serviceName)
            #if DEBUG
                print("🔍 Current preset: System Debugging")
            #endif
            return
        }

        // Check for General Usage preset
        if currentLevel == .info && allCategoriesEnabled {
            logger.warning("Current preset: General Usage (Less Verbose)", service: serviceName)
            #if DEBUG
                print("🔍 Current preset: General Usage (Less Verbose)")
            #endif
            return
        }

        // Check for Warnings & Errors Only preset
        if currentLevel == .warning && allCategoriesEnabled {
            logger.warning("Current preset: Warnings & Errors Only", service: serviceName)
            #if DEBUG
                print("🔍 Current preset: Warnings & Errors Only")
            #endif
            return
        }

        // If no preset matches, it's a custom configuration
        logger.warning("Current preset: Custom Configuration", service: serviceName)
        #if DEBUG
            print("🔍 Current preset: Custom Configuration")
        #endif
    }

    /// Enable only specific categories, disabling all others
    func enableOnlyCategories(_ categories: [LogCategory]) {
        let service = LoggingService.shared

        // First disable all categories
        service.disableAllCategories()

        // Then enable only the specified ones
        service.enableCategories(categories)

        // Use warning level to ensure visibility even with minimum level set to warning
        logger.warning(
            "Enabled only categories: \(categories.map { $0.rawValue }.joined(separator: ", "))",
            service: serviceName)
        #if DEBUG
            print(
                "🔍 Enabled only categories: \(categories.map { $0.rawValue }.joined(separator: ", "))"
            )
        #endif

        // Post notification that categories changed
        NotificationCenter.default.post(
            name: LoggingManager.loggingSettingsChanged,
            object: LoggingService.Keys.enabledCategories
        )
    }

    /// Set the minimum log level
    func setMinimumLogLevel(_ level: LogLevel) {
        let service = LoggingService.shared
        service.minimumLogLevel = level

        // Use warning level to ensure visibility even with minimum level set to warning
        logger.warning("Set minimum log level to: \(level.rawValue)", service: serviceName)
        #if DEBUG
            print("🔍 Set minimum log level to: \(level.rawValue)")
        #endif

        // Post notification that log level changed
        NotificationCenter.default.post(
            name: LoggingManager.loggingSettingsChanged,
            object: LoggingService.Keys.minimumLogLevel
        )
    }

    /// Enable or disable all logging
    func setLoggingEnabled(_ enabled: Bool) {
        let service = LoggingService.shared
        service.isLoggingEnabled = enabled

        if enabled {
            logger.info("Logging enabled", service: serviceName)
        } else {
            #if DEBUG
                print("⚠️ Logging disabled")
            #endif
        }

        // Post notification that logging enabled state changed
        NotificationCenter.default.post(
            name: LoggingManager.loggingSettingsChanged,
            object: LoggingService.Keys.loggingEnabled
        )
    }

    /// Enable a preset for window management debugging
    func enableWindowManagementDebugging() {
        #if DEBUG
            print("🔴 LoggingManager.enableWindowManagementDebugging method called")
        #endif

        enableOnlyCategories([
            .windowSnapping,
            .windowPositioning,
            .windowCalculation,
        ])
        setMinimumLogLevel(.debug)

        logger.warning("Window management debugging preset enabled", service: serviceName)
        #if DEBUG
            print("🔍 Window management debugging preset enabled")
        #endif

        logCurrentPreset()

        NotificationCenter.default.post(
            name: LoggingManager.loggingSettingsChanged,
            object: PresetKeys.windowManagement
        )
    }

    /// Enable a preset for screen detection debugging
    func enableScreenDetectionDebugging() {
        #if DEBUG
            print("🔴 LoggingManager.enableScreenDetectionDebugging method called")
        #endif

        enableOnlyCategories([
            .screenDetection,
            .screenDebug,
        ])
        setMinimumLogLevel(.debug)

        logger.warning("Screen detection debugging preset enabled", service: serviceName)
        #if DEBUG
            print("🔍 Screen detection debugging preset enabled")
        #endif

        logCurrentPreset()

        NotificationCenter.default.post(
            name: LoggingManager.loggingSettingsChanged,
            object: PresetKeys.screenDetection
        )
    }

    /// Enable a preset for workspaces debugging
    func enableWorkspacesDebugging() {
        #if DEBUG
            print("🔴 LoggingManager.enableWorkspacesDebugging method called")
        #endif

        enableOnlyCategories([
            .workspaces
        ])
        setMinimumLogLevel(.debug)

        logger.warning("Workspaces debugging preset enabled", service: serviceName)
        #if DEBUG
            print("🔍 Workspaces debugging preset enabled")
        #endif

        logCurrentPreset()

        NotificationCenter.default.post(
            name: LoggingManager.loggingSettingsChanged,
            object: PresetKeys.workspaces
        )
    }

    /// Enable a preset for user interface debugging
    func enableUserInterfaceDebugging() {
        #if DEBUG
            print("🔴 LoggingManager.enableUserInterfaceDebugging method called")
        #endif

        enableOnlyCategories([
            .userInterface,
            .menuBar,
            .toast,
        ])
        setMinimumLogLevel(.debug)

        logger.warning("User interface debugging preset enabled", service: serviceName)
        #if DEBUG
            print("🔍 User interface debugging preset enabled")
        #endif

        logCurrentPreset()

        NotificationCenter.default.post(
            name: LoggingManager.loggingSettingsChanged,
            object: PresetKeys.userInterface
        )
    }

    /// Enable a preset for shortcuts debugging
    func enableShortcutsDebugging() {
        #if DEBUG
            print("🔴 LoggingManager.enableShortcutsDebugging method called")
        #endif

        enableOnlyCategories([
            .shortcuts,
            .dragToSnap,
        ])
        setMinimumLogLevel(.debug)

        logger.warning("Shortcuts debugging preset enabled", service: serviceName)
        #if DEBUG
            print("🔍 Shortcuts debugging preset enabled")
        #endif

        logCurrentPreset()

        NotificationCenter.default.post(
            name: LoggingManager.loggingSettingsChanged,
            object: PresetKeys.shortcuts
        )
    }

    /// Enable a preset for system debugging
    func enableSystemDebugging() {
        #if DEBUG
            print("🔴 LoggingManager.enableSystemDebugging method called")
        #endif

        enableOnlyCategories([
            .system,
            .startup,
            .permissions,
            .accessibility,
        ])
        setMinimumLogLevel(.debug)

        logger.warning("System debugging preset enabled", service: serviceName)
        #if DEBUG
            print("🔍 System debugging preset enabled")
        #endif

        logCurrentPreset()

        NotificationCenter.default.post(
            name: LoggingManager.loggingSettingsChanged,
            object: PresetKeys.system
        )
    }

    /// Enable a preset for general usage (less verbose)
    func enableGeneralUsageLogging() {
        #if DEBUG
            print("🔴 LoggingManager.enableGeneralUsageLogging method called")
        #endif

        enableOnlyCategories(LogCategory.allCases)
        setMinimumLogLevel(.info)

        logger.warning("General usage logging preset enabled", service: serviceName)
        #if DEBUG
            print("🔍 General usage logging preset enabled")
        #endif

        logCurrentPreset()

        NotificationCenter.default.post(
            name: LoggingManager.loggingSettingsChanged,
            object: PresetKeys.generalUsage
        )
    }

    /// Disable all debug logs but keep warnings and errors
    func disableDebugLogs() {
        #if DEBUG
            print("🔴 LoggingManager.disableDebugLogs method called")
        #endif

        enableOnlyCategories(LogCategory.allCases)
        setMinimumLogLevel(.warning)

        logger.warning(
            "Debug logs disabled, keeping warnings and errors only", service: serviceName)
        #if DEBUG
            print("🔍 Debug logs disabled, keeping warnings and errors only")
        #endif

        logCurrentPreset()

        NotificationCenter.default.post(
            name: LoggingManager.loggingSettingsChanged,
            object: PresetKeys.warningsOnly
        )
    }

    /// Enable logging for a specific feature group
    func enableFeatureLogging(featureGroup: String) {
        #if DEBUG
            print("🔴 LoggingManager.enableFeatureLogging called for feature: \(featureGroup)")
        #endif

        if let categories = LogCategory.featureGroups[featureGroup] {
            enableOnlyCategories(categories)
            setMinimumLogLevel(.debug)

            logger.warning("Enabled logging for feature: \(featureGroup)", service: serviceName)
            #if DEBUG
                print("🔍 Enabled logging for feature: \(featureGroup)")
            #endif

            logCurrentPreset()

            NotificationCenter.default.post(
                name: LoggingManager.loggingSettingsChanged,
                object: "preset.feature.\(featureGroup)"
            )
        } else {
            logger.warning("Unknown feature group: \(featureGroup)", service: serviceName)
            #if DEBUG
                print("⚠️ Unknown feature group: \(featureGroup)")
            #endif
        }
    }

    /// Enable all logging (all categories at debug level)
    func enableAllLogging() {
        #if DEBUG
            print("🔴 LoggingManager.enableAllLogging method called")
        #endif

        enableOnlyCategories(LogCategory.allCases)
        setMinimumLogLevel(.debug)

        logger.warning("All logging enabled at debug level", service: serviceName)
        #if DEBUG
            print("🔍 All logging enabled at debug level")
        #endif

        logCurrentPreset()

        NotificationCenter.default.post(
            name: LoggingManager.loggingSettingsChanged,
            object: "preset.allLogging"
        )
    }

    // Workspace preview logging method removed
}
