import Foundation

// Extension to make it easier to add categories to existing logging calls
extension LoggingService {
    /// Convenience method to log with a category derived from the service name
    func debugWithCategory(_ message: String, service: String) {
        debug(message, service: service, category: LogCategory.categoryForService(service))
    }

    /// Convenience method to log with a category derived from the service name
    func infoWithCategory(_ message: String, service: String) {
        info(message, service: service, category: LogCategory.categoryForService(service))
    }

    /// Convenience method to log with a category derived from the service name
    func warningWithCategory(_ message: String, service: String) {
        warning(message, service: service, category: LogCategory.categoryForService(service))
    }

    /// Convenience method to log with a category derived from the service name
    func errorWithCategory(_ message: String, service: String) {
        error(message, service: service, category: LogCategory.categoryForService(service))
    }
}