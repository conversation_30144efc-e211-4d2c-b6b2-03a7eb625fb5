import SwiftUI

/// This file contains examples of how to use the OhSnapTheme
/// It's meant as a reference for developers working on the app
struct ThemeExamples: View {
    @State private var isSelected = false
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                Text("OhSnap Theme Examples")
                    .font(.title)
                    .padding(.bottom)
                
                // Section titles
                Group {
                    Text("Section Title Style")
                        .font(.headline)
                    
                    Text("Example Section Title")
                        .ohSnapSectionTitleStyle()
                        .padding(.bottom)
                }
                
                // Cards
                Group {
                    Text("Card Styles")
                        .font(.headline)
                    
                    // Using the modifier
                    VStack(alignment: .leading) {
                        Text("Card with Modifier")
                            .font(.subheadline)
                        
                        Toggle("Selected", isOn: $isSelected)
                            .padding(.bottom)
                        
                        Text("This card uses the .ohSnapCardStyle() modifier")
                            .padding()
                            .frame(maxWidth: .infinity)
                            .ohSnapCardStyle(isSelected: isSelected)
                    }
                    .padding(.bottom)
                    
                    // Using direct styling
                    VStack(alignment: .leading) {
                        Text("Card with Direct Styling")
                            .font(.subheadline)
                        
                        OhSnapTheme.applyCardStyle(
                            to: Text("This card uses OhSnapTheme.applyCardStyle()")
                                .padding()
                                .frame(maxWidth: .infinity),
                            isSelected: isSelected
                        )
                    }
                    .padding(.bottom)
                    
                    // Using the constants directly
                    VStack(alignment: .leading) {
                        Text("Card with Direct Constants")
                            .font(.subheadline)
                        
                        Text("This card uses theme constants directly")
                            .padding()
                            .frame(maxWidth: .infinity)
                            .background(isSelected ? OhSnapTheme.Background.selectedCard : OhSnapTheme.Background.card)
                            .clipShape(RoundedRectangle(cornerRadius: OhSnapTheme.CornerRadius.card))
                            .overlay(
                                RoundedRectangle(cornerRadius: OhSnapTheme.CornerRadius.card)
                                    .strokeBorder(
                                        isSelected ? OhSnapTheme.Border.selectedCard : OhSnapTheme.Border.card,
                                        lineWidth: isSelected ? OhSnapTheme.BorderWidth.selected : OhSnapTheme.BorderWidth.normal
                                    )
                            )
                    }
                }
                
                // Typography
                Group {
                    Text("Typography")
                        .font(.headline)
                        .padding(.top)
                    
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Title Text")
                            .font(.system(size: OhSnapTheme.FontSize.title, weight: OhSnapTheme.FontWeight.title))
                        
                        Text("Heading Text")
                            .font(.system(size: OhSnapTheme.FontSize.heading, weight: OhSnapTheme.FontWeight.heading))
                        
                        Text("Body Text")
                            .font(.system(size: OhSnapTheme.FontSize.body, weight: OhSnapTheme.FontWeight.body))
                        
                        Text("Caption Text")
                            .font(.system(size: OhSnapTheme.FontSize.caption, weight: OhSnapTheme.FontWeight.caption))
                            .foregroundColor(OhSnapTheme.Text.caption)
                    }
                    .padding()
                    .ohSnapCardStyle()
                }
                
                // Row styling
                Group {
                    Text("Row Styling")
                        .font(.headline)
                        .padding(.top)
                    
                    VStack(spacing: 0) {
                        HStack {
                            Text("Row Item 1")
                            Spacer()
                            Toggle("", isOn: .constant(true))
                                .labelsHidden()
                        }
                        .ohSnapRowStyle()
                        
                        Divider()
                        
                        HStack {
                            Text("Row Item 2")
                            Spacer()
                            Toggle("", isOn: .constant(false))
                                .labelsHidden()
                        }
                        .ohSnapRowStyle()
                        
                        Divider()
                        
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Row with Caption")
                                Spacer()
                                Toggle("", isOn: .constant(true))
                                    .labelsHidden()
                            }
                            
                            Text("This is a caption that explains the setting in more detail.")
                                .ohSnapCaptionStyle()
                        }
                        .ohSnapRowStyle()
                    }
                    .ohSnapCardStyle()
                }
            }
            .padding()
        }
    }
}

#Preview {
    ThemeExamples()
}