import AppKit

/// A transparent window that provides visual feedback for window snapping
class FootprintWindow: NSWindow {
    private let boxView = NSBox()

    init() {
        // Initialize with zero rect, will be positioned later
        let initialRect = NSRect(x: 0, y: 0, width: 0, height: 0)
        super.init(
            contentRect: initialRect,
            styleMask: .borderless,
            backing: .buffered,
            defer: false
        )

        // Configure window appearance
        isOpaque = false
        backgroundColor = .clear
        hasShadow = false
        level = .floating
        isReleasedWhenClosed = false

        // Create visual box with rounded corners like Apple windows
        boxView.boxType = .custom
        boxView.borderWidth = 2.0
        boxView.cornerRadius = 10.0  // Apple-style rounded corners
        boxView.borderColor = NSColor.systemBlue.withAlphaComponent(0.7)
        boxView.fillColor = NSColor.systemBlue.withAlphaComponent(0.2)
        contentView = boxView

        // Make window ignore mouse events
        ignoresMouseEvents = true
    }

    /// Show the footprint at the specified frame with animation
    func show(at frame: NSRect, animated: Bool = true) {
        // If already visible, animate to new position
        if isVisible {
            if animated {
                NSAnimationContext.runAnimationGroup { context in
                    context.duration = 0.2
                    context.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
                    animator().setFrame(frame, display: true)
                }
            } else {
                setFrame(frame, display: true)
            }
        } else {
            // Set initial frame and make visible
            setFrame(frame, display: false)
            orderFront(nil)

            if animated {
                // Fade in effect
                alphaValue = 0
                NSAnimationContext.runAnimationGroup { context in
                    context.duration = 0.2
                    animator().alphaValue = 1.0
                }
            } else {
                alphaValue = 1.0
            }
        }
    }

    /// Hide the footprint with animation
    func hide(animated: Bool = true) {
        guard isVisible else { return }

        if animated {
            NSAnimationContext.runAnimationGroup { context in
                context.duration = 0.2
                animator().alphaValue = 0
            } completionHandler: {
                self.orderOut(nil)
            }
        } else {
            orderOut(nil)
        }
    }

    /// Update the appearance of the footprint
    func updateAppearance(borderColor: NSColor, fillColor: NSColor, borderWidth: CGFloat) {
        boxView.borderColor = borderColor
        boxView.fillColor = fillColor
        boxView.borderWidth = borderWidth
    }
}
