import SwiftUI

/// A centralized theme for the OhSnap app
/// This struct provides consistent styling across the app
struct OhSnapTheme {
    // MARK: - Colors

    /// Background colors
    struct Background {
        /// Main window background
        static let window = Color(.windowBackgroundColor)

        /// Card background
        static let card = Color(.quaternarySystemFill)

        /// Selected card background
        static let selectedCard = Color.accentColor.opacity(0.1)
    }

    /// Border colors
    struct Border {
        /// Default border color for cards
        static let card = Color.gray.opacity(0.2)

        /// Selected border color for cards
        static let selectedCard = Color.accentColor
    }

    /// Text colors
    struct Text {
        /// Primary text color
        static let primary = Color.primary

        /// Secondary text color
        static let secondary = Color.secondary

        /// Caption text color
        static let caption = Color.secondary
        
        /// Accent text color for interactive elements
        static let accent = Color.accentColor
        
        /// Error text color
        static let error = Color.red
    }

    // MARK: - Dimensions

    /// Border widths
    struct BorderWidth {
        /// Default border width
        static let normal: CGFloat = 1

        /// Selected border width
        static let selected: CGFloat = 2
    }

    /// Corner radii
    struct CornerRadius {
        /// Default corner radius for cards
        static let card: CGFloat = 8
    }

    /// Padding values
    struct Padding {
        /// Standard padding for card content
        static let standard: CGFloat = 12

        /// Vertical padding for rows
        static let vertical: CGFloat = 8

        /// Horizontal padding for rows
        static let horizontal: CGFloat = 12
        
        /// Small padding for compact spacing
        static let small: CGFloat = 4
        
        /// Large padding for section spacing
        static let large: CGFloat = 16
        
        /// Content edge insets for settings sections
        static let section = EdgeInsets(top: 16, leading: 16, bottom: 16, trailing: 16)
    }

    // MARK: - Typography

    /// Font sizes
    struct FontSize {
        /// Title font size
        static let title: CGFloat = 20

        /// Heading font size
        static let heading: CGFloat = 15

        /// Body font size
        static let body: CGFloat = 13

        /// Caption font size
        static let caption: CGFloat = 11
    }

    /// Font weights
    struct FontWeight {
        /// Title font weight
        static let title = Font.Weight.semibold

        /// Heading font weight
        static let heading = Font.Weight.medium

        /// Body font weight
        static let body = Font.Weight.regular

        /// Caption font weight
        static let caption = Font.Weight.regular
    }

    // MARK: - View Modifiers

    /// Card style modifier
    struct CardStyle: ViewModifier {
        var isSelected: Bool = false

        func body(content: Content) -> some View {
            content
                .background(isSelected ? Background.selectedCard : Background.card)
                .clipShape(RoundedRectangle(cornerRadius: CornerRadius.card))
                .overlay(
                    RoundedRectangle(cornerRadius: CornerRadius.card)
                        .strokeBorder(
                            isSelected ? Border.selectedCard : Border.card,
                            lineWidth: isSelected ? BorderWidth.selected : BorderWidth.normal
                        )
                )
        }
    }

    /// Section title style modifier
    struct SectionTitleStyle: ViewModifier {
        func body(content: Content) -> some View {
            content
                .font(.system(size: FontSize.heading, weight: FontWeight.heading))
                .foregroundColor(Text.primary)
                .padding(.top, 8)
        }
    }

    /// Row style modifier
    struct RowStyle: ViewModifier {
        func body(content: Content) -> some View {
            content
                .padding(.vertical, Padding.vertical)
                .padding(.horizontal, Padding.horizontal)
        }
    }

    /// Caption style modifier
    struct CaptionStyle: ViewModifier {
        func body(content: Content) -> some View {
            content
                .font(.system(size: FontSize.caption))
                .foregroundColor(Text.caption)
                .frame(maxWidth: 350, alignment: .leading)
                .fixedSize(horizontal: false, vertical: true)
        }
    }
}

// MARK: - View Extensions

extension View {
    /// Apply the card style
    func ohSnapCardStyle(isSelected: Bool = false) -> some View {
        self.modifier(OhSnapTheme.CardStyle(isSelected: isSelected))
    }

    /// Apply the section title style
    func ohSnapSectionTitleStyle() -> some View {
        self.modifier(OhSnapTheme.SectionTitleStyle())
    }

    /// Apply the row style
    func ohSnapRowStyle() -> some View {
        self.modifier(OhSnapTheme.RowStyle())
    }

    /// Apply the caption style
    func ohSnapCaptionStyle() -> some View {
        self.modifier(OhSnapTheme.CaptionStyle())
    }
    
    /// Apply error text style
    func ohSnapErrorStyle() -> some View {
        self.font(.system(size: OhSnapTheme.FontSize.body))
            .foregroundColor(OhSnapTheme.Text.error)
    }
    
    /// Apply accent text style
    func ohSnapAccentStyle() -> some View {
        self.font(.system(size: OhSnapTheme.FontSize.body))
            .foregroundColor(OhSnapTheme.Text.accent)
    }
    
    /// Apply settings section container style
    func ohSnapSettingsSectionStyle() -> some View {
        self.padding(OhSnapTheme.Padding.section)
            .background(OhSnapTheme.Background.card)
            .clipShape(RoundedRectangle(cornerRadius: OhSnapTheme.CornerRadius.card))
    }
}

// MARK: - Direct Style Functions

extension OhSnapTheme {
    /// Apply card styling directly to a view
    static func applyCardStyle(to content: some View, isSelected: Bool = false) -> some View {
        content
            .background(isSelected ? Background.selectedCard : Background.card)
            .clipShape(RoundedRectangle(cornerRadius: CornerRadius.card))
            .overlay(
                RoundedRectangle(cornerRadius: CornerRadius.card)
                    .strokeBorder(
                        isSelected ? Border.selectedCard : Border.card,
                        lineWidth: isSelected ? BorderWidth.selected : BorderWidth.normal
                    )
            )
    }
}
