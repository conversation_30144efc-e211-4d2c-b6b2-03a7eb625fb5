import SwiftUI

struct ToastContentView: View {
    @ObservedObject private var toastManager = ToastManager.shared
    @State private var isRotating = false

    var body: some View {
        if let toast = toastManager.currentToast {
            VStack {
                HStack(spacing: 12) {
                    // Icon
                    Group {
                        if toast.type == .loading {
                            Image(systemName: toast.type.icon)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 18, height: 18)
                                .rotationEffect(.degrees(isRotating ? 360 : 0))
                                .onAppear {
                                    withAnimation(
                                        Animation.linear(duration: 1.0).repeatForever(
                                            autoreverses: false)
                                    ) {
                                        isRotating = true
                                    }
                                }
                                .onDisappear {
                                    isRotating = false
                                }
                        } else {
                            Image(systemName: toast.type.icon)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 18, height: 18)
                        }
                    }
                    .foregroundColor(toast.type.color)

                    // Content
                    VStack(alignment: .leading, spacing: 2) {
                        Text(toast.title)
                            .font(.system(size: 14, weight: .semibold))

                        if let message = toast.message {
                            Text(message)
                                .font(.system(size: 12))
                                .opacity(0.8)
                        }
                    }

                    // Close button
                    Button(action: {
                        toastManager.dismiss()
                    }) {
                        Image(systemName: "xmark")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.secondary)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(NSColor.windowBackgroundColor))
                        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                )
            }
            .padding(.top, 20)
            .transition(.move(edge: .top).combined(with: .opacity))
            .animation(
                .spring(response: 0.3, dampingFraction: 0.6), value: toastManager.isPresented)
        }
    }
}

// MARK: - Toast Container Window

class ToastWindowController: NSWindowController {
    static let shared = ToastWindowController()

    private override init(window: NSWindow?) {
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 400, height: 100),
            styleMask: [.borderless],
            backing: .buffered,
            defer: false
        )

        window.backgroundColor = .clear
        window.isOpaque = false
        window.hasShadow = false
        window.level = .floating
        window.ignoresMouseEvents = true

        super.init(window: window)

        // Set the content view
        let hostingView = NSHostingView(rootView: ToastContainerView())
        window.contentView = hostingView

        // Position the window at the top center of the main screen
        positionWindow()

        // Observe screen changes to reposition the window
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(screenParametersDidChange),
            name: NSApplication.didChangeScreenParametersNotification,
            object: nil
        )
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func showToast() {
        if window?.isVisible == false {
            window?.orderFront(nil)
        }
    }

    @objc private func screenParametersDidChange() {
        positionWindow()
    }

    private func positionWindow() {
        guard let window = window, let screen = NSScreen.main else { return }

        // Calculate the window position (top center of the main screen)
        let windowWidth: CGFloat = 400
        let windowHeight: CGFloat = 100
        let screenFrame = screen.visibleFrame

        let x = screenFrame.midX - windowWidth / 2
        let y = screenFrame.maxY - windowHeight - 20  // 20px from the top

        window.setFrame(NSRect(x: x, y: y, width: windowWidth, height: windowHeight), display: true)
    }
}

// MARK: - Toast Container View

struct ToastContainerView: View {
    @ObservedObject private var toastManager = ToastManager.shared

    var body: some View {
        VStack {
            if toastManager.isPresented {
                ToastContentView()
            }
            Spacer()
        }
        .frame(maxWidth: .infinity)
        .onChange(of: toastManager.isPresented) { oldValue, newValue in
            if newValue {
                ToastWindowController.shared.showToast()
            }
        }
    }
}
