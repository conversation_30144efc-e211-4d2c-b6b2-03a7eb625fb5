import AppKit
import ApplicationServices  // For AXIsProcessTrusted
import Foundation

/// Manages accessibility permissions for the app
class PermissionManager: ObservableObject {
    static let shared = PermissionManager()

    /// Published property to track if accessibility permissions are granted
    @Published var isAccessibilityPermissionGranted: Bool = false

    /// Timer to periodically check permissions
    private var permissionCheckTimer: Timer?

    /// Notification name for permission changes
    static let permissionStatusChanged = Notification.Name("PermissionStatusChanged")

    private init() {
        // Check initial permission status
        isAccessibilityPermissionGranted = checkAccessibilityPermission()

        // Set up timer to check permissions periodically
        permissionCheckTimer = Timer.scheduledTimer(
            timeInterval: 1.0,
            target: self,
            selector: #selector(checkPermissionStatus),
            userInfo: nil,
            repeats: true
        )
    }

    /// Check if accessibility permissions are granted
    func checkAccessibilityPermission() -> Bool {
        return AXIsProcessTrusted()
    }

    /// Request accessibility permissions
    func requestAccessibilityPermission() {
        let alert = NSAlert()
        alert.messageText = "Accessibility Permissions Required"
        alert.informativeText =
            "OhSnap needs Accessibility permissions to manage window positions. Please grant access in System Settings > Privacy & Security > Accessibility."
        alert.alertStyle = .warning
        alert.addButton(withTitle: "Open Settings")
        alert.addButton(withTitle: "Cancel")

        if alert.runModal() == .alertFirstButtonReturn {
            if let url = URL(
                string:
                    "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")
            {
                NSWorkspace.shared.open(url)
            }
        }
    }

    /// Periodically check permission status
    @objc private func checkPermissionStatus() {
        let currentStatus = checkAccessibilityPermission()

        // If permission status has changed, update and notify
        if currentStatus != isAccessibilityPermissionGranted {
            isAccessibilityPermissionGranted = currentStatus

            // Post notification about permission change
            NotificationCenter.default.post(
                name: PermissionManager.permissionStatusChanged,
                object: isAccessibilityPermissionGranted
            )
        }
    }
}
