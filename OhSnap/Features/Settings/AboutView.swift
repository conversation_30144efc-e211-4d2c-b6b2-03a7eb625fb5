import SwiftUI

struct AboutView: View {
    private let appVersion =
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"

    var body: some View {
        VStack(spacing: 0) {  // Remove default spacing and control it manually
            // App Icon - Center it properly
            ZStack {
                RoundedRectangle(cornerRadius: 24)
                    .fill(.blue.opacity(0.2))
                    .frame(width: 148, height: 148)

                Image(systemName: "inset.filled.leadinghalf.toptrailing.bottomtrailing.rectangle")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 128, height: 128)
                    .foregroundColor(.blue)
            }
            .padding(.top, 40)  // Add more top padding
            .padding(.bottom, 20)  // Adjust spacing to app name

            // App Name - Adjust spacing
            Text("OhSnap")
                .font(.title)
                .fontWeight(.medium)
                .padding(.bottom, 8)  // Reduce spacing to version

            // Version
            Text(appVersion)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .padding(.bottom, 16)  // Adjust spacing to privacy policy

            // Privacy Policy Link
            Button("Privacy Policy") {
                if let url = URL(string: "https://ohsnap.app/privacy") {
                    NSWorkspace.shared.open(url)
                }
            }
            .buttonStyle(.link)

            Spacer()  // This will push the copyright to the bottom

            // Copyright - Adjust bottom spacing
            Text("© \(String(Calendar.current.component(.year, from: Date()))) Ricardo Ramirez")
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.bottom, 4)  // Reduce spacing between copyright lines

            Text("All Rights Reserved")
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.bottom, 20)  // Add padding at the bottom
        }
        .frame(width: 280, height: 400)
        .multilineTextAlignment(.center)  // Center all text
    }
}

#Preview {
    AboutView()
}
