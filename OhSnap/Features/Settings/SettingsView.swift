import AppKit  // For NSEvent
import Carbon.HIToolbox  // For key code constants
// Import for system shortcut conflict detection
import Foundation
import KeyboardShortcuts
import SwiftUI

struct SettingsView: View {
    @AppStorage("startAtLogin") private var startAtLogin = false
    @AppStorage("showMenuBarIcon") private var showMenuBarIcon = true
    @EnvironmentObject private var appDelegate: AppDelegate
    @EnvironmentObject private var workspaceService: WorkspaceService
    @State private var selectedTab = "general"

    // Define the tabs with their icons and titles
    private let tabs = [
        Tab(id: "general", title: "General", icon: "gearshape"),
        Tab(id: "hotkeys", title: "Hotkeys", icon: "command"),
        Tab(
            id: "workspaces", title: "Workspaces",
            icon: "inset.filled.topleft.topright.bottomleft.bottomright.rectangle"),
    ]

    // Get the current tab title for the window title
    private var currentTabTitle: String {
        tabs.first(where: { $0.id == selectedTab })?.title ?? "Settings"
    }

    var body: some View {
        // We need to use NSViewControllerRepresentable to properly customize the window
        VStack(spacing: 0) {
            // Custom toolbar - this will be positioned at the top
            HStack(spacing: 0) {
                Spacer()
                ForEach(tabs) { tab in
                    ToolbarButton(
                        tab: tab,
                        isSelected: selectedTab == tab.id,
                        action: { selectedTab = tab.id }
                    )
                }
                Spacer()
            }
            .padding(.top, 8)
            .padding(.bottom, 4)

            // Bottom border
            Divider()

            // Content area
            Group {
                switch selectedTab {
                case "general":
                    GeneralSettingsView(
                        startAtLogin: $startAtLogin,
                        showMenuBarIcon: $showMenuBarIcon,
                        appDelegate: appDelegate
                    )
                case "hotkeys":
                    ShortcutSettingsView()
                        .environmentObject(workspaceService)
                case "workspaces":
                    WorkspacesSettingsView()
                        .environmentObject(workspaceService)
                default:
                    EmptyView()
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
        .frame(width: 600, height: 500)
        .onAppear {
            // Only update the window title on appear, don't reconfigure the window
            // This avoids conflicts with the WindowManager configuration
            DispatchQueue.main.async {
                if let window = NSApp.mainWindow {
                    window.title = currentTabTitle
                }
            }
        }
        .onChange(of: selectedTab) { oldValue, newValue in
            // Update the window title when tab changes
            // Use DispatchQueue.main.async to ensure it happens after the view update
            DispatchQueue.main.async {
                if let window = NSApp.mainWindow {
                    window.title = currentTabTitle
                }
            }
        }
    }
}

// Tab model
struct Tab: Identifiable {
    let id: String
    let title: String
    let icon: String
}

// Custom toolbar button
struct ToolbarButton: View {
    let tab: Tab
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: tab.icon)
                    .font(.system(size: 16))
                    .symbolRenderingMode(.hierarchical)
                    .foregroundColor(isSelected ? .accentColor : .secondary)
                    .frame(height: 18)
                    .imageScale(.large)

                Text(tab.title)
                    .font(.system(size: 11, weight: .regular))
                    .foregroundColor(isSelected ? .primary : .secondary)
            }
            .frame(width: 70, height: 40)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .background(Color.clear)
        .padding(.horizontal, 5)
        // No underline indicator for selected tab
    }
}

struct GeneralSettingsView: View {
    @Binding var startAtLogin: Bool
    @Binding var showMenuBarIcon: Bool
    let appDelegate: AppDelegate

    // Drag-to-snap settings
    @AppStorage("dragToSnapEnabled") private var dragToSnapEnabled = false
    @AppStorage("avoidSystemConflicts") private var avoidSystemConflicts = true
    @State private var snapModifiers: UInt = 0
    @State private var selectedModifier = 0

    // Track the allow any shortcut state to force UI updates
    @State private var allowAnyShortcut: Bool = false

    // State to trigger reset
    @State private var resetTriggered: Bool = false

    private let modifierOptions = [
        (0, "None"),
        (NSEvent.ModifierFlags.command.rawValue, "Command (⌘)"),
        (NSEvent.ModifierFlags.option.rawValue, "Option (⌥)"),
        (NSEvent.ModifierFlags.control.rawValue, "Control (⌃)"),
        (NSEvent.ModifierFlags.shift.rawValue, "Shift (⇧)"),
    ]

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // Application Settings
                Text("Application")
                    .ohSnapSectionTitleStyle()

                GroupBox {
                    VStack(spacing: 0) {
                        HStack {
                            Text("Start at login")
                            Spacer()
                            Toggle("", isOn: $startAtLogin)
                                .toggleStyle(.switch)
                                .labelsHidden()
                                .onChange(of: startAtLogin) { oldValue, newValue in
                                    LoginItemManager.setStartAtLogin(newValue)
                                }
                        }
                        .ohSnapRowStyle()

                        Divider()

                        HStack {
                            Text("Show menu bar icon")
                            Spacer()
                            Toggle("", isOn: $showMenuBarIcon)
                                .toggleStyle(.switch)
                                .labelsHidden()
                                .onChange(of: showMenuBarIcon) { oldValue, newValue in
                                    appDelegate.updateMenuBarIconVisibility()
                                }
                        }
                        .ohSnapRowStyle()
                    }
                }

                // Workspaces Settings
                Text("Workspaces")
                    .ohSnapSectionTitleStyle()

                GroupBox {
                    VStack(spacing: 0) {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Close non-workspace windows when restoring")
                                Spacer()
                                Toggle(
                                    "",
                                    isOn: Binding(
                                        get: { DefaultsManager.shared.closeNonWorkspaceWindows },
                                        set: {
                                            DefaultsManager.shared.setCloseNonWorkspaceWindows($0)
                                        }
                                    )
                                )
                                .toggleStyle(.switch)
                                .labelsHidden()
                            }

                            Text(
                                "When enabled, windows that are not part of the workspace will be closed when restoring a workspace."
                            )
                            .ohSnapCaptionStyle()
                        }
                        .ohSnapRowStyle()
                    }
                }

                // Keyboard Shortcuts Settings
                Text("Keyboard Shortcuts")
                    .ohSnapSectionTitleStyle()

                GroupBox {
                    VStack(spacing: 0) {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Allow any keyboard shortcut")
                                Spacer()
                                Toggle("", isOn: $allowAnyShortcut)
                                    .toggleStyle(.switch)
                                    .labelsHidden()
                                    .onChange(of: allowAnyShortcut) { oldValue, newValue in
                                        // Update both the validator and DefaultsManager
                                        ShortcutValidatorManager.shared.setAllowAnyShortcut(
                                            newValue)
                                        DefaultsManager.shared.setAllowAnyShortcut(newValue)
                                    }
                            }

                            Text(
                                "When enabled, OhSnap will allow you to set any keyboard shortcut, even if it conflicts with system shortcuts or other applications."
                            )
                            .ohSnapCaptionStyle()
                        }
                        .ohSnapRowStyle()
                    }
                }

                // Drag-to-Snap Settings
                Text("Drag-to-Snap")
                    .ohSnapSectionTitleStyle()

                GroupBox {
                    VStack(spacing: 0) {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Enable drag-to-snap")
                                Spacer()
                                Toggle("", isOn: $dragToSnapEnabled)
                                    .toggleStyle(.switch)
                                    .labelsHidden()
                                    .onChange(of: dragToSnapEnabled) { oldValue, newValue in
                                        // Post notification to update SnappingManager
                                        NotificationCenter.default.post(
                                            name: Notification.Name("DragToSnapEnabledChanged"),
                                            object: newValue
                                        )
                                    }
                            }

                            Text(
                                "When enabled, drag-to-snap allows you to snap windows by dragging them to the edges or corners of the screen."
                            )
                            .ohSnapCaptionStyle()
                        }
                        .ohSnapRowStyle()

                        Divider()

                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Modifier key required for drag-to-snap:")
                                Spacer()
                                Picker("", selection: $selectedModifier) {
                                    ForEach(0..<modifierOptions.count, id: \.self) { index in
                                        Text(modifierOptions[index].1).tag(index)
                                    }
                                }
                                .frame(width: 120)
                                .labelsHidden()
                                .onChange(of: selectedModifier) { oldValue, newValue in
                                    snapModifiers = modifierOptions[newValue].0
                                    UserDefaults.standard.set(
                                        snapModifiers, forKey: "snapModifiers")

                                    // Post notification to update SnappingManager
                                    NotificationCenter.default.post(
                                        name: Notification.Name("SnapModifiersChanged"),
                                        object: modifierOptions[newValue].0
                                    )
                                }
                            }

                            Text(
                                "If a modifier key is selected, you'll need to hold it while dragging windows to activate the snapping functionality."
                            )
                            .ohSnapCaptionStyle()
                        }
                        .ohSnapRowStyle()

                        Divider()

                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Avoid conflicts with macOS features")
                                Spacer()
                                Toggle("", isOn: $avoidSystemConflicts)
                                    .toggleStyle(.switch)
                                    .labelsHidden()
                                    .onChange(of: avoidSystemConflicts) { oldValue, newValue in
                                        // Post notification to update SnappingManager
                                        NotificationCenter.default.post(
                                            name: Notification.Name("AvoidSystemConflictsChanged"),
                                            object: newValue
                                        )
                                    }
                            }

                            Text(
                                "When 'Avoid conflicts with macOS features' is enabled, OhSnap will detect and avoid potential conflicts with Mission Control, Stage Manager, and other macOS window management features."
                            )
                            .ohSnapCaptionStyle()
                        }
                        .ohSnapRowStyle()
                    }
                }

                // Defaults Section
                Text("Defaults")
                    .ohSnapSectionTitleStyle()

                GroupBox {
                    Button("Restore to defaults") {
                        print("Reset to defaults button clicked")
                        resetTriggered = true
                    }
                    .buttonStyle(PlainButtonStyle())
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 8)
                }
            }
            .padding()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .onAppear {
            // Sync the AppStorage value with the actual login item status when the view appears
            startAtLogin = LoginItemManager.isStartingAtLogin()

            // Load the snapModifiers value from UserDefaults
            if let modifierValue = UserDefaults.standard.object(forKey: "snapModifiers") as? UInt {
                snapModifiers = modifierValue
            }

            // Set the selected modifier based on the stored value
            if let index = modifierOptions.firstIndex(where: { $0.0 == snapModifiers }) {
                selectedModifier = index
            } else {
                selectedModifier = 0
            }

            // Initialize the allowAnyShortcut state from DefaultsManager
            allowAnyShortcut = ShortcutValidatorManager.shared.allowAnyShortcut
        }
        .onChange(of: resetTriggered) { oldValue, newValue in
            if newValue {
                print("Reset triggered via state change")
                resetToDefaults()
                // Reset the trigger after handling
                resetTriggered = false
            }
        }
    }

    // Reset all general settings to their default values
    private func resetToDefaults() {
        print("🔄 resetToDefaults() function called")

        let logger = LoggingService.shared
        logger.info(
            "Resetting general settings to defaults",
            service: "GeneralSettingsView",
            category: .general
        )

        // Reset Application settings
        startAtLogin = false
        LoginItemManager.setStartAtLogin(false)

        showMenuBarIcon = true
        appDelegate.updateMenuBarIconVisibility()

        // Reset Workspaces settings
        DefaultsManager.shared.setCloseNonWorkspaceWindows(false)

        // Reset Keyboard Shortcuts settings
        allowAnyShortcut = false
        ShortcutValidatorManager.shared.setAllowAnyShortcut(false)
        DefaultsManager.shared.setAllowAnyShortcut(false)

        // Reset Drag-to-Snap settings
        dragToSnapEnabled = true
        NotificationCenter.default.post(
            name: Notification.Name("DragToSnapEnabledChanged"),
            object: true
        )

        selectedModifier = 0  // None
        snapModifiers = 0
        UserDefaults.standard.set(0, forKey: "snapModifiers")
        NotificationCenter.default.post(
            name: Notification.Name("SnapModifiersChanged"),
            object: 0
        )

        avoidSystemConflicts = true
        NotificationCenter.default.post(
            name: Notification.Name("AvoidSystemConflictsChanged"),
            object: true
        )

        // Force synchronize to ensure changes are written immediately
        UserDefaults.standard.synchronize()

        logger.info(
            "General settings reset to defaults",
            service: "GeneralSettingsView",
            category: .general
        )

        print("🔄 resetToDefaults() function completed")
    }
}

#Preview {
    let snappingService = WindowSnappingService()
    let workspaceService = WorkspaceService(snappingService: snappingService)
    return SettingsView()
        .environmentObject(MockAppDelegate())
        .environmentObject(workspaceService)
}

private class MockAppDelegate: NSObject, NSApplicationDelegate, ObservableObject {
    func updateMenuBarIconVisibility() {}
}
