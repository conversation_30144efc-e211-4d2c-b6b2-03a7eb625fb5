= aerospace-move(1)
include::util/man-attributes.adoc[]
// tag::purpose[]
:manpurpose: Move the focused window in the given direction
// end::purpose[]
:manname: aerospace-move

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace move [-h|--help] [--window-id <window-id>] (left|down|up|right)

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
Move the focused window in the given direction. See the "Examples" section for more details.

Deprecated name: `move-through`

// =========================================================== Options
include::./util/conditional-options-header.adoc[]

-h, --help:: Print help

--window-id <window-id>::
include::./util/window-id-flag-desc.adoc[]

// =========================================================== Examples
include::util/conditional-examples-header.adoc[]

. Given this layout
+
----
h_tiles
├── window 1 (focused)
└── window 2
----
+
`move right` will result in the following layout
+
----
h_tiles
├── window 2
└── window 1 (focused)
----

. Given this layout
+
----
h_tiles
├── window 1
├── window 2 (focused)
└── v_tiles
    ├── window 3
    └── window 4
----
+
`move right` will result in the following layout
+
----
h_tiles
├── window 1
└── v_tiles
    ├── window 3
    ├── window 2 (focused)
    └── window 4
----

. Given this layout
+
----
h_tiles
├── window 1
└── v_tiles
    ├── window 3
    ├── window 2 (focused)
    └── window 4
----
+
`move left` will result in the following layout
+
----
h_tiles
├── window 1
├── window 2 (focused)
└── v_tiles
    ├── window 3
    └── window 4
----
. *Implicit container example*
+
In some cases, `move` needs to implicitly create a container to fulfill your command.
+
Given this layout
+
----
h_tiles
├── window 1
├── window 2 (focused)
└── window 3
----
+
`move up` will result in the following layout
+
----
v_tiles
├── window 2 (focused)
└── h_tiles
    ├── window 1
    └── window 3
----
+
`v_tiles` is an implicitly created container.

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
