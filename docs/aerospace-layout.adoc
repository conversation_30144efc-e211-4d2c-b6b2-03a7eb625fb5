= aerospace-layout(1)
include::util/man-attributes.adoc[]
:manname: aerospace-layout
// tag::purpose[]
:manpurpose: Change layout of the focused window to the given layout
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace layout [-h|--help] [--window-id <window-id>]
                 (h_tiles|v_tiles|h_accordion|v_accordion|tiles|accordion|horizontal|vertical|tiling|floating)...

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

If several arguments are supplied then finds the first argument that doesn't describe the currently active layout, and applies the layout.

* Change both tiling layout and orientation in one go: `h_tiles|v_tiles|h_accordion|v_accordion`
* Change tiling layout but preserve orientation: `tiles|accordion`
* Change orientation but preserve layout: `horizontal|vertical`
* Toggle floating/tiling mode: `tiling|floating`

// =========================================================== Options
include::./util/conditional-options-header.adoc[]

-h, --help:: Print help

--window-id <window-id>::
include::./util/window-id-flag-desc.adoc[]

// =========================================================== Examples
include::util/conditional-examples-header.adoc[]

* Toggle between `floating` and `tiling` layouts (order of args doesn't matter): +
`aerospace layout floating tiling`

* Toggle orientation (order of args doesn't matter): +
`aerospace layout horizontal vertical`

* Toggle between `tiles` and `accordion` layouts (order of args doesn't matter): +
`aerospace layout tiles accordion`

* Switch to `tiles` layout. Toggle the layout orientation if already in `tiles` layout: +
`aerospace layout tiles horizontal vertical`

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
