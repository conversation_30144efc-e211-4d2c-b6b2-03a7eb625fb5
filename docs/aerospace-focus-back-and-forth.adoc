= aerospace-focus-back-and-forth(1)
include::util/man-attributes.adoc[]
// tag::purpose[]
:manpurpose: Switch between the current and previously focused elements back and forth
// end::purpose[]
:manname: aerospace-focus-back-and-forth

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace focus-back-and-forth [-h|--help]

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}.
The element is either a window or an empty workspace.

AeroSpace stores only one previously focused window in history,
which means that if you close the previous window,
`focus-back-and-forth` has no window to switch focus to.
In that case, the command will exit with non-zero exit code.

That's why it may be preferred to combine `focus-back-and-forth` with `workspace-back-and-forth`: +
----
aerospace focus-back-and-forth || aerospace workspace-back-and-forth
----

Also see: <<workspace-back-and-forth, workspace-back-and-forth>>
// end::body[]

// =========================================================== Options
include::util/conditional-options-header.adoc[]

-h, --help:: Print help

// =========================================================== Footer
include::util/man-footer.adoc[]
