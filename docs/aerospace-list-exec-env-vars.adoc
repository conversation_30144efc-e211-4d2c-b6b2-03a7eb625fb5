= aerospace-list-exec-env-vars(1)
include::util/man-attributes.adoc[]
:manname: aerospace-list-exec-env-vars
// tag::purpose[]
:manpurpose: List environment variables that exec-* commands and callbacks are run with
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace list-exec-env-vars [-h|--help]

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

Examples of commands and callbacks:

* `aerospace exec-and-forget` command
* `exec-on-workspace-change-callback`

// end::body[]

// =========================================================== Options
include::util/conditional-options-header.adoc[]

-h, --help:: Print help

// =========================================================== Footer
include::util/man-footer.adoc[]
