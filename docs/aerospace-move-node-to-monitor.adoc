= aerospace-move-node-to-monitor(1)
include::util/man-attributes.adoc[]
:manname: aerospace-move-node-to-monitor
// tag::purpose[]
:manpurpose: Move window to monitor targeted by relative direction, by order, or by pattern
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace move-node-to-monitor [-h|--help] [--window-id <window-id>] [--focus-follows-window]
                               [--fail-if-noop] [--wrap-around] (left|down|up|right|next|prev)
aerospace move-node-to-monitor [-h|--help] [--window-id <window-id>] [--focus-follows-window]
                               [--fail-if-noop] <monitor-pattern>...

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

// =========================================================== Options
include::./util/conditional-options-header.adoc[]

-h, --help:: Print help
--wrap-around:: Make it possible to wrap around the movement

--focus-follows-window::
Make sure that the window in question receives focus after moving.
This flag is a shortcut for manually running `aerospace-workspace`/`aerospace-focus` after `move-node-to-monitor` successful execution.

--window-id <window-id>::
include::./util/window-id-flag-desc.adoc[]

// =========================================================== Arguments
include::util/conditional-arguments-header.adoc[]

(left|down|up|right)::
Move window to monitor in direction relative to the focused monitor

(next|prev)::
Move window to next|prev monitor in order they appear in tray icon

<monitor-pattern>...::
Find the first monitor pattern in the list that doesn't describe the current monitor and move the window to the appropriate monitor.
Monitor pattern is the same as in `workspace-to-monitor-force-assignment` config option

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
