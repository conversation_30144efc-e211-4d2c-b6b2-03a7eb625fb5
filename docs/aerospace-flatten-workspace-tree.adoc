= aerospace-flatten-workspace-tree(1)
include::util/man-attributes.adoc[]
:manname: aerospace-flatten-workspace-tree
// tag::purpose[]
:manpurpose: Flatten the tree of the focused workspace
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace flatten-workspace-tree [-h|--help] [--workspace <workspace>]

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

The command is useful when you messed up with your layout, and it's easier to "reset" it and start again.

// =========================================================== Options
include::./util/conditional-options-header.adoc[]

-h, --help:: Print help

--workspace <workspace>::
include::./util/workspace-flag-desc.adoc[]

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
