= aerospace-focus(1)
include::util/man-attributes.adoc[]
:manname: aerospace-focus
// tag::purpose[]
:manpurpose: Set focus to the nearest window in the given direction.
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace focus [-h|--help] [--ignore-floating]
                [--boundaries <boundary>] [--boundaries-action <action>]
                (left|down|up|right)
aerospace focus [-h|--help] --window-id <window-id>
aerospace focus [-h|--help] --dfs-index <dfs-index>

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

Contrary to i3, `focus` command doesn't have a separate argument to focus floating windows.
From `focus` command perspective, floating windows are part of xref:guide.adoc#tree[the tree].
The floating window parent container is determined as the smallest tiling container that contains the center of the floating window.
The technique eliminates the need for an additional binding for floating windows.
This behavior can be disabled with `--ignore-floating` flag.

`focus child|parent` isn't supported because the necessity of this operation is under the question.
https://github.com/nikitabobko/AeroSpace/issues/5

// =========================================================== Options
include::util/conditional-options-header.adoc[]

-h, --help:: Print help

--boundaries <boundary>::
Defines focus boundaries. +
`<boundary>` possible values: `(workspace|all-monitors-outer-frame)`. +
The default is: `workspace`

--boundaries-action <action>::
Defines the behavior when requested to cross the `<boundary>`. +
`<action>` possible values: `(stop|fail|wrap-around-the-workspace|wrap-around-all-monitors)` +
The default is: `stop`

--window-id <window-id>::
Focus the window with specified `<window-id>`

--dfs-index <dfs-index>::
Focus window by its index, based on a depth-first search (DFS) of the window within the workspace tree.
Index is 0-based.

--ignore-floating::
Don't perceive floating windows as part of the tree.
It may be useful for more reliable scripting.

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
