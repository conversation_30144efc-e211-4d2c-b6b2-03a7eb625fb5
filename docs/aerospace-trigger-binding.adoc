= aerospace-trigger-binding(1)
include::util/man-attributes.adoc[]
:manname: aerospace-trigger-binding
// tag::purpose[]
:manpurpose: Trigger AeroSpace binding as if it was pressed by user
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace trigger-binding [-h|--help] <binding> --mode <mode-id>

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

You can use aerospace-config command to inspect available bindings: +
`aerospace config --get mode.main.binding --keys`

// =========================================================== Options
include::util/conditional-options-header.adoc[]

-h, --help:: Print help
--mode <mode-id>:: Mode to search `<binding>` in

// =========================================================== Arguments
include::util/conditional-arguments-header.adoc[]

<binding>:: Binding to trigger

// =========================================================== Examples
include::util/conditional-examples-header.adoc[]

* Run alphabetically first binding from config (useless and synthetic example): +
`aerospace trigger-binding --mode main "$(aerospace config --get mode.main.binding --keys | head -1)"`
* Trigger `alt-tab` binding: +
`aerospace trigger-binding --mode main alt-tab`

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
