= aerospace-focus-monitor(1)
include::util/man-attributes.adoc[]
:manname: aerospace-focus-monitor
// tag::purpose[]
:manpurpose: Focus monitor by relative direction, by order, or by pattern
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace focus-monitor [-h|--help] [--wrap-around] (left|down|up|right)
aerospace focus-monitor [-h|--help] [--wrap-around] (next|prev)
aerospace focus-monitor [-h|--help] <monitor-pattern>...

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

// =========================================================== Options
include::./util/conditional-options-header.adoc[]

-h, --help:: Print help
--wrap-around:: Make it possible to wrap around focus

// =========================================================== Arguments
include::./util/conditional-arguments-header.adoc[]

(left|down|up|right)::
Focus monitor in direction relative to the focused monitor

(next|prev)::
Focus next|prev monitor in order they appear in tray icon

<monitor-pattern>...::
Find the first monitor pattern in the list that doesn't describe the current monitor and focus it.
Monitor pattern is the same as in `workspace-to-monitor-force-assignment` config option

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
