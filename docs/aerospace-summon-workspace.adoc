= aerospace-summon-workspace(1)
include::util/man-attributes.adoc[]
// tag::purpose[]
:manpurpose: Move the requested workspace to the focused monitor.
// end::purpose[]
:manname: aerospace-summon-workspace

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace summon-workspace [-h|--help] [--fail-if-noop] <workspace>

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}
The moved workspace becomes focused.
The behavior is identical to Xmonad.

The command makes sense only in multi-monitor setup.
In single monitor setup the command is identical to `workspace` command.

// =========================================================== Options
include::./util/conditional-options-header.adoc[]

-h, --help:: Print help
--fail-if-noop:: Exit with non-zero exit code if the workspace already visible on the focused monitor.

// =========================================================== Arguments
include::./util/conditional-arguments-header.adoc[]

<workspace>:: The workspace to operate on.

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
