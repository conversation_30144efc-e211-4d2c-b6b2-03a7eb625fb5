= aerospace-list-windows(1)
include::util/man-attributes.adoc[]
:manname: aerospace-list-windows
// tag::purpose[]
:manpurpose: Print windows that satisfy conditions
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace list-windows [-h|--help] (--workspace <workspace>...|--monitor <monitor>...)
                       [--monitor <monitor>...] [--workspace <workspace>...]
                       [--pid <pid>] [--app-bundle-id <app-bundle-id>] [--format <output-format>]
                       [--count] [--json]
aerospace list-windows [-h|--help] --all [--format <output-format>] [--count] [--json]
aerospace list-windows [-h|--help] --focused [--format <output-format>] [--count] [--json]

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

// =========================================================== Options
include::util/conditional-options-header.adoc[]

-h, --help:: Print help

include::util/all-monitors-option.adoc[]

--focused::
Print the focused window.
Please note that it is possible for no window to be in focus.
In that case, error is reported.

--workspace <workspace>...::
Filter results to only print windows that belong to either of specified workspaces.
`<workspace>...` is a space-separated list of workspace names.
+
Possible values: +
+
. Workspace name
. `focused` is a special workspace name that represents the focused workspace
. `visible` is a special workspace name that represents all currently visible workspaces (In multi-monitor setup, there are multiple visible workspaces)

include::util/monitor-option.adoc[]

--pid <pid>:: Filter results to only print windows that belong to the Application with specified `<pid>`

--app-bundle-id <app-bundle-id>::
Filter results to only print windows that belong to the Application with specified https://developer.apple.com/documentation/appstoreconnectapi/bundle_ids[Bundle ID]
+
Deprecated (but still supported) flag name: `--app-id`

--format <output-format>:: Specify output format. See "Output Format" section for more details.
Incompatible with `--count`

--count:: Output only the number of windows.
Incompatible with `--format`

--json:: Output in JSON format.
Can be used in combination with `--format` to specify which data to include into the json.
Incompatible with `--count`

// =========================================================== Output Format
include::util/conditional-output-format-header.adoc[]

Output format can be configured with optional `[--format <output-format>]` option.
`<output-format>` supports https://en.wikipedia.org/wiki/String_interpolation[string interpolation].

If not specified, the default `<output-format>` is: +
`%{window-id}%{right-padding} | %{app-name}%{right-padding} | %{window-title}`

The following variables can be used inside `<output-format>`:

%{window-id}:: Number. Window unique ID
%{window-title}:: String. Window title
%{window-is-fullscreen}:: Boolean. Is window in fullscreen by `aerospace fullscreen` command

%{app-bundle-id}:: String. Application unique identifier. https://developer.apple.com/documentation/appstoreconnectapi/bundle_ids[Bundle ID]
%{app-name}:: String. Application name
%{app-pid}:: Number. https://en.wikipedia.org/wiki/Process_identifier[UNIX process identifier]
%{app-exec-path}:: String. Application executable path
%{app-bundle-path}:: String. Application bundle path

%{workspace}:: String. Name of the belonging workspace
%{workspace-is-focused}:: Boolean. True if the workspace has focus
%{workspace-is-visible}:: Boolean. True if the workspace is visible. A workspace can be visible but not focused in a multi-monitor setup

%{monitor-id}:: 1-based Number. Sequential number of the belonging monitor.
%{monitor-appkit-nsscreen-screens-id}:: 1-based index of the belonging monitor in `NSScreen.screens` array. Useful for integration with other tools that might be using `NSScreen.screens` ordering (like sketchybar).
%{monitor-name}:: String. Name of the belonging monitor

%{right-padding}:: A special variable which expands with a minimum number of spaces required to form a right padding in the appropriate column
%{newline}:: Unicode U+000A newline symbol `\n`
%{tab}:: Unicode U+0009 tab symbol `\t`

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
