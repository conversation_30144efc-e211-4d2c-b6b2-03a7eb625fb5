= aerospace-macos-native-minimize(1)
include::util/man-attributes.adoc[]
:manname: aerospace-macos-native-minimize
// tag::purpose[]
:manpurpose: Minimize focused window
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace macos-native-minimize [-h|--help] [--window-id <window-id>]

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

// =========================================================== Options
include::./util/conditional-options-header.adoc[]

-h, --help:: Print help

--window-id <window-id>::
include::./util/window-id-flag-desc.adoc[]

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
