= aerospace-list-workspaces(1)
include::util/man-attributes.adoc[]
:manname: aerospace-list-workspaces
// tag::purpose[]
:manpurpose: Print workspaces that satisfy conditions
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace list-workspaces [-h|--help] --monitor <monitor>... [--visible [no]] [--empty [no]] [--format <output-format>] [--count] [--json]
aerospace list-workspaces [-h|--help] --all [--format <output-format>] [--count] [--json]
aerospace list-workspaces [-h|--help] --focused [--format <output-format>] [--count] [--json]

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

// =========================================================== Options
include::util/conditional-options-header.adoc[]

-h, --help:: Print help
--format <output-format>:: Specify output format. See "Output Format" section for more details

include::util/all-monitors-option.adoc[]

--focused::
Alias for `--monitor focused --visible`.
Always prints a single workspace

include::util/monitor-option.adoc[]

--visible [no]::
Filter results to only print currently visible workspaces.
`[no]` inverts the condition.
Several workspaces can be visible in multi-monitor setup

--empty [no]::
Filter results to only print empty workspaces.
`[no]` inverts the condition.

--format <output-format>:: Specify output format. See "Output Format" section for more details.
Incompatible with `--count`

--count:: Output only the number of workspaces.
Incompatible with `--format`

--json:: Output in JSON format.
Can be used in combination with `--format` to specify which data to include into the json.
Incompatible with `--count`

// =========================================================== Output Format
include::util/conditional-output-format-header.adoc[]

Output format can be configured with optional `[--format <output-format>]` option.
`<output-format>` supports https://en.wikipedia.org/wiki/String_interpolation[string interpolation].

If not specified, the default `<output-format>` is: +
`%{workspace}`

The following variables can be used inside `<output-format>`:

%{workspace}:: String. Name of the belonging workspace
%{workspace-is-focused}:: Boolean. True if the workspace has focus
%{workspace-is-visible}:: Boolean. True if the workspace is visible. A workspace can be visible but not focused in a multi-monitor setup

%{monitor-id}:: 1-based Number. Sequential number of the belonging monitor
%{monitor-appkit-nsscreen-screens-id}:: 1-based Number. Sequential number of the belonging monitor in `NSScreen.screens`. Useful for integration with other tools that might be using `NSScreen.screens` ordering (like sketchybar).
%{monitor-name}:: String. Name of the belonging monitor

%{right-padding}:: A special variable which expands with a minimum number of spaces required to form a right padding in the appropriate column
%{newline}:: Unicode U+000A newline symbol `\n`
%{tab}:: Unicode U+0009 tab symbol `\t`

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
