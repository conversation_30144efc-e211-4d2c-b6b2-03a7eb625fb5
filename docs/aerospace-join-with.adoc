= aerospace-join-with(1)
include::util/man-attributes.adoc[]
// tag::purpose[]
:manpurpose: Put the focused window and the nearest node in the specified direction under a common parent container
// end::purpose[]
:manname: aerospace-join-with

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace join-with [-h|--help] [--window-id <window-id>] (left|down|up|right)

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

// =========================================================== Examples
include::util/conditional-examples-header.adoc[]

Given this layout

----
h_tiles
├── window 1
├── window 2 (focused)
└── window 3
----

`join-with right` will result in the following layout

----
h_tiles
├── window 1
└── v_tiles
    ├── window 2 (focused)
    └── window 3
----

NOTE: `join-with` is a high-level replacement for i3's https://i3wm.org/docs/userguide.html#_splitting_containers[split command].
There is an observation that the only reason why you might want to split a node is to put several windows under a common "umbrella" parent. Unlike `split`, `join-with` can be used with xref:guide.adoc#normalization[`enable-normalization-flatten-containers`]

// =========================================================== Options
include::util/conditional-options-header.adoc[]

-h, --help:: Print help

--window-id <window-id>::
include::./util/window-id-flag-desc.adoc[]

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
