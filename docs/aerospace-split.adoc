= aerospace-split(1)
include::util/man-attributes.adoc[]
:manname: aerospace-split
// tag::purpose[]
:manpurpose: Split focused window
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace split [-h|--help] [--window-id <window-id>] (horizontal|vertical|opposite)

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
`split` command exist solely for compatibility with i3.
Unless you're hardcore i3 user who knows what they are doing, it's recommended to use `join-with`

*If the parent of focused window contains more than one child*, then the command

. Creates a new tiling container
. Replaces the focused window with the container
. Puts the focused window into the container as its the only child

The argument configures orientation of the newly created container.
`opposite` means opposite orientation compared to the parent container.

*If the parent of the focused window contains only a single child* (the window itself), then `split` command changes the orientation of the parent container

IMPORTANT: `split` command has no effect if `enable-normalization-flatten-containers` is turned on.
Consider using `join-with` if you want to keep `enable-normalization-flatten-containers` enabled

// =========================================================== Options
include::util/conditional-options-header.adoc[]

-h, --help:: Print help

--window-id <window-id>::
include::./util/window-id-flag-desc.adoc[]

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
