= AeroSpace Goodies
include::util/site-attributes.adoc[]

include::util/header.adoc[]

Do you have a cool automatization, AeroSpace integration, or workflow?
Feel free to open an issue or pull request to add it to this list!
The source code of the page can be found in the `./docs` directory.

[#move-by-dragging-any-part-of-the-window]
== Move windows by dragging any part of the window

[source,bash]
----
defaults write -g NSWindowShouldDragOnGesture -bool true
----

Now, you can move windows by holding `ctrl + cmd` and dragging any part of the window (not necessarily the window title)

[#highlight-focused-windows-with-colored-borders]
== Highlight focused windows with colored borders

To highlight the focused window with colored border you can use link:https://github.com/FelixKratz/JankyBorders[JankyBorders].

You can also use `after-startup-command` to start JankyBorders together with AeroSpace

[source,toml]
----
after-startup-command = [
    # JankyBorders has a built-in detection of already running process,
    # so it won't be run twice on AeroSpace restart
    'exec-and-forget borders active_color=0xffe1e3e4 inactive_color=0xff494d64 width=5.0'
]
----

[#raycast-extension]
== AeroSpace Raycast extension

There is a third party Raycast extension for AeroSpace. https://www.raycast.com/limonkufu/aerospace

If you struggle remembering shortcuts, it's useful to search for commands until they become muscle memory.

[#disable-open-animations]
== Disable windows opening animations

Observable in Google Chrome

[source,bash]
----
defaults write -g NSAutomaticWindowAnimationsEnabled -bool false
----

[#use-trackpad-gestures-to-switch-workspaces]
== Use trackpad gestures to switch workspaces

The following commands focus next or previous workspaces on monitors where the mouse is located

[source,bash]
----
aerospace workspace "$(aerospace list-workspaces --monitor mouse --visible)" && aerospace workspace next

aerospace workspace "$(aerospace list-workspaces --monitor mouse --visible)" && aerospace workspace prev
----

Use the software of your choice to assign trackpad gestures to the respective commands.
Here are a few third party options in alphabetical order:

* https://github.com/acsandmann/aerospace-swipe
* link:https://folivora.ai/[BetterTouchTool] can assign arbitrary commands to trackpad gestures.
Beware that you might need to specify full path to aerospace executable https://community.folivora.ai/t/how-to-execute-terminal-command-to-switch-workspaces-with-aerospace/35914
* https://github.com/MediosZ/SwipeAeroSpace

CAUTION: Make sure that you trust project authors before running the distributed binaries.
Alternatively, inspect the source code, and build it yourself.

[#show-aerospace-workspaces-in-sketchybar]
== Show AeroSpace workspaces in Sketchybar

You can integrate AeroSpace workspace indicators with link:https://github.com/FelixKratz/SketchyBar[Sketchybar].
Use these snippets as a starting point.

.~/.aerospace.toml
[source,toml]
----
# Run Sketchybar together with AeroSpace
# sketchbar has a built-in detection of already running process,
# so it won't be run twice on AeroSpace restart
after-startup-command = ['exec-and-forget sketchybar']

# Notify Sketchybar about workspace change
exec-on-workspace-change = ['/bin/bash', '-c',
    'sketchybar --trigger aerospace_workspace_change FOCUSED_WORKSPACE=$AEROSPACE_FOCUSED_WORKSPACE'
]
----

.~/.config/sketchybar/sketchybarrc
[source,bash]
----
sketchybar --add event aerospace_workspace_change

for sid in $(aerospace list-workspaces --all); do
    sketchybar --add item space.$sid left \
        --subscribe space.$sid aerospace_workspace_change \
        --set space.$sid \
        background.color=0x44ffffff \
        background.corner_radius=5 \
        background.height=20 \
        background.drawing=off \
        label="$sid" \
        click_script="aerospace workspace $sid" \
        script="$CONFIG_DIR/plugins/aerospace.sh $sid"
done
----

.~/.config/sketchybar/plugins/aerospace.sh
[source,bash]
----
#!/usr/bin/env bash

# make sure it's executable with:
# chmod +x ~/.config/sketchybar/plugins/aerospace.sh

if [ "$1" = "$FOCUSED_WORKSPACE" ]; then
    sketchybar --set $NAME background.drawing=on
else
    sketchybar --set $NAME background.drawing=off
fi
----

[#open-a-new-window-with-applescript]
== Open a new window with AppleScript

Invoking Safari/Terminal with a command the obvious way (`exec-and-forget open -a Safari`) results in an outcome that is probably not the intended one.
Namely, that any workspace already containing an instance of Safari/Terminal is brought in focus.

Opening *a new window* of a program that can supports multiple windows (such as Safari or Terminal.app) can be accomplished with an AppleScript inlined in `aerospace.toml` as follows:

- Safari
+
[source,toml]
----
ctrl-g = '''exec-and-forget osascript -e '
tell application "Safari"
    make new document at end of documents
    activate
end tell'
'''
----
- Terminal
+
[source,toml]
----
ctrl-g = '''exec-and-forget osascript -e '
tell application "Terminal"
    do script
    activate
end tell'
'''
----

[#disable-hide-app]
== Disable annoying and useless "hide application" shortcut

If `automatically-unhide-macos-hidden-apps` isn't enough, you can disable `cmd-h` altogether (which will make this hotkey unavailable for apps that might use it for other purposes)

.~/.aerospace.toml
[source,toml]
----
[mode.main.binding]
    cmd-h = [] # Disable "hide application"
    cmd-alt-h = [] # Disable "hide others"
----

[#screenshoot-shortcut]
== Take screenshots to clipboard using keyboard shortcut

You can configure a custom shortcut take a screenshot.
`screencapture` is a built-in macOS command.

.~/.aerospace.toml
[source,toml]
----
alt-shift-s = 'exec-and-forget screencapture -i -c'
----

[#i3-like-config]
== i3 like config

link:config-examples/i3-like-config-example.toml[Download i3-like-config.toml]

[source,toml,subs="macros+,specialchars+"]
----
include::config-examples/i3-like-config-example.toml[]
----

[#popular-apps-ids]
== List of popular and built-in applications IDs

The list is useful to compose custom xref:guide.adoc#on-window-detected-callback[on-window-detected callback].

[cols="1,3"]
|===
|Application name|Application ID

|1Password|`com.1password.1password`
|Activity Monitor|`com.apple.ActivityMonitor`
|AirPort Utility|`com.apple.airport.airportutility`
|Alacritty|`org.alacritty`
|Android Studio|`com.google.android.studio`
|App Store|`com.apple.AppStore`
|AppCode|`com.jetbrains.AppCode`
|Arc Browser|`company.thebrowser.Browser`
|Audio MIDI Setup|`com.apple.audio.AudioMIDISetup`
|Automator|`com.apple.Automator`
|Battle.net|`net.battle.app`
|Books|`com.apple.iBooksX`
|Brave|`com.brave.Browser`
|Calculator|`com.apple.calculator`
|Calendar|`com.apple.iCal`
|Chess|`com.apple.Chess`
|CLion|`com.jetbrains.CLion`
|Clock|`com.apple.clock`
|ColorSync Utility|`com.apple.ColorSyncUtility`
|Console|`com.apple.Console`
|Contacts|`com.apple.AddressBook`
|Dictionary|`com.apple.Dictionary`
|Disk Utility|`com.apple.DiskUtility`
|Docker|`com.docker.docker`
|FaceTime|`com.apple.FaceTime`
|Figma|`com.figma.Desktop`
|Find My|`com.apple.findmy`
|Finder|`com.apple.finder`
|Firefox|`org.mozilla.firefox`
|Freeform|`com.apple.freeform`
|Ghostty|`com.mitchellh.ghostty`
|GIMP|`org.gimp.gimp-2.10`
|Google Chrome|`com.google.Chrome`
|Grapher|`com.apple.grapher`
|Home|`com.apple.Home`
|iMovie|`com.apple.iMovieApp`
|Inkscape|`org.inkscape.Inkscape`
|IntelliJ IDEA Community|`com.jetbrains.intellij.ce`
|IntelliJ IDEA Ultimate|`com.jetbrains.intellij`
|iTerm2|`com.googlecode.iterm2`
|Karabiner-Elements|`org.pqrs.Karabiner-Elements.Settings`
|kdenlive|`org.kde.Kdenlive`
|Keychain Access|`com.apple.keychainaccess`
|Keynote|`com.apple.iWork.Keynote`
|Kitty|`net.kovidgoyal.kitty`
|Mail|`com.apple.mail`
|Maps|`com.apple.Maps`
|Marta|`org.yanex.marta`
|Messages|`com.apple.MobileSMS`
|Music|`com.apple.Music`
|Notes|`com.apple.Notes`
|Obsidian|`md.obsidian`
|Pages|`com.apple.iWork.Pages`
|Photo Booth|`com.apple.PhotoBooth`
|Photos|`com.apple.Photos`
|Podcasts|`com.apple.podcasts`
|Preview|`com.apple.Preview`
|PyCharm Community|`com.jetbrains.pycharm.ce`
|PyCharm Professional|`com.jetbrains.pycharm`
|QuickTime Player|`com.apple.QuickTimePlayerX`
|Reminders|`com.apple.reminders`
|Safari|`com.apple.Safari`
|Shortcuts|`com.apple.shortcuts`
|Slack|`com.tinyspeck.slackmacgap`
|Spotify|`com.spotify.client`
|Steam|`com.valvesoftware.steam`
|Stocks|`com.apple.stocks`
|Sublime Merge|`com.sublimemerge`
|Sublime Text|`com.sublimetext.4`
|System Settings|`com.apple.systempreferences`
|Telegram|`com.tdesktop.Telegram`
|Terminal|`com.apple.Terminal`
|TextEdit|`com.apple.TextEdit`
|Thunderbird|`org.mozilla.thunderbird`
|Time Machine|`com.apple.backup.launcher`
|Todoist|`com.todoist.mac.Todoist`
|Tor Browser|`org.torproject.torbrowser`
|Transmission|`org.m0k.transmission`
|TV|`com.apple.TV`
|Visual Studio Code|`com.microsoft.VSCode`
|VLC|`org.videolan.vlc`
|VoiceMemos|`com.apple.VoiceMemos`
|VoiceOver Utility|`com.apple.VoiceOverUtility`
|Weather|`com.apple.weather`
|WezTerm|`com.github.wez.wezterm`
|Xcode|`com.apple.dt.Xcode`
|Zen Browser|`app.zen-browser.zen`

|===
