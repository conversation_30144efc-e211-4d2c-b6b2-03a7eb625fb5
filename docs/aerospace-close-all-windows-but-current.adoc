= aerospace-close-all-windows-but-current(1)
include::util/man-attributes.adoc[]
:manname: aerospace-close-all-windows-but-current
// tag::purpose[]
:manpurpose: On the focused workspace, close all windows but current
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace close-all-windows-but-current [-h|--help] [--quit-if-last-window]

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

// =========================================================== Options
include::util/conditional-options-header.adoc[]

-h, --help:: Print help
--quit-if-last-window:: Quit the apps instead of closing them if it's their last window

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
