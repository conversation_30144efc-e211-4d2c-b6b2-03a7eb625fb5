= aerospace-resize(1)
include::util/man-attributes.adoc[]
:manname: aerospace-resize
// tag::purpose[]
:manpurpose: Resize the focused window
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace resize [-h|--help] [--window-id <window-id>] (smart|smart-opposite|width|height) [+|-]<number>

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

The dimension to resize is chosen by the first argument

* `width` changes width
* `height` changes height
* `smart` changes width if the parent has horizontal orientation, and
it changes height if the parent has vertical orientation
* `smart-opposite` does resizes the opposite axis of smart

Second argument controls how much the size changes

* If the `<number>` is prefixed with `+` then the dimension is increased
* If the `<number>` is prefixed with `-` then the dimension is decreased
* If the `<number>` is prefixed with neither `+` nor `-` then the command changes the absolute value of the dimension

// =========================================================== Options
include::./util/conditional-options-header.adoc[]

-h, --help:: Print help

--window-id <window-id>::
include::./util/window-id-flag-desc.adoc[]

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
