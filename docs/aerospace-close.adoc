= aerospace-close(1)
include::util/man-attributes.adoc[]
:manname: aerospace-close
// tag::purpose[]
:manpurpose: Close the focused window
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace close [-h|--help] [--quit-if-last-window] [--window-id <window-id>]

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

Normally, you don’t need to use this command, because macOS offers its own `cmd+w` binding.
You might want to use the command from CLI for scripting purposes

// =========================================================== Options
include::./util/conditional-options-header.adoc[]

-h, --help:: Print help
--quit-if-last-window:: Quit the app instead of closing if it's the last window of the app

--window-id <window-id>::
include::./util/window-id-flag-desc.adoc[]

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
