= aerospace-exec-and-forget(1)
include::util/man-attributes.adoc[]
:manname: aerospace-exec-and-forget
// tag::purpose[]
:manpurpose: Run /bin/bash -c '<bash-script>'
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace exec-and-forget <bash-script>

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
Run `/bin/bash -c '<bash-script>'`, and don't wait for the command termination.
Stdout, stderr and exit code are ignored.

For example, you can use this command to launch applications:

[source,toml]
----
alt-enter = 'exec-and-forget open -n /System/Applications/Utilities/Terminal.app'
----

`<bash-script>` is passed "as is" to bash without any transformations and escaping. `<bash-script>` is treated as suffix of the TOML string, it's not even an argument in classic CLI sense

* The command is available in config
* The command is *NOT* available in CLI

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
