= aerospace-macos-native-fullscreen(1)
include::util/man-attributes.adoc[]
:manname: aerospace-macos-native-fullscreen
// tag::purpose[]
:manpurpose: Toggle macOS fullscreen for the focused window
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace macos-native-fullscreen [-h|--help] [--window-id <window-id>]
aerospace macos-native-fullscreen [-h|--help] [--window-id <window-id>] [--fail-if-noop] on
aerospace macos-native-fullscreen [-h|--help] [--window-id <window-id>] [--fail-if-noop] off

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

// =========================================================== Options
include::./util/conditional-options-header.adoc[]

-h, --help:: Print help
--fail-if-noop:: Exit with non-zero exit code if already fullscreen or already not fullscreen

--window-id <window-id>::
include::./util/window-id-flag-desc.adoc[]

// =========================================================== Arguments
include::./util/conditional-arguments-header.adoc[]

on, off::
`on` means enter fullscreen mode.
`off` means exit fullscreen mode.
Toggle between the two if not specified

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
