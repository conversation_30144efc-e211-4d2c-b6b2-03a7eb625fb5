= aerospace-balance-sizes(1)
include::util/man-attributes.adoc[]
:manname: aerospace-balance-sizes
// tag::purpose[]
:manpurpose: Balance sizes of all windows in the current workspace
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace balance-sizes [-h|--help] [--workspace <workspace>]

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

// =========================================================== Options
include::./util/conditional-options-header.adoc[]

-h, --help:: Print help

--workspace <workspace>::
include::./util/workspace-flag-desc.adoc[]

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
