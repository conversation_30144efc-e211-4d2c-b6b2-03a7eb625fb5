= aerospace-enable(1)
include::util/man-attributes.adoc[]
:manname: aerospace-enable
// tag::purpose[]
:manpurpose: Temporarily disable window management
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace enable [-h|--help] toggle
aerospace enable [-h|--help] on [--fail-if-noop]
aerospace enable [-h|--help] off [--fail-if-noop]

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

When you disable AeroSpace, windows from currently invisible workspaces will be placed to the visible area of the screen

Key events are not intercepted when AeroSpace is disabled

// =========================================================== Options
include::util/conditional-options-header.adoc[]

-h, --help:: Print help
--fail-if-noop:: Exit with non-zero exit code if already in the requested mode

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
