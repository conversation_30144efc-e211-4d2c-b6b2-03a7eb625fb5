= aerospace-workspace(1)
include::util/man-attributes.adoc[]
:manname: aerospace-workspace
// tag::purpose[]
:manpurpose: Focus the specified workspace
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace workspace [-h|--help] [--auto-back-and-forth] [--fail-if-noop] <workspace-name>
aerospace workspace [-h|--help] [--wrap-around] (next|prev)

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
*1. <workspace-name> syntax*

{manpurpose}

*2. (next|prev) syntax*

Focuses next or previous workspace in *the list*.

* If stdin is not TTY and stdin contains non whitespace characters then *the list* is taken from stdin
* Otherwise, *the list* is defined as all workspaces on focused monitor in alphabetical order

// =========================================================== Options
include::util/conditional-options-header.adoc[]

-h, --help:: Print help
--wrap-around:: Make it possible to jump between first and last workspaces using `(next|prev)`

--auto-back-and-forth::
Automatic `back-and-forth` when switching to already focused workspace.
Incompatible with `--fail-if-noop`

--fail-if-noop::
Exit with non-zero exit code if switch to the already focused workspace
Incompatible with `--auto-back-and-forth`

// =========================================================== Examples
include::util/conditional-examples-header.adoc[]

* Go to the next non empty workspace on the focused monitor: +
`aerospace list-workspaces --monitor focused --empty no | aerospace workspace next`

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
