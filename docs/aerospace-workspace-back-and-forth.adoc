= aerospace-workspace-back-and-forth(1)
include::util/man-attributes.adoc[]
// tag::purpose[]
:manpurpose: Switch between the focused workspace and previously focused workspace back and forth
// end::purpose[]
:manname: aerospace-workspace-back-and-forth

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace workspace-back-and-forth [-h|--help]

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

Unlike `focus-back-and-forth`, `workspace-back-and-forth` always succeeds.
Because unlike windows, workspaces can not be "closed".
Workspaces are name-addressable objects.
They are created and destroyed on the fly.

Also see: <<focus-back-and-forth, focus-back-and-forth>>
// end::body[]

// =========================================================== Options
include::util/conditional-options-header.adoc[]

-h, --help:: Print help

// =========================================================== Footer
include::util/man-footer.adoc[]
