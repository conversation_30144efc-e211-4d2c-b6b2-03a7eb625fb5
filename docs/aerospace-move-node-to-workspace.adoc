= aerospace-move-node-to-workspace(1)
include::util/man-attributes.adoc[]
:manname: aerospace-move-node-to-workspace
// tag::purpose[]
:manpurpose: Move the focused window to the specified workspace
// end::purpose[]

// =========================================================== Synopsis
== Synopsis
[verse]
// tag::synopsis[]
aerospace move-node-to-workspace [-h|--help] [--focus-follows-window] [--wrap-around]
                                 (next|prev)
aerospace move-node-to-workspace [-h|--help] [--focus-follows-window] [--fail-if-noop]
                                 [--window-id <window-id>] <workspace-name>

// end::synopsis[]

// =========================================================== Description
== Description

// tag::body[]
{manpurpose}

`(next|prev)` is identical to `workspace (next|prev)`

// =========================================================== Options
include::./util/conditional-options-header.adoc[]

-h, --help:: Print help
--wrap-around:: Make it possible to jump between first and last workspaces using (next|prev)
--fail-if-noop:: Exit with non-zero code if move window to workspace it already belongs to

--focus-follows-window::
Make sure that the window in question receives focus after moving.
This flag is a shortcut for manually running `aerospace-workspace`/`aerospace-focus` after `move-node-to-workspace` successful execution.

--window-id <window-id>::
include::./util/window-id-flag-desc.adoc[]

// =========================================================== Arguments
include::./util/conditional-arguments-header.adoc[]

(next|prev):: Move window to next or prev workspace
<workspace-name>:: Specifies workspace name where to move window to

// end::body[]

// =========================================================== Footer
include::util/man-footer.adoc[]
