// FILE IS GENERATED FROM docs/aerospace-*.adoc files
// TO REGENERATE THE FILE RUN generate.sh

let subcommandDescriptions = [
    ["  balance-sizes", "Balance sizes of all windows in the current workspace"],
    ["  close-all-windows-but-current", "On the focused workspace, close all windows but current"],
    ["  close", "Close the focused window"],
    ["  config", "Query AeroSpace config options"],
    ["  debug-windows", "Interactive command to record Accessibility API debug information to create bug reports"],
    ["  enable", "Temporarily disable window management"],
    ["  flatten-workspace-tree", "Flatten the tree of the focused workspace"],
    ["  focus-back-and-forth", "Switch between the current and previously focused elements back and forth"],
    ["  focus-monitor", "Focus monitor by relative direction, by order, or by pattern"],
    ["  focus", "Set focus to the nearest window in the given direction."],
    ["  fullscreen", "Toggle the fullscreen mode for the focused window"],
    ["  join-with", "Put the focused window and the nearest node in the specified direction under a common parent container"],
    ["  layout", "Change layout of the focused window to the given layout"],
    ["  list-apps", "Print the list of running applications that appears in the Dock and may have a user interface"],
    ["  list-exec-env-vars", "List environment variables that exec-* commands and callbacks are run with"],
    ["  list-modes", "Print a list of modes currently specified in the configuration"],
    ["  list-monitors", "Print monitors that satisfy conditions"],
    ["  list-windows", "Print windows that satisfy conditions"],
    ["  list-workspaces", "Print workspaces that satisfy conditions"],
    ["  macos-native-fullscreen", "Toggle macOS fullscreen for the focused window"],
    ["  macos-native-minimize", "Minimize focused window"],
    ["  mode", "Activate the specified binding mode"],
    ["  move-mouse", "Move mouse to the requested position"],
    ["  move-node-to-monitor", "Move window to monitor targeted by relative direction, by order, or by pattern"],
    ["  move-node-to-workspace", "Move the focused window to the specified workspace"],
    ["  move-workspace-to-monitor", "Move workspace to monitor targeted by relative direction, by order, or by pattern."],
    ["  move", "Move the focused window in the given direction"],
    ["  reload-config", "Reload currently active config"],
    ["  resize", "Resize the focused window"],
    ["  split", "Split focused window"],
    ["  summon-workspace", "Move the requested workspace to the focused monitor."],
    ["  trigger-binding", "Trigger AeroSpace binding as if it was pressed by user"],
    ["  volume", "Manipulate volume"],
    ["  workspace-back-and-forth", "Switch between the focused workspace and previously focused workspace back and forth"],
    ["  workspace", "Focus the specified workspace"],
]
